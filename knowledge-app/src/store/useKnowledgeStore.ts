import { create } from 'zustand'
import { KnowledgePoint, KnowledgeDependency, UserProgress, KnowledgeGraph } from '@/types'

interface KnowledgeStore {
  // State
  knowledgePoints: KnowledgePoint[]
  dependencies: KnowledgeDependency[]
  userProgress: UserProgress[]
  selectedKnowledgePoint: KnowledgePoint | null
  isLoading: boolean
  error: string | null

  // Actions
  setKnowledgePoints: (points: KnowledgePoint[]) => void
  addKnowledgePoint: (point: KnowledgePoint) => void
  updateKnowledgePoint: (id: string, updates: Partial<KnowledgePoint>) => void
  deleteKnowledgePoint: (id: string) => void
  
  setDependencies: (deps: KnowledgeDependency[]) => void
  addDependency: (dep: KnowledgeDependency) => void
  removeDependency: (prerequisiteId: string, dependentId: string) => void
  
  setUserProgress: (progress: UserProgress[]) => void
  updateUserProgress: (knowledgePointId: string, updates: Partial<UserProgress>) => void
  
  setSelectedKnowledgePoint: (point: KnowledgePoint | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Computed
  getKnowledgeGraph: () => KnowledgeGraph
  getPrerequisites: (knowledgePointId: string) => KnowledgePoint[]
  getDependents: (knowledgePointId: string) => KnowledgePoint[]
  getUserMasteryLevel: (knowledgePointId: string) => number
  getRecommendations: () => KnowledgePoint[]
}

export const useKnowledgeStore = create<KnowledgeStore>((set, get) => ({
  // Initial state
  knowledgePoints: [],
  dependencies: [],
  userProgress: [],
  selectedKnowledgePoint: null,
  isLoading: false,
  error: null,

  // Actions
  setKnowledgePoints: (points) => set({ knowledgePoints: points }),
  
  addKnowledgePoint: (point) => set((state) => ({
    knowledgePoints: [...state.knowledgePoints, point]
  })),
  
  updateKnowledgePoint: (id, updates) => set((state) => ({
    knowledgePoints: state.knowledgePoints.map(point =>
      point.id === id ? { ...point, ...updates } : point
    )
  })),
  
  deleteKnowledgePoint: (id) => set((state) => ({
    knowledgePoints: state.knowledgePoints.filter(point => point.id !== id),
    dependencies: state.dependencies.filter(dep => 
      dep.prerequisite_id !== id && dep.dependent_id !== id
    )
  })),
  
  setDependencies: (deps) => set({ dependencies: deps }),
  
  addDependency: (dep) => set((state) => ({
    dependencies: [...state.dependencies, dep]
  })),
  
  removeDependency: (prerequisiteId, dependentId) => set((state) => ({
    dependencies: state.dependencies.filter(dep =>
      !(dep.prerequisite_id === prerequisiteId && dep.dependent_id === dependentId)
    )
  })),
  
  setUserProgress: (progress) => set({ userProgress: progress }),
  
  updateUserProgress: (knowledgePointId, updates) => set((state) => ({
    userProgress: state.userProgress.map(progress =>
      progress.knowledge_point_id === knowledgePointId 
        ? { ...progress, ...updates }
        : progress
    )
  })),
  
  setSelectedKnowledgePoint: (point) => set({ selectedKnowledgePoint: point }),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),

  // Computed functions
  getKnowledgeGraph: () => {
    const { knowledgePoints, dependencies, userProgress } = get()
    
    const nodes = knowledgePoints.map(point => {
      const progress = userProgress.find(p => p.knowledge_point_id === point.id)
      return {
        id: point.id,
        name: point.name,
        group: point.difficulty_level,
        mastery_level: progress?.mastery_level || 0,
        difficulty_level: point.difficulty_level
      }
    })
    
    const links = dependencies.map(dep => ({
      source: dep.prerequisite_id,
      target: dep.dependent_id
    }))
    
    return { nodes, links }
  },
  
  getPrerequisites: (knowledgePointId) => {
    const { knowledgePoints, dependencies } = get()
    const prerequisiteIds = dependencies
      .filter(dep => dep.dependent_id === knowledgePointId)
      .map(dep => dep.prerequisite_id)
    
    return knowledgePoints.filter(point => prerequisiteIds.includes(point.id))
  },
  
  getDependents: (knowledgePointId) => {
    const { knowledgePoints, dependencies } = get()
    const dependentIds = dependencies
      .filter(dep => dep.prerequisite_id === knowledgePointId)
      .map(dep => dep.dependent_id)
    
    return knowledgePoints.filter(point => dependentIds.includes(point.id))
  },
  
  getUserMasteryLevel: (knowledgePointId) => {
    const { userProgress } = get()
    const progress = userProgress.find(p => p.knowledge_point_id === knowledgePointId)
    return progress?.mastery_level || 0
  },
  
  getRecommendations: () => {
    const { knowledgePoints, dependencies, userProgress } = get()
    
    // Simple recommendation algorithm: find knowledge points where all prerequisites are mastered
    return knowledgePoints.filter(point => {
      const progress = userProgress.find(p => p.knowledge_point_id === point.id)
      const currentMastery = progress?.mastery_level || 0
      
      // Skip if already mastered (>= 80%)
      if (currentMastery >= 80) return false
      
      // Check if all prerequisites are mastered
      const prerequisites = dependencies
        .filter(dep => dep.dependent_id === point.id)
        .map(dep => dep.prerequisite_id)
      
      const allPrerequisitesMastered = prerequisites.every(prereqId => {
        const prereqProgress = userProgress.find(p => p.knowledge_point_id === prereqId)
        return (prereqProgress?.mastery_level || 0) >= 80
      })
      
      return allPrerequisitesMastered
    })
  }
}))
