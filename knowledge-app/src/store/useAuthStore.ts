import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AuthUser, AuthState } from '@/lib/auth'
import { Session } from '@supabase/supabase-js'

interface AuthStore extends AuthState {
  // Actions
  setUser: (user: AuthUser | null) => void
  setSession: (session: Session | null) => void
  setLoading: (loading: boolean) => void
  clearAuth: () => void
  
  // Computed
  isAuthenticated: boolean
  isLoading: boolean
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      session: null,
      loading: true,

      // Actions
      setUser: (user) => {
        console.log('AuthStore.setUser 被调用:', user)
        set({ user })
      },
      setSession: (session) => {
        console.log('AuthStore.setSession 被调用:', session)
        set({ session })
      },
      setLoading: (loading) => set({ loading }),
      clearAuth: () => set({ user: null, session: null, loading: false }),

      // Computed properties
      get isAuthenticated() {
        const state = get()
        return !!state.user
      },
      
      get isLoading() {
        return get().loading
      }
    }),
    {
      name: 'auth-storage',
      // 只持久化用户信息，不持久化session（安全考虑）
      partialize: (state) => ({ 
        user: state.user,
        loading: false // 重新加载时不保持loading状态
      })
    }
  )
)
