export interface KnowledgePoint {
  id: string
  name: string
  description?: string
  difficulty_level: number
  tags: string[]
  created_at: string
  updated_at: string
  user_id: string
  prerequisites?: KnowledgePoint[]
  dependents?: KnowledgePoint[]
}

export interface KnowledgeDependency {
  id: string
  prerequisite_id: string
  dependent_id: string
  created_at: string
}

export interface UserProgress {
  id: string
  user_id: string
  knowledge_point_id: string
  mastery_level: number // 0-100
  notes?: string
  last_reviewed?: string
  test_scores: number[]
  created_at: string
  updated_at: string
}

export interface GraphNode {
  id: string
  name: string
  group: number
  mastery_level?: number
  difficulty_level: number
}

export interface GraphLink {
  source: string
  target: string
}

export interface KnowledgeGraph {
  nodes: GraphNode[]
  links: GraphLink[]
}

export interface LearningRecommendation {
  knowledge_point: KnowledgePoint
  reason: string
  priority: number
  estimated_time: number // in minutes
}

export interface TestQuestion {
  id: string
  knowledge_point_id: string
  question: string
  type: 'multiple_choice' | 'short_answer' | 'essay'
  options?: string[]
  correct_answer?: string
  difficulty: number
}

export interface LearningSession {
  id: string
  user_id: string
  knowledge_point_id: string
  start_time: string
  end_time?: string
  notes?: string
  test_results?: {
    score: number
    questions_answered: number
    time_spent: number
  }
}
