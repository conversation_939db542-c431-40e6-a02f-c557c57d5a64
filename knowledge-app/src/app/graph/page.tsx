'use client'

import React, { useState, useEffect } from 'react'
import { KnowledgeGraphVisualization } from '@/components/graph/KnowledgeGraphVisualization'
import { AuthGuard } from '@/components/auth/AuthGuard'
import { useKnowledgeGraphData } from '@/hooks/useKnowledgeGraph'
import { GraphNode, GraphLink } from '@/types'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Brain, ArrowLeft, Plus, Link as LinkIcon, Target, BookOpen, Zap } from 'lucide-react'
import Link from 'next/link'

export default function GraphPage() {
  const { knowledgeGraph, knowledgePoints, isLoading, error } = useKnowledgeGraphData()
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null)
  const [hoveredNode, setHoveredNode] = useState<GraphNode | null>(null)
  const [graphDimensions, setGraphDimensions] = useState({ width: 800, height: 600 })

  // 响应式调整图谱尺寸
  useEffect(() => {
    const updateDimensions = () => {
      const container = document.getElementById('graph-container')
      if (container) {
        const rect = container.getBoundingClientRect()
        setGraphDimensions({
          width: Math.max(800, rect.width - 32), // 减去padding
          height: Math.max(600, window.innerHeight - 300)
        })
      }
    }

    updateDimensions()
    window.addEventListener('resize', updateDimensions)
    return () => window.removeEventListener('resize', updateDimensions)
  }, [])

  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node)
  }

  const handleNodeHover = (node: GraphNode | null) => {
    setHoveredNode(node)
  }

  const handleLinkClick = (link: GraphLink) => {
    console.log('Link clicked:', link)
    // 可以在这里添加链接点击的处理逻辑
  }

  // 获取选中节点的详细信息
  const selectedKnowledgePoint = selectedNode 
    ? knowledgePoints.find(point => point.id === selectedNode.id)
    : null

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="text-center py-8">
            <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              重试
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/knowledge">
                <Button variant="ghost" size="sm" className="mr-4">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回知识管理
                </Button>
              </Link>
              <div className="flex items-center">
                <Brain className="h-8 w-8 text-indigo-600 mr-3" />
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    知识图谱可视化
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400 mt-1">
                    探索知识点之间的依赖关系和学习路径
                  </p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Link href="/adaptive">
                <Button variant="outline" className="text-indigo-600 border-indigo-600 hover:bg-indigo-50">
                  <Zap className="h-4 w-4 mr-2" />
                  AI自适应学习
                </Button>
              </Link>
              <Link href="/knowledge">
                <Button variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  添加知识点
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">知识点总数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {knowledgeGraph.nodes.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <LinkIcon className="h-8 w-8 text-green-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">依赖关系</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {knowledgeGraph.links.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-purple-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">已掌握</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {knowledgeGraph.nodes.filter(node => (node.mastery_level || 0) >= 80).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Brain className="h-8 w-8 text-orange-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">平均难度</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {knowledgeGraph.nodes.length > 0 
                      ? (knowledgeGraph.nodes.reduce((sum, node) => sum + node.difficulty_level, 0) / knowledgeGraph.nodes.length).toFixed(1)
                      : '0'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 图谱可视化 */}
          <div className="lg:col-span-3" id="graph-container">
            {isLoading ? (
              <Card>
                <CardContent className="flex items-center justify-center h-96">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-2"></div>
                    <p className="text-gray-600 dark:text-gray-400">加载知识图谱...</p>
                  </div>
                </CardContent>
              </Card>
            ) : knowledgeGraph.nodes.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Brain className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    还没有知识点
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    创建一些知识点来开始构建你的知识图谱
                  </p>
                  <Link href="/knowledge">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      创建第一个知识点
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ) : (
              <KnowledgeGraphVisualization
                graphData={knowledgeGraph}
                onNodeClick={handleNodeClick}
                onNodeHover={handleNodeHover}
                onLinkClick={handleLinkClick}
                width={graphDimensions.width}
                height={graphDimensions.height}
              />
            )}
          </div>

          {/* 侧边栏 - 节点详情 */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {selectedNode ? '知识点详情' : '选择知识点'}
                </h3>
              </CardHeader>
              <CardContent>
                {selectedNode && selectedKnowledgePoint ? (
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                        {selectedKnowledgePoint.name}
                      </h4>
                      {selectedKnowledgePoint.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                          {selectedKnowledgePoint.description}
                        </p>
                      )}
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        难度级别
                      </p>
                      <div className="flex items-center gap-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                          <div 
                            className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${(selectedKnowledgePoint.difficulty_level / 10) * 100}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {selectedKnowledgePoint.difficulty_level}/10
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        掌握程度
                      </p>
                      <div className="flex items-center gap-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                          <div 
                            className="bg-green-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${selectedNode.mastery_level || 0}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {selectedNode.mastery_level || 0}%
                        </span>
                      </div>
                    </div>
                    
                    {selectedKnowledgePoint.tags && selectedKnowledgePoint.tags.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          标签
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {selectedKnowledgePoint.tags.map((tag, index) => (
                            <span 
                              key={index}
                              className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                      <Link href={`/knowledge?edit=${selectedKnowledgePoint.id}`}>
                        <Button className="w-full" size="sm">
                          编辑知识点
                        </Button>
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Brain className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-600 dark:text-gray-400 text-sm">
                      点击图谱中的节点查看详细信息
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          </div>
        </div>
      </div>
    </AuthGuard>
  )
}
