'use client'

import React, { useState, useRef, useEffect } from 'react'
import { AuthGuard } from '@/components/auth/AuthGuard'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import { Send, Brain, Sparkles, BookOpen, Code, Lightbulb, Target } from 'lucide-react'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

const KNOWLEDGE_TOPICS = [
  { id: 1, title: 'Python编程基础', icon: Code, color: 'bg-blue-500' },
  { id: 2, title: '机器学习入门', icon: Brain, color: 'bg-green-500' },
  { id: 3, title: 'React开发实战', icon: Sparkles, color: 'bg-purple-500' },
  { id: 4, title: '数据结构与算法', icon: Target, color: 'bg-red-500' },
  { id: 5, title: '深度学习原理', icon: Lightbulb, color: 'bg-yellow-500' },
  { id: 6, title: 'Web开发全栈', icon: BookOpen, color: 'bg-indigo-500' },
  { id: 7, title: 'TypeScript进阶', icon: Code, color: 'bg-cyan-500' },
  { id: 8, title: '系统设计面试', icon: Target, color: 'bg-orange-500' },
]

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleTopicClick = (topic: string) => {
    setInputValue(`我想学习${topic}，请给我一个学习计划`)
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputValue,
          history: messages.slice(-10) // 只发送最近10条消息作为上下文
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get response')
      }

      const data = await response.json()

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.response,
        timestamp: new Date()
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '抱歉，我现在无法回应。请稍后再试。',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
        {/* 知识话题区域 */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
          <div className="container mx-auto">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Sparkles className="w-5 h-5 mr-2 text-indigo-600" />
              推荐学习话题
            </h2>
            <div className="flex flex-wrap gap-3">
              {KNOWLEDGE_TOPICS.map((topic) => {
                const IconComponent = topic.icon
                return (
                  <button
                    key={topic.id}
                    onClick={() => handleTopicClick(topic.title)}
                    className={`${topic.color} hover:opacity-90 text-white px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 hover:scale-105 flex items-center gap-2`}
                  >
                    <IconComponent className="w-4 h-4" />
                    {topic.title}
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        {/* 聊天消息区域 */}
        <div className="flex-1 container mx-auto px-4 py-6 max-w-4xl">
          <div className="space-y-4 mb-6">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <Brain className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">
                  开始您的学习对话
                </h3>
                <p className="text-gray-500 dark:text-gray-500">
                  选择上方的话题或直接输入您想学习的内容
                </p>
              </div>
            ) : (
              messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <Card className={`max-w-[80%] p-4 ${
                    message.role === 'user'
                      ? 'bg-indigo-600 text-white'
                      : 'bg-white dark:bg-gray-800'
                  }`}>
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">
                      {message.content}
                    </div>
                    <div className={`text-xs mt-2 ${
                      message.role === 'user' 
                        ? 'text-indigo-200' 
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                  </Card>
                </div>
              ))
            )}
            
            {isLoading && (
              <div className="flex justify-start">
                <Card className="bg-white dark:bg-gray-800 p-4">
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span className="text-sm text-gray-500">AI正在思考...</span>
                  </div>
                </Card>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* 输入区域 */}
        <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
          <div className="container mx-auto max-w-4xl">
            <div className="flex gap-3">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入您想学习的内容或问题..."
                disabled={isLoading}
                className="flex-1"
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
                className="px-6"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </AuthGuard>
  )
}
