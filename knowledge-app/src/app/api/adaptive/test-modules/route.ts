import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('开始测试模块导入...')
    
    // 测试基础LLM服务
    console.log('1. 测试LLM服务导入...')
    const { llmService } = await import('@/lib/llm/service')
    console.log('LLM服务导入成功')
    
    // 测试知识生成器
    console.log('2. 测试知识生成器导入...')
    const { knowledgeGenerator } = await import('@/lib/llm/knowledgeGenerator')
    console.log('知识生成器导入成功')
    
    // 测试依赖分析器
    console.log('3. 测试依赖分析器导入...')
    const { dependencyAnalyzer } = await import('@/lib/llm/dependencyAnalyzer')
    console.log('依赖分析器导入成功')
    
    // 测试路径规划器
    console.log('4. 测试路径规划器导入...')
    const { pathPlanner } = await import('@/lib/llm/pathPlanner')
    console.log('路径规划器导入成功')
    
    // 测试自适应学习服务
    console.log('5. 测试自适应学习服务导入...')
    const { adaptiveLearning } = await import('@/lib/llm/adaptiveLearning')
    console.log('自适应学习服务导入成功')
    
    // 测试知识生成功能
    console.log('6. 测试知识生成功能...')
    const knowledge = await knowledgeGenerator.generateKnowledge({
      topic: 'Python基础',
      userLevel: 'beginner',
      preferences: {
        includeExamples: true,
        includeExercises: true
      }
    })
    console.log('知识生成成功:', knowledge.title)

    return NextResponse.json({
      success: true,
      message: '所有模块测试通过',
      testResults: {
        llmService: '✓',
        knowledgeGenerator: '✓',
        dependencyAnalyzer: '✓',
        pathPlanner: '✓',
        adaptiveLearning: '✓',
        knowledgeGeneration: '✓'
      },
      generatedKnowledge: knowledge,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('模块测试失败:', error)
    
    return NextResponse.json(
      { 
        error: '模块测试失败',
        details: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}
