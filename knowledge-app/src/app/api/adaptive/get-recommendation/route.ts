import { NextRequest, NextResponse } from 'next/server'
import { adaptiveLearning } from '@/lib/llm/adaptiveLearning'

export async function POST(request: NextRequest) {
  try {
    const { sessionId } = await request.json()

    if (!sessionId) {
      return NextResponse.json(
        { error: '会话ID不能为空' },
        { status: 400 }
      )
    }

    // 获取学习建议
    const response = await adaptiveLearning.getLearningRecommendation(sessionId)

    return NextResponse.json({
      success: true,
      response,
      message: '已生成个性化学习建议',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('获取学习建议失败:', error)
    
    return NextResponse.json(
      { 
        error: '获取学习建议失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
