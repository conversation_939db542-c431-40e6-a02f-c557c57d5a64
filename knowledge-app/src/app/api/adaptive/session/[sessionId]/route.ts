import { NextRequest, NextResponse } from 'next/server'
import { adaptiveLearning } from '@/lib/llm/adaptiveLearning'

export async function GET(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const { sessionId } = params

    if (!sessionId) {
      return NextResponse.json(
        { error: '会话ID不能为空' },
        { status: 400 }
      )
    }

    // 获取学习会话
    const session = adaptiveLearning.getSession(sessionId)

    if (!session) {
      return NextResponse.json(
        { error: '学习会话不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      session,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('获取学习会话失败:', error)
    
    return NextResponse.json(
      { 
        error: '获取学习会话失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
