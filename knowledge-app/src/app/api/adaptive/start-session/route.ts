import { NextRequest, NextResponse } from 'next/server'
import { adaptiveLearning } from '@/lib/llm/adaptiveLearning'

export async function POST(request: NextRequest) {
  try {
    const { userId, targetTopic, userContext } = await request.json()

    if (!userId || !targetTopic) {
      return NextResponse.json(
        { error: '用户ID和学习主题不能为空' },
        { status: 400 }
      )
    }

    // 开始新的学习会话
    const session = await adaptiveLearning.startLearningSession(
      userId,
      targetTopic,
      userContext
    )

    return NextResponse.json({
      success: true,
      session,
      message: '学习会话已成功启动',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('启动学习会话失败:', error)
    
    return NextResponse.json(
      { 
        error: '启动学习会话失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
