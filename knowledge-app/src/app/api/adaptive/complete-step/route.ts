import { NextRequest, NextResponse } from 'next/server'
import { adaptiveLearning } from '@/lib/llm/adaptiveLearning'

export async function POST(request: NextRequest) {
  try {
    const { sessionId, stepId, masteryLevel, timeSpent, feedback } = await request.json()

    if (!sessionId || !stepId || masteryLevel === undefined) {
      return NextResponse.json(
        { error: '会话ID、步骤ID和掌握程度不能为空' },
        { status: 400 }
      )
    }

    // 完成学习步骤
    const response = await adaptiveLearning.completeStep(
      sessionId,
      stepId,
      masteryLevel,
      timeSpent || 30,
      feedback
    )

    return NextResponse.json({
      success: true,
      response,
      message: '学习步骤已完成',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('完成学习步骤失败:', error)
    
    return NextResponse.json(
      { 
        error: '完成学习步骤失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
