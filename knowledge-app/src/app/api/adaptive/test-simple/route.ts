import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('简单测试API被调用')
    
    const { userId, targetTopic } = await request.json()
    console.log('接收到参数:', { userId, targetTopic })

    if (!userId || !targetTopic) {
      return NextResponse.json(
        { error: '用户ID和学习主题不能为空' },
        { status: 400 }
      )
    }

    // 创建一个简单的模拟会话
    const mockSession = {
      id: `session_${Date.now()}`,
      userId,
      targetTopic,
      currentStep: 'initial_learning',
      learningHistory: [],
      knowledgeGraph: {},
      currentPath: null,
      blockedConcepts: [],
      sessionStartTime: new Date(),
      lastActivityTime: new Date(),
      status: 'active'
    }

    console.log('创建模拟会话成功:', mockSession.id)

    return NextResponse.json({
      success: true,
      session: mockSession,
      message: '简单测试会话已成功启动',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('简单测试API失败:', error)
    
    return NextResponse.json(
      { 
        error: '简单测试失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
