import { NextRequest, NextResponse } from 'next/server'
import { adaptiveLearning } from '@/lib/llm/adaptiveLearning'

export async function POST(request: NextRequest) {
  try {
    const { sessionId, difficulty } = await request.json()

    if (!sessionId || !difficulty) {
      return NextResponse.json(
        { error: '会话ID和困难描述不能为空' },
        { status: 400 }
      )
    }

    // 处理学习困难
    const response = await adaptiveLearning.handleLearningDifficulty(
      sessionId,
      difficulty
    )

    return NextResponse.json({
      success: true,
      response,
      message: '已分析学习困难并提供建议',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('处理学习困难失败:', error)
    
    return NextResponse.json(
      { 
        error: '处理学习困难失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
