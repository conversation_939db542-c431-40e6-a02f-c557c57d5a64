import { NextRequest, NextResponse } from 'next/server'

interface Message {
  role: 'user' | 'assistant'
  content: string
}

export async function POST(request: NextRequest) {
  try {
    const { message, history = [] } = await request.json()

    if (!message) {
      return NextResponse.json(
        { error: '消息内容不能为空' },
        { status: 400 }
      )
    }

    // 构建消息历史
    const messages: Message[] = [
      {
        role: 'system',
        content: `你是一个专业的学习助手和导师，名字叫"小智"。你的任务是帮助用户学习各种知识和技能。

请遵循以下原则：
1. 提供准确、实用的学习建议和知识解答
2. 根据用户的问题调整回答的深度和复杂度
3. 鼓励用户主动思考和实践
4. 提供具体的学习步骤和资源建议
5. 保持友好、耐心的语调
6. 如果用户问题不清楚，主动询问更多细节

回答要求：
- 结构清晰，使用适当的格式（如列表、步骤等）
- 包含实际例子和应用场景
- 提供进一步学习的方向
- 控制回答长度，避免过于冗长`
      },
      // 添加历史消息（最近10条）
      ...history.slice(-10).map((msg: any) => ({
        role: msg.role,
        content: msg.content
      })),
      {
        role: 'user',
        content: message
      }
    ]

    // 调用Groq API
    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama-3.1-70b-versatile', // 使用Groq的Llama模型
        messages: messages,
        max_tokens: 2000,
        temperature: 0.7,
        top_p: 1,
        stream: false
      }),
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('Groq API Error:', response.status, errorData)
      throw new Error(`Groq API请求失败: ${response.status}`)
    }

    const data = await response.json()
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('API响应格式错误')
    }

    const assistantResponse = data.choices[0].message.content

    return NextResponse.json({
      response: assistantResponse,
      usage: data.usage,
      model: data.model
    })

  } catch (error) {
    console.error('聊天API错误:', error)
    
    // 根据错误类型返回不同的错误信息
    if (error instanceof Error) {
      if (error.message.includes('API请求失败')) {
        return NextResponse.json(
          { error: 'AI服务暂时不可用，请稍后重试' },
          { status: 503 }
        )
      }
    }
    
    return NextResponse.json(
      { error: '处理请求时发生错误，请稍后重试' },
      { status: 500 }
    )
  }
}
