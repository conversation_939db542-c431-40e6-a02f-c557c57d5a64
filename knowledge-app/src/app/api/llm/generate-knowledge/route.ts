import { NextRequest, NextResponse } from 'next/server'
import { knowledgeGenerator } from '@/lib/llm/knowledgeGenerator'
import { KnowledgeGenerationRequest } from '@/lib/llm/types'

export async function POST(request: NextRequest) {
  try {
    const body: KnowledgeGenerationRequest = await request.json()

    if (!body.topic) {
      return NextResponse.json(
        { error: '主题不能为空' },
        { status: 400 }
      )
    }

    // 生成知识内容
    const knowledge = await knowledgeGenerator.generateKnowledge(body)

    return NextResponse.json({
      success: true,
      knowledge,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('知识生成失败:', error)
    
    return NextResponse.json(
      { 
        error: '知识生成失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
