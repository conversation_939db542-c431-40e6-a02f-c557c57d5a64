import { NextRequest, NextResponse } from 'next/server'
import { llmService } from '@/lib/llm/service'

export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json()

    if (!message) {
      return NextResponse.json(
        { error: '消息内容不能为空' },
        { status: 400 }
      )
    }

    // 测试LLM服务
    const response = await llmService.chat({
      messages: [
        {
          role: 'user',
          content: message
        }
      ],
      maxTokens: 100,
      temperature: 0.7
    })

    // 检查服务可用性
    const isAvailable = await llmService.isAvailable()
    const supportedModels = llmService.getSupportedModels()
    const providerName = llmService.getProviderName()

    return NextResponse.json({
      success: true,
      response: response.content,
      serviceInfo: {
        isAvailable,
        providerName,
        supportedModels,
        usage: response.usage
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('LLM测试失败:', error)
    
    return NextResponse.json(
      { 
        error: 'LLM服务测试失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
