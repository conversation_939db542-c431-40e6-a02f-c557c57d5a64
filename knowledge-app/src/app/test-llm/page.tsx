'use client'

import React, { useState } from 'react'
import { AuthGuard } from '@/components/auth/AuthGuard'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Brain, Send, Loader2 } from 'lucide-react'

export default function TestLLMPage() {
  const [topic, setTopic] = useState('')
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const testKnowledgeGeneration = async () => {
    if (!topic.trim()) {
      setError('请输入一个主题')
      return
    }

    setLoading(true)
    setError('')
    setResult(null)

    try {
      const response = await fetch('/api/llm/generate-knowledge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          topic: topic.trim(),
          userLevel: 'beginner',
          preferences: {
            includeExamples: true,
            includeExercises: true
          }
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      setResult(data)
    } catch (err) {
      console.error('测试失败:', err)
      setError(err instanceof Error ? err.message : '测试失败')
    } finally {
      setLoading(false)
    }
  }

  const testLLMService = async () => {
    setLoading(true)
    setError('')
    setResult(null)

    try {
      const response = await fetch('/api/llm/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: 'Hello, this is a test message'
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      setResult(data)
    } catch (err) {
      console.error('LLM服务测试失败:', err)
      setError(err instanceof Error ? err.message : 'LLM服务测试失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <div className="flex items-center">
              <Brain className="h-8 w-8 text-indigo-600 mr-3" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  LLM服务测试
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  测试LLM服务和知识生成功能
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 基础LLM测试 */}
            <Card>
              <CardHeader>
                <h2 className="text-xl font-semibold">基础LLM服务测试</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  测试LLM服务的基本连接和响应
                </p>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={testLLMService}
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      测试中...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      测试LLM服务
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* 知识生成测试 */}
            <Card>
              <CardHeader>
                <h2 className="text-xl font-semibold">知识生成测试</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  测试智能知识生成功能
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  placeholder="输入一个学习主题，例如：Python基础"
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  disabled={loading}
                />
                <Button
                  onClick={testKnowledgeGeneration}
                  disabled={loading || !topic.trim()}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Brain className="w-4 h-4 mr-2" />
                      生成知识内容
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* 错误显示 */}
          {error && (
            <Card className="mt-8 border-red-200 bg-red-50">
              <CardContent className="pt-6">
                <div className="text-red-800">
                  <h3 className="font-semibold mb-2">错误信息</h3>
                  <p>{error}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 结果显示 */}
          {result && (
            <Card className="mt-8">
              <CardHeader>
                <h2 className="text-xl font-semibold">测试结果</h2>
              </CardHeader>
              <CardContent>
                <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-auto text-sm">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </AuthGuard>
  )
}
