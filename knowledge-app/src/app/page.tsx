'use client'

import { <PERSON>, BookOpen, Target, Users } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center items-center mb-6">
            <Brain className="w-16 h-16 text-indigo-600 mr-4" />
            <h1 className="text-5xl font-bold text-gray-900 dark:text-white">
              Learn Everything
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            构建你的个人知识图谱，智能推荐学习路径，通过严格验证确保真正掌握每个知识点
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <Brain className="w-12 h-12 text-indigo-600 mb-4" />
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              知识图谱
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              构建有向无环图结构的知识体系，清晰展示知识点间的依赖关系
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <Target className="w-12 h-12 text-green-600 mb-4" />
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              智能推荐
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              基于你的学习进度和知识掌握情况，推荐最适合的下一步学习内容
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <BookOpen className="w-12 h-12 text-purple-600 mb-4" />
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              严格验证
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              通过笔记总结和题目测试，确保你真正理解和掌握每个知识点
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <Users className="w-12 h-12 text-orange-600 mb-4" />
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              分类管理
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              多维度标签系统，让你的知识库井然有序，便于查找和管理
            </p>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg max-w-2xl mx-auto">
            <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
              开始构建你的知识体系
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              从今天开始，让学习变得更有条理、更高效、更深入
            </p>
            <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
              <a
                href="/auth/signin"
                className="w-full sm:w-auto bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors inline-flex items-center justify-center"
              >
                立即开始
              </a>
              <a
                href="/auth/signup"
                className="w-full sm:w-auto border border-indigo-600 text-indigo-600 hover:bg-indigo-50 dark:hover:bg-indigo-900 font-semibold py-3 px-6 rounded-lg transition-colors inline-flex items-center justify-center"
              >
                免费注册
              </a>
            </div>
          </div>
        </div>

        {/* Status */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            项目初始化完成 - 准备开始开发核心功能
          </div>
        </div>
      </div>
    </div>
  )
}
