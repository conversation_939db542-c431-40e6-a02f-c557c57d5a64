'use client'

import React, { useState } from 'react'
import { KnowledgePointList } from '@/components/knowledge/KnowledgePointList'
import { KnowledgePointForm } from '@/components/knowledge/KnowledgePointForm'
import { AuthGuard } from '@/components/auth/AuthGuard'
import { useKnowledgeGraph } from '@/hooks/useKnowledgeGraph'
import { useAuth } from '@/hooks/useAuth'
import { KnowledgePoint } from '@/types'
import { Brain, ArrowLeft, LogOut, User, Zap, MessageCircle } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'

type ViewMode = 'list' | 'create' | 'edit'

export default function KnowledgePage() {
  const {
    createKnowledgePoint,
    updateKnowledgePoint,
    deleteKnowledgePoint,
    isLoading,
    error
  } = useKnowledgeGraph()

  const { user, signOut } = useAuth()
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [editingPoint, setEditingPoint] = useState<KnowledgePoint | null>(null)

  const handleCreateNew = () => {
    setEditingPoint(null)
    setViewMode('create')
  }

  const handleEdit = (point: KnowledgePoint) => {
    setEditingPoint(point)
    setViewMode('edit')
  }

  const handleDelete = async (id: string) => {
    if (window.confirm('确定要删除这个知识点吗？这将同时删除相关的依赖关系和学习记录。')) {
      const result = await deleteKnowledgePoint(id)
      if (!result.success && result.error) {
        alert(result.error)
      }
    }
  }

  const handleFormSubmit = async (data: Omit<KnowledgePoint, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      // 使用当前登录用户的ID
      const formDataWithUser = {
        ...data,
        user_id: user?.id || 'mock-user'
      }

      let result
      if (viewMode === 'edit' && editingPoint) {
        result = await updateKnowledgePoint(editingPoint.id, formDataWithUser)
      } else {
        result = await createKnowledgePoint(formDataWithUser)
      }

      if (result.success) {
        setViewMode('list')
        setEditingPoint(null)
      } else {
        alert(result.error || '操作失败')
      }
    } catch (error) {
      console.error('表单提交失败:', error)
      alert('操作失败，请重试')
    }
  }

  const handleCancel = () => {
    setViewMode('list')
    setEditingPoint(null)
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {viewMode !== 'list' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCancel}
                  className="mr-4"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回
                </Button>
              )}
              <div className="flex items-center">
                <Brain className="h-8 w-8 text-indigo-600 mr-3" />
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    {viewMode === 'list' && '知识管理'}
                    {viewMode === 'create' && '创建知识点'}
                    {viewMode === 'edit' && '编辑知识点'}
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400 mt-1">
                    {viewMode === 'list' && '管理你的知识点和学习进度'}
                    {viewMode === 'create' && '添加新的知识点到你的学习体系'}
                    {viewMode === 'edit' && '修改知识点信息'}
                  </p>
                </div>
              </div>
            </div>

            {/* 用户信息和操作 */}
            <div className="flex items-center gap-4">
              <Link href="/chat">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-green-600 border-green-600 hover:bg-green-50"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  AI学习助手
                </Button>
              </Link>
              <Link href="/adaptive">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-indigo-600 border-indigo-600 hover:bg-indigo-50"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  AI自适应学习
                </Button>
              </Link>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <User className="h-4 w-4" />
                <span>欢迎，{user?.name || user?.email}</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={signOut}
                className="text-gray-600 hover:text-gray-800"
              >
                <LogOut className="h-4 w-4 mr-2" />
                登出
              </Button>
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* 主要内容区域 */}
        <div className="max-w-7xl mx-auto">
          {viewMode === 'list' && (
            <KnowledgePointList
              onCreateNew={handleCreateNew}
              onEditPoint={handleEdit}
              onDeletePoint={handleDelete}
              showActions={true}
            />
          )}

          {(viewMode === 'create' || viewMode === 'edit') && (
            <div className="flex justify-center">
              <KnowledgePointForm
                knowledgePoint={editingPoint}
                onSubmit={handleFormSubmit}
                onCancel={handleCancel}
                isLoading={isLoading}
              />
            </div>
          )}
        </div>

        {/* 底部统计信息 */}
        {viewMode === 'list' && (
          <div className="mt-12 text-center">
            <div className="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <Brain className="h-4 w-4 text-indigo-600 mr-2" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                知识图谱核心功能已就绪 - 可以开始创建和管理知识点
              </span>
            </div>
          </div>
        )}
        </div>
      </div>
    </AuthGuard>
  )
}
