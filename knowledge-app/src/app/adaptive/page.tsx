'use client'

import React from 'react'
import { AuthGuard } from '@/components/auth/AuthGuard'
import { AdaptiveLearningInterface } from '@/components/adaptive/AdaptiveLearningInterface'
import { useAuth } from '@/hooks/useAuth'
import { Brain, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'

export default function AdaptiveLearningPage() {
  const { user } = useAuth()

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          {/* 页面头部 */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Link href="/knowledge">
                  <Button variant="ghost" size="sm" className="mr-4">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    返回知识管理
                  </Button>
                </Link>
                <div className="flex items-center">
                  <Brain className="h-8 w-8 text-indigo-600 mr-3" />
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                      AI自适应学习
                    </h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                      智能分析学习困难，动态规划个性化学习路径
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 功能介绍 */}
          <div className="mb-8 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              🚀 体验AI驱动的个性化学习
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-start">
                <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-800 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-indigo-600 dark:text-indigo-300 font-bold">1</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">开始学习</p>
                  <p className="text-gray-600 dark:text-gray-400">输入您想学习的主题，AI将生成个性化内容</p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-800 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-indigo-600 dark:text-indigo-300 font-bold">2</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">遇到困难</p>
                  <p className="text-gray-600 dark:text-gray-400">告诉AI您不理解的概念，获得智能分析</p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-800 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-indigo-600 dark:text-indigo-300 font-bold">3</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">智能规划</p>
                  <p className="text-gray-600 dark:text-gray-400">AI分析依赖关系，规划最优学习路径</p>
                </div>
              </div>
            </div>
          </div>

          {/* 自适应学习界面 */}
          <AdaptiveLearningInterface userId={user?.id || 'demo-user'} />

          {/* 功能说明 */}
          <div className="mt-12 bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              💡 系统特性
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">智能依赖分析</h4>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• 自动识别知识前置要求</li>
                  <li>• 分析概念间的依赖关系</li>
                  <li>• 评估学习准备度</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">动态路径规划</h4>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• 基于个人进度调整路径</li>
                  <li>• 优化学习顺序和时间</li>
                  <li>• 提供个性化建议</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">自适应学习循环</h4>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• 学习 → 遇到问题 → 分析依赖</li>
                  <li>• 规划路径 → 学习前置知识</li>
                  <li>• 回到原知识点继续学习</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">智能内容生成</h4>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• 基于用户水平生成内容</li>
                  <li>• 包含示例和练习</li>
                  <li>• 实时调整难度和重点</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 开发模式提示 */}
          <div className="mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <Brain className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  开发模式说明
                </h3>
                <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                  <p>
                    当前运行在开发模式下，使用模拟的LLM响应。在生产环境中，系统将连接到真实的AI模型
                    （Groq、Moonshot Kimi等）提供更智能的分析和建议。
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AuthGuard>
  )
}
