import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder_key'

// 检查是否为开发环境且使用占位符配置
const isDevMode = process.env.NODE_ENV === 'development' && supabaseUrl.includes('placeholder')

// 创建Supabase客户端，开发模式下使用模拟配置
export const supabase = isDevMode
  ? null // 开发模式下返回null，API调用将被模拟
  : createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      knowledge_points: {
        Row: {
          id: string
          name: string
          description: string | null
          difficulty_level: number
          tags: string[]
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          difficulty_level?: number
          tags?: string[]
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          difficulty_level?: number
          tags?: string[]
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      knowledge_dependencies: {
        Row: {
          id: string
          prerequisite_id: string
          dependent_id: string
          created_at: string
        }
        Insert: {
          id?: string
          prerequisite_id: string
          dependent_id: string
          created_at?: string
        }
        Update: {
          id?: string
          prerequisite_id?: string
          dependent_id?: string
          created_at?: string
        }
      }
      user_progress: {
        Row: {
          id: string
          user_id: string
          knowledge_point_id: string
          mastery_level: number
          notes: string | null
          last_reviewed: string | null
          test_scores: number[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          knowledge_point_id: string
          mastery_level?: number
          notes?: string | null
          last_reviewed?: string | null
          test_scores?: number[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          knowledge_point_id?: string
          mastery_level?: number
          notes?: string | null
          last_reviewed?: string | null
          test_scores?: number[]
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
