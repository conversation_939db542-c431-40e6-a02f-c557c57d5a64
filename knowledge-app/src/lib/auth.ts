import { supabase } from './supabase'
import { User, Session } from '@supabase/supabase-js'

export interface AuthUser {
  id: string
  email: string
  name?: string
  avatar_url?: string
  created_at: string
}

export interface AuthState {
  user: AuthUser | null
  session: Session | null
  loading: boolean
}

/**
 * 认证服务类
 * 处理用户注册、登录、登出等认证相关操作
 */
export class AuthService {
  
  /**
   * 用户注册
   */
  async signUp(email: string, password: string, metadata?: { name?: string }) {
    if (!supabase) {
      // 开发模式下的模拟注册
      return {
        data: {
          user: {
            id: 'mock-user-' + Date.now(),
            email,
            user_metadata: metadata || {},
            created_at: new Date().toISOString()
          },
          session: null
        },
        error: null
      }
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata || {}
      }
    })

    return { data, error }
  }

  /**
   * 用户登录
   */
  async signIn(email: string, password: string) {
    if (!supabase) {
      // 开发模式下的模拟登录
      return {
        data: {
          user: {
            id: 'mock-user',
            email,
            user_metadata: { name: '测试用户' },
            created_at: new Date().toISOString()
          },
          session: {
            access_token: 'mock-token',
            refresh_token: 'mock-refresh',
            expires_in: 3600,
            token_type: 'bearer',
            user: {
              id: 'mock-user',
              email,
              user_metadata: { name: '测试用户' }
            }
          }
        },
        error: null
      }
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    return { data, error }
  }

  /**
   * 第三方登录 (Google)
   */
  async signInWithGoogle() {
    if (!supabase) {
      // 开发模式下不支持第三方登录
      return {
        data: null,
        error: { message: '开发模式下不支持第三方登录' }
      }
    }

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })

    return { data, error }
  }

  /**
   * 用户登出
   */
  async signOut() {
    if (!supabase) {
      // 开发模式下的模拟登出
      return { error: null }
    }

    const { error } = await supabase.auth.signOut()
    return { error }
  }

  /**
   * 获取当前用户
   */
  async getCurrentUser(): Promise<AuthUser | null> {
    if (!supabase) {
      // 开发模式下返回模拟用户
      return {
        id: 'mock-user',
        email: '<EMAIL>',
        name: '测试用户',
        created_at: new Date().toISOString()
      }
    }

    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return null
    }

    return {
      id: user.id,
      email: user.email || '',
      name: user.user_metadata?.name || user.email?.split('@')[0],
      avatar_url: user.user_metadata?.avatar_url,
      created_at: user.created_at || new Date().toISOString()
    }
  }

  /**
   * 获取当前会话
   */
  async getCurrentSession(): Promise<Session | null> {
    if (!supabase) {
      // 开发模式下返回模拟会话
      return {
        access_token: 'mock-token',
        refresh_token: 'mock-refresh',
        expires_in: 3600,
        token_type: 'bearer',
        user: {
          id: 'mock-user',
          email: '<EMAIL>',
          user_metadata: { name: '测试用户' }
        }
      } as Session
    }

    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      console.error('获取会话失败:', error)
      return null
    }

    return session
  }

  /**
   * 刷新会话
   */
  async refreshSession() {
    if (!supabase) {
      return { data: null, error: null }
    }

    const { data, error } = await supabase.auth.refreshSession()
    return { data, error }
  }

  /**
   * 更新用户信息
   */
  async updateUser(updates: { 
    email?: string
    password?: string
    data?: { name?: string, avatar_url?: string }
  }) {
    if (!supabase) {
      // 开发模式下的模拟更新
      return {
        data: {
          user: {
            id: 'mock-user',
            email: updates.email || '<EMAIL>',
            user_metadata: updates.data || { name: '测试用户' }
          }
        },
        error: null
      }
    }

    const { data, error } = await supabase.auth.updateUser(updates)
    return { data, error }
  }

  /**
   * 重置密码
   */
  async resetPassword(email: string) {
    if (!supabase) {
      // 开发模式下的模拟重置
      return { data: null, error: null }
    }

    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    })

    return { data, error }
  }

  /**
   * 监听认证状态变化
   */
  onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    if (!supabase) {
      // 开发模式下不监听状态变化
      return { data: { subscription: { unsubscribe: () => {} } } }
    }

    return supabase.auth.onAuthStateChange(callback)
  }
}

// 导出单例实例
export const authService = new AuthService()
