/**
 * 智能知识生成服务
 * 基于用户兴趣和需求，使用LLM生成个性化学习内容
 */

import { llmService } from './service'
import { 
  KnowledgeGenerationRequest, 
  GeneratedKnowledge,
  LLMMessage 
} from './types'

/**
 * 知识生成服务类
 */
export class KnowledgeGeneratorService {
  
  /**
   * 生成知识内容
   */
  async generateKnowledge(request: KnowledgeGenerationRequest): Promise<GeneratedKnowledge> {
    const prompt = this.buildKnowledgeGenerationPrompt(request)
    
    try {
      const response = await llmService.chat({
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        maxTokens: 3000
      })

      return this.parseKnowledgeResponse(response.content, request)
    } catch (error) {
      console.error('知识生成失败:', error)
      throw new Error('无法生成知识内容，请稍后重试')
    }
  }

  /**
   * 批量生成相关知识点
   */
  async generateRelatedKnowledge(
    baseTopic: string, 
    count: number = 5
  ): Promise<GeneratedKnowledge[]> {
    const prompt = `请为主题"${baseTopic}"生成${count}个相关的知识点，每个知识点应该：
1. 与主题相关但各有侧重
2. 难度递进或并列
3. 包含实用的学习内容

请以JSON数组格式返回，每个知识点包含：title, description, difficulty_level, estimated_time, prerequisites, tags`

    try {
      const response = await llmService.chat({
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.8,
        maxTokens: 4000
      })

      return this.parseMultipleKnowledgeResponse(response.content)
    } catch (error) {
      console.error('批量知识生成失败:', error)
      throw new Error('无法生成相关知识点，请稍后重试')
    }
  }

  /**
   * 根据用户兴趣推荐学习主题
   */
  async recommendTopics(
    userInterests: string[], 
    currentLevel: string = 'beginner'
  ): Promise<string[]> {
    const prompt = `基于用户兴趣：${userInterests.join(', ')}，当前水平：${currentLevel}
请推荐10个适合的学习主题，要求：
1. 与用户兴趣相关
2. 适合当前水平
3. 有实际应用价值
4. 学习路径清晰

请以简洁的主题名称列表返回，每行一个主题。`

    try {
      const response = await llmService.chat({
        messages: [
          {
            role: 'system',
            content: '你是一个专业的学习顾问，擅长根据用户兴趣推荐合适的学习主题。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.6,
        maxTokens: 1000
      })

      return this.parseTopicRecommendations(response.content)
    } catch (error) {
      console.error('主题推荐失败:', error)
      return [] // 返回空数组而不是抛出错误
    }
  }

  /**
   * 构建知识生成提示词
   */
  private buildKnowledgeGenerationPrompt(request: KnowledgeGenerationRequest): string {
    const { topic, userLevel = 'beginner', context, preferences } = request

    let prompt = `请为主题"${topic}"生成详细的学习内容。

用户水平：${userLevel}
${context ? `背景信息：${context}` : ''}

要求：
1. 内容要准确、实用、易懂
2. 适合${userLevel}水平的学习者
3. 包含清晰的概念解释和实例
4. 估算学习时间和难度等级（1-10）
5. 列出必要的前置知识
6. 添加相关标签

${preferences?.includeExamples ? '请包含具体的代码示例或实际案例。' : ''}
${preferences?.includeExercises ? '请包含练习题或实践任务。' : ''}
${preferences?.focusAreas?.length ? `重点关注：${preferences.focusAreas.join(', ')}` : ''}

请以结构化的格式返回，包含：
- 标题
- 描述
- 详细内容
- 难度等级（1-10）
- 估计学习时间（分钟）
- 前置知识列表
- 标签列表
${preferences?.includeExamples ? '- 示例列表' : ''}
${preferences?.includeExercises ? '- 练习列表' : ''}`

    return prompt
  }

  /**
   * 获取系统提示词
   */
  private getSystemPrompt(): string {
    return `你是一个专业的教育内容生成专家，擅长创建高质量的学习材料。你的任务是：

1. 生成准确、实用的学习内容
2. 根据用户水平调整内容难度
3. 提供清晰的学习路径和时间估算
4. 确保内容的逻辑性和连贯性
5. 包含实际应用和案例

请始终以学习者的角度思考，确保内容易于理解和实践。`
  }

  /**
   * 解析知识生成响应
   */
  private parseKnowledgeResponse(
    content: string, 
    request: KnowledgeGenerationRequest
  ): GeneratedKnowledge {
    try {
      // 尝试解析JSON格式的响应
      if (content.includes('{') && content.includes('}')) {
        const jsonMatch = content.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          const parsed = JSON.parse(jsonMatch[0])
          return this.normalizeKnowledgeObject(parsed)
        }
      }

      // 如果不是JSON格式，则解析文本格式
      return this.parseTextKnowledgeResponse(content, request)
    } catch (error) {
      console.warn('解析知识响应失败，使用文本解析:', error)
      return this.parseTextKnowledgeResponse(content, request)
    }
  }

  /**
   * 解析文本格式的知识响应
   */
  private parseTextKnowledgeResponse(
    content: string, 
    request: KnowledgeGenerationRequest
  ): GeneratedKnowledge {
    const lines = content.split('\n').filter(line => line.trim())
    
    return {
      title: request.topic,
      description: `关于${request.topic}的学习内容`,
      content: content,
      difficulty_level: this.extractDifficultyLevel(content),
      estimated_time: this.extractEstimatedTime(content),
      prerequisites: this.extractPrerequisites(content),
      tags: this.extractTags(content, request.topic),
      examples: request.preferences?.includeExamples ? this.extractExamples(content) : undefined,
      exercises: request.preferences?.includeExercises ? this.extractExercises(content) : undefined
    }
  }

  /**
   * 解析多个知识点响应
   */
  private parseMultipleKnowledgeResponse(content: string): GeneratedKnowledge[] {
    try {
      // 尝试解析JSON数组
      const jsonMatch = content.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        return parsed.map((item: any) => this.normalizeKnowledgeObject(item))
      }

      // 如果不是JSON格式，返回空数组
      return []
    } catch (error) {
      console.warn('解析多个知识点响应失败:', error)
      return []
    }
  }

  /**
   * 解析主题推荐
   */
  private parseTopicRecommendations(content: string): string[] {
    return content
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('#'))
      .map(line => line.replace(/^\d+\.\s*/, '').replace(/^-\s*/, ''))
      .slice(0, 10) // 限制最多10个主题
  }

  /**
   * 标准化知识对象
   */
  private normalizeKnowledgeObject(obj: any): GeneratedKnowledge {
    return {
      title: obj.title || obj.name || '未命名知识点',
      description: obj.description || obj.desc || '',
      content: obj.content || obj.details || obj.description || '',
      difficulty_level: parseInt(obj.difficulty_level || obj.difficulty || '5'),
      estimated_time: parseInt(obj.estimated_time || obj.time || '30'),
      prerequisites: Array.isArray(obj.prerequisites) ? obj.prerequisites : [],
      tags: Array.isArray(obj.tags) ? obj.tags : [],
      examples: Array.isArray(obj.examples) ? obj.examples : undefined,
      exercises: Array.isArray(obj.exercises) ? obj.exercises : undefined
    }
  }

  /**
   * 提取难度等级
   */
  private extractDifficultyLevel(content: string): number {
    const match = content.match(/难度[：:]\s*(\d+)/i) || 
                  content.match(/difficulty[：:]\s*(\d+)/i)
    return match ? parseInt(match[1]) : 5
  }

  /**
   * 提取估计时间
   */
  private extractEstimatedTime(content: string): number {
    const match = content.match(/时间[：:]\s*(\d+)/i) || 
                  content.match(/time[：:]\s*(\d+)/i) ||
                  content.match(/(\d+)\s*分钟/i)
    return match ? parseInt(match[1]) : 30
  }

  /**
   * 提取前置知识
   */
  private extractPrerequisites(content: string): string[] {
    const section = this.extractSection(content, ['前置知识', 'prerequisites', '先决条件'])
    if (!section) return []
    
    return section
      .split(/[,，\n]/)
      .map(item => item.trim())
      .filter(item => item && item.length > 1)
  }

  /**
   * 提取标签
   */
  private extractTags(content: string, topic: string): string[] {
    const section = this.extractSection(content, ['标签', 'tags', '关键词'])
    const tags = section ? 
      section.split(/[,，\n]/).map(item => item.trim()).filter(item => item) :
      [topic]
    
    return [...new Set(tags)] // 去重
  }

  /**
   * 提取示例
   */
  private extractExamples(content: string): string[] {
    const section = this.extractSection(content, ['示例', 'examples', '案例'])
    if (!section) return []
    
    return section
      .split(/\n\n/)
      .map(item => item.trim())
      .filter(item => item && item.length > 10)
  }

  /**
   * 提取练习
   */
  private extractExercises(content: string): string[] {
    const section = this.extractSection(content, ['练习', 'exercises', '任务'])
    if (!section) return []
    
    return section
      .split(/\n\n/)
      .map(item => item.trim())
      .filter(item => item && item.length > 5)
  }

  /**
   * 提取内容段落
   */
  private extractSection(content: string, keywords: string[]): string | null {
    for (const keyword of keywords) {
      const regex = new RegExp(`${keyword}[：:]([\\s\\S]*?)(?=\\n\\n|\\n[^\\s]|$)`, 'i')
      const match = content.match(regex)
      if (match) {
        return match[1].trim()
      }
    }
    return null
  }
}

// 导出单例实例
export const knowledgeGenerator = new KnowledgeGeneratorService()
