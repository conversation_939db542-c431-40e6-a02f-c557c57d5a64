/**
 * LLM服务的类型定义
 */

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

export interface LLMResponse {
  content: string
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
  model?: string
  finish_reason?: string
}

export interface LLMStreamResponse {
  content: string
  done: boolean
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export interface LLMRequestOptions {
  messages: LLMMessage[]
  maxTokens?: number
  temperature?: number
  stream?: boolean
  stop?: string[]
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
}

export interface LLMError {
  code: string
  message: string
  type: 'api_error' | 'network_error' | 'rate_limit' | 'invalid_request' | 'authentication_error'
  details?: any
}

/**
 * LLM服务接口
 */
export interface LLMService {
  /**
   * 发送聊天请求
   */
  chat(options: LLMRequestOptions): Promise<LLMResponse>

  /**
   * 发送流式聊天请求
   */
  chatStream(options: LLMRequestOptions): AsyncGenerator<LLMStreamResponse, void, unknown>

  /**
   * 检查服务是否可用
   */
  isAvailable(): Promise<boolean>

  /**
   * 获取支持的模型列表
   */
  getSupportedModels(): string[]

  /**
   * 获取服务提供商名称
   */
  getProviderName(): string
}

/**
 * 知识生成相关的类型
 */
export interface KnowledgeGenerationRequest {
  topic: string
  userLevel?: 'beginner' | 'intermediate' | 'advanced'
  context?: string
  preferences?: {
    includeExamples?: boolean
    includeExercises?: boolean
    focusAreas?: string[]
  }
}

export interface GeneratedKnowledge {
  title: string
  description: string
  content: string
  difficulty_level: number
  estimated_time: number
  prerequisites?: string[]
  tags?: string[]
  examples?: string[]
  exercises?: string[]
}

/**
 * 依赖分析相关的类型
 */
export interface DependencyAnalysisRequest {
  currentTopic: string
  unknownConcepts: string[]
  userContext?: string
  currentLevel?: string
}

export interface AnalyzedDependency {
  concept: string
  importance: number
  difficulty: number
  description: string
  learningPath?: string[]
  estimatedTime?: number
  resources?: string[]
}

export interface DependencyAnalysisResponse {
  dependencies: AnalyzedDependency[]
  recommendedOrder: string[]
  totalEstimatedTime: number
  explanation: string
}

/**
 * 学习路径规划相关的类型
 */
export interface LearningPathRequest {
  targetTopic: string
  currentKnowledge: string[]
  missingKnowledge: string[]
  timeConstraints?: {
    totalTime?: number
    sessionLength?: number
    frequency?: string
  }
  preferences?: {
    learningStyle?: 'visual' | 'auditory' | 'kinesthetic' | 'reading'
    difficulty?: 'gradual' | 'challenging'
    includeProjects?: boolean
  }
}

export interface LearningStep {
  id: string
  title: string
  description: string
  type: 'concept' | 'practice' | 'project' | 'review'
  estimatedTime: number
  difficulty: number
  prerequisites: string[]
  resources?: string[]
  objectives?: string[]
}

export interface LearningPath {
  id: string
  title: string
  description: string
  totalTime: number
  difficulty: number
  steps: LearningStep[]
  milestones: string[]
  alternatives?: LearningPath[]
}
