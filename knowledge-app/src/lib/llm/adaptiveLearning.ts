/**
 * 自适应学习流程核心服务
 * 实现完整的学习循环：学习→遇到问题→分析依赖→规划路径→学习前置知识→回到原知识
 */

import { knowledgeGenerator } from './knowledgeGenerator'
import { dependencyAnalyzer } from './dependencyAnalyzer'
import { pathPlanner } from './pathPlanner'
import { llmService } from './service'
import { 
  GeneratedKnowledge,
  DependencyAnalysisResponse,
  LearningPath,
  LearningStep
} from './types'

/**
 * 学习会话状态
 */
export interface LearningSession {
  id: string
  userId: string
  targetTopic: string
  currentStep: string
  learningHistory: LearningRecord[]
  knowledgeGraph: { [concept: string]: number } // 概念 -> 掌握程度
  currentPath: LearningPath | null
  blockedConcepts: string[] // 当前遇到困难的概念
  sessionStartTime: Date
  lastActivityTime: Date
  status: 'active' | 'paused' | 'completed' | 'abandoned'
}

/**
 * 学习记录
 */
export interface LearningRecord {
  id: string
  timestamp: Date
  action: 'start_topic' | 'encounter_difficulty' | 'request_help' | 'complete_step' | 'path_generated'
  concept: string
  details: any
  timeSpent?: number
  masteryBefore?: number
  masteryAfter?: number
}

/**
 * 学习困难报告
 */
export interface LearningDifficulty {
  concept: string
  description: string
  userQuestion?: string
  context: string
  timestamp: Date
}

/**
 * 自适应学习响应
 */
export interface AdaptiveLearningResponse {
  type: 'knowledge_generated' | 'dependency_analysis' | 'path_suggested' | 'help_provided'
  content: any
  nextSteps: string[]
  estimatedTime: number
  confidence: number
}

/**
 * 自适应学习服务类
 */
export class AdaptiveLearningService {
  private activeSessions: Map<string, LearningSession> = new Map()

  /**
   * 开始新的学习会话
   */
  async startLearningSession(
    userId: string, 
    targetTopic: string,
    userContext?: {
      currentKnowledge: string[]
      learningGoals: string[]
      timeAvailable: number
      preferredStyle: string
    }
  ): Promise<LearningSession> {
    const sessionId = this.generateSessionId()
    
    // 生成初始知识内容
    const initialKnowledge = await knowledgeGenerator.generateKnowledge({
      topic: targetTopic,
      userLevel: this.inferUserLevel(userContext?.currentKnowledge || []),
      context: userContext ? JSON.stringify(userContext) : undefined,
      preferences: {
        includeExamples: true,
        includeExercises: true
      }
    })

    // 创建学习会话
    const session: LearningSession = {
      id: sessionId,
      userId,
      targetTopic,
      currentStep: 'initial_learning',
      learningHistory: [{
        id: this.generateRecordId(),
        timestamp: new Date(),
        action: 'start_topic',
        concept: targetTopic,
        details: { initialKnowledge }
      }],
      knowledgeGraph: this.initializeKnowledgeGraph(userContext?.currentKnowledge || []),
      currentPath: null,
      blockedConcepts: [],
      sessionStartTime: new Date(),
      lastActivityTime: new Date(),
      status: 'active'
    }

    this.activeSessions.set(sessionId, session)
    return session
  }

  /**
   * 处理学习困难 - 核心自适应逻辑
   */
  async handleLearningDifficulty(
    sessionId: string,
    difficulty: LearningDifficulty
  ): Promise<AdaptiveLearningResponse> {
    const session = this.activeSessions.get(sessionId)
    if (!session) {
      throw new Error('学习会话不存在')
    }

    try {
      // 1. 记录困难
      this.recordLearningDifficulty(session, difficulty)

      // 2. 分析依赖关系
      const dependencyAnalysis = await dependencyAnalyzer.analyzeDependencies({
        currentTopic: session.targetTopic,
        unknownConcepts: [difficulty.concept],
        userContext: difficulty.context,
        currentLevel: this.getCurrentUserLevel(session)
      })

      // 3. 评估是否需要学习前置知识
      const needsPrerequisites = await this.assessPrerequisiteNeed(
        session, 
        difficulty.concept, 
        dependencyAnalysis
      )

      if (needsPrerequisites) {
        // 4. 生成学习路径
        const learningPath = await this.generateAdaptivePath(session, dependencyAnalysis)
        
        // 5. 更新会话状态
        session.currentPath = learningPath
        session.blockedConcepts.push(difficulty.concept)
        session.currentStep = 'learning_prerequisites'

        return {
          type: 'path_suggested',
          content: {
            analysis: dependencyAnalysis,
            path: learningPath,
            message: `我发现您在"${difficulty.concept}"上遇到了困难。让我为您规划一个学习路径，先掌握必要的前置知识。`
          },
          nextSteps: learningPath.steps.slice(0, 3).map(step => step.title),
          estimatedTime: learningPath.totalTime,
          confidence: 0.85
        }
      } else {
        // 6. 提供直接帮助
        const helpContent = await this.generateDirectHelp(session, difficulty)
        
        return {
          type: 'help_provided',
          content: helpContent,
          nextSteps: ['继续当前学习', '尝试相关练习'],
          estimatedTime: 15,
          confidence: 0.75
        }
      }
    } catch (error) {
      console.error('处理学习困难失败:', error)
      throw new Error('无法处理学习困难，请稍后重试')
    }
  }

  /**
   * 完成学习步骤
   */
  async completeStep(
    sessionId: string,
    stepId: string,
    masteryLevel: number,
    timeSpent: number,
    feedback?: string
  ): Promise<AdaptiveLearningResponse> {
    const session = this.activeSessions.get(sessionId)
    if (!session) {
      throw new Error('学习会话不存在')
    }

    // 更新掌握程度
    const step = this.findStepInPath(session.currentPath, stepId)
    if (step) {
      session.knowledgeGraph[step.title] = masteryLevel
      
      // 记录学习记录
      session.learningHistory.push({
        id: this.generateRecordId(),
        timestamp: new Date(),
        action: 'complete_step',
        concept: step.title,
        details: { stepId, feedback },
        timeSpent,
        masteryAfter: masteryLevel
      })
    }

    // 检查是否可以回到原始学习目标
    const canReturnToOriginal = await this.checkReturnCondition(session)
    
    if (canReturnToOriginal) {
      // 移除已解决的阻塞概念
      session.blockedConcepts = session.blockedConcepts.filter(
        concept => session.knowledgeGraph[concept] < 70
      )
      
      if (session.blockedConcepts.length === 0) {
        session.currentStep = 'returning_to_original'
        
        return {
          type: 'knowledge_generated',
          content: {
            message: `太好了！您已经掌握了必要的前置知识。现在让我们回到原始学习目标："${session.targetTopic}"`,
            nextConcept: session.targetTopic,
            readinessScore: this.calculateReadinessScore(session)
          },
          nextSteps: ['回到原始学习目标', '应用新掌握的知识'],
          estimatedTime: 30,
          confidence: 0.9
        }
      }
    }

    // 继续当前路径
    const nextStep = this.getNextStep(session.currentPath, stepId)
    return {
      type: 'path_suggested',
      content: {
        currentProgress: this.calculatePathProgress(session.currentPath, stepId),
        nextStep,
        encouragement: this.generateEncouragement(masteryLevel)
      },
      nextSteps: nextStep ? [nextStep.title] : ['完成当前路径'],
      estimatedTime: nextStep?.estimatedTime || 0,
      confidence: 0.8
    }
  }

  /**
   * 获取学习建议
   */
  async getLearningRecommendation(sessionId: string): Promise<AdaptiveLearningResponse> {
    const session = this.activeSessions.get(sessionId)
    if (!session) {
      throw new Error('学习会话不存在')
    }

    const prompt = `基于用户的学习会话，提供个性化的学习建议：

学习目标：${session.targetTopic}
当前步骤：${session.currentStep}
学习历史：${session.learningHistory.length}个记录
知识掌握情况：${JSON.stringify(session.knowledgeGraph)}
遇到困难的概念：${session.blockedConcepts.join(', ')}

请提供：
1. 当前学习状态评估
2. 下一步学习建议
3. 潜在的学习风险和解决方案
4. 学习效率优化建议

请以JSON格式返回建议。`

    try {
      const response = await llmService.chat({
        messages: [
          {
            role: 'system',
            content: '你是一个专业的学习顾问，能够基于学习数据提供个性化建议。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.6,
        maxTokens: 2000
      })

      const recommendation = this.parseRecommendationResponse(response.content)

      return {
        type: 'help_provided',
        content: recommendation,
        nextSteps: recommendation.nextSteps || ['继续当前学习'],
        estimatedTime: 0,
        confidence: 0.8
      }
    } catch (error) {
      console.error('获取学习建议失败:', error)
      throw new Error('无法获取学习建议')
    }
  }

  /**
   * 暂停学习会话
   */
  pauseSession(sessionId: string): void {
    const session = this.activeSessions.get(sessionId)
    if (session) {
      session.status = 'paused'
      session.lastActivityTime = new Date()
    }
  }

  /**
   * 恢复学习会话
   */
  resumeSession(sessionId: string): LearningSession | null {
    const session = this.activeSessions.get(sessionId)
    if (session) {
      session.status = 'active'
      session.lastActivityTime = new Date()
      return session
    }
    return null
  }

  /**
   * 获取学习会话
   */
  getSession(sessionId: string): LearningSession | null {
    return this.activeSessions.get(sessionId) || null
  }

  /**
   * 私有辅助方法
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateRecordId(): string {
    return `record_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private inferUserLevel(currentKnowledge: string[]): 'beginner' | 'intermediate' | 'advanced' {
    const count = currentKnowledge.length
    if (count < 3) return 'beginner'
    if (count < 10) return 'intermediate'
    return 'advanced'
  }

  private initializeKnowledgeGraph(currentKnowledge: string[]): { [concept: string]: number } {
    const graph: { [concept: string]: number } = {}
    currentKnowledge.forEach(concept => {
      graph[concept] = 80 // 假设已掌握的知识为80%
    })
    return graph
  }

  private recordLearningDifficulty(session: LearningSession, difficulty: LearningDifficulty): void {
    session.learningHistory.push({
      id: this.generateRecordId(),
      timestamp: new Date(),
      action: 'encounter_difficulty',
      concept: difficulty.concept,
      details: difficulty
    })
    session.lastActivityTime = new Date()
  }

  private getCurrentUserLevel(session: LearningSession): string {
    const masteredConcepts = Object.values(session.knowledgeGraph).filter(level => level >= 70).length
    if (masteredConcepts < 3) return 'beginner'
    if (masteredConcepts < 10) return 'intermediate'
    return 'advanced'
  }

  private async assessPrerequisiteNeed(
    session: LearningSession,
    concept: string,
    analysis: DependencyAnalysisResponse
  ): Promise<boolean> {
    // 检查用户是否已经掌握了必要的前置知识
    const missingPrerequisites = analysis.dependencies.filter(dep => 
      !session.knowledgeGraph[dep.concept] || session.knowledgeGraph[dep.concept] < 60
    )

    return missingPrerequisites.length > 0
  }

  private async generateAdaptivePath(
    session: LearningSession,
    analysis: DependencyAnalysisResponse
  ): Promise<LearningPath> {
    const currentKnowledge = Object.keys(session.knowledgeGraph).filter(
      concept => session.knowledgeGraph[concept] >= 70
    )

    const missingKnowledge = analysis.dependencies.map(dep => dep.concept)

    return await pathPlanner.generateLearningPath({
      targetTopic: session.targetTopic,
      currentKnowledge,
      missingKnowledge,
      timeConstraints: {
        sessionLength: 60 // 默认60分钟会话
      }
    })
  }

  private async generateDirectHelp(
    session: LearningSession,
    difficulty: LearningDifficulty
  ): Promise<any> {
    const prompt = `用户在学习"${session.targetTopic}"时，对"${difficulty.concept}"感到困惑。

用户问题：${difficulty.userQuestion || '理解困难'}
上下文：${difficulty.context}

请提供：
1. 概念的简化解释
2. 具体的例子或类比
3. 学习建议和技巧
4. 相关的练习或思考题

请以易懂的方式回答，帮助用户克服这个困难。`

    const response = await llmService.chat({
      messages: [
        {
          role: 'system',
          content: '你是一个耐心的导师，擅长用简单易懂的方式解释复杂概念。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      maxTokens: 1500
    })

    return {
      explanation: response.content,
      concept: difficulty.concept,
      helpType: 'direct_explanation'
    }
  }

  private checkReturnCondition(session: LearningSession): boolean {
    // 检查是否所有阻塞概念都已掌握
    return session.blockedConcepts.every(concept => 
      session.knowledgeGraph[concept] && session.knowledgeGraph[concept] >= 70
    )
  }

  private calculateReadinessScore(session: LearningSession): number {
    const relevantConcepts = Object.keys(session.knowledgeGraph)
    if (relevantConcepts.length === 0) return 50

    const avgMastery = relevantConcepts.reduce((sum, concept) => 
      sum + (session.knowledgeGraph[concept] || 0), 0
    ) / relevantConcepts.length

    return Math.round(avgMastery)
  }

  private findStepInPath(path: LearningPath | null, stepId: string): LearningStep | null {
    if (!path) return null
    return path.steps.find(step => step.id === stepId) || null
  }

  private getNextStep(path: LearningPath | null, currentStepId: string): LearningStep | null {
    if (!path) return null
    const currentIndex = path.steps.findIndex(step => step.id === currentStepId)
    return currentIndex >= 0 && currentIndex < path.steps.length - 1 ? 
      path.steps[currentIndex + 1] : null
  }

  private calculatePathProgress(path: LearningPath | null, currentStepId: string): number {
    if (!path) return 0
    const currentIndex = path.steps.findIndex(step => step.id === currentStepId)
    return currentIndex >= 0 ? Math.round(((currentIndex + 1) / path.steps.length) * 100) : 0
  }

  private generateEncouragement(masteryLevel: number): string {
    if (masteryLevel >= 80) return '太棒了！您掌握得很好！'
    if (masteryLevel >= 60) return '不错的进步！继续保持！'
    if (masteryLevel >= 40) return '您正在进步，继续努力！'
    return '没关系，学习需要时间，让我们再试一次！'
  }

  private parseRecommendationResponse(content: string): any {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0])
      }
    } catch (error) {
      console.warn('解析建议响应失败:', error)
    }

    return {
      assessment: '继续当前的学习进度',
      nextSteps: ['继续当前学习'],
      risks: [],
      optimizations: []
    }
  }
}

// 导出单例实例
export const adaptiveLearning = new AdaptiveLearningService()
