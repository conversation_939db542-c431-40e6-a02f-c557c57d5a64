/**
 * LLM配置管理
 * 支持多种LLM提供商和模型的配置
 */

export interface LLMConfig {
  provider: 'groq' | 'moonshot' | 'openai' | 'anthropic'
  apiKey: string
  apiBase?: string
  model: string
  maxTokens?: number
  temperature?: number
  timeout?: number
}

export interface LLMProviderConfig {
  name: string
  apiBase: string
  defaultModel: string
  supportedModels: string[]
  headers?: Record<string, string>
}

/**
 * 支持的LLM提供商配置
 */
export const LLM_PROVIDERS: Record<string, LLMProviderConfig> = {
  groq: {
    name: 'Groq',
    apiBase: 'https://api.groq.com/openai/v1',
    defaultModel: 'llama-3.1-70b-versatile',
    supportedModels: [
      'llama-3.1-70b-versatile',
      'llama-3.1-8b-instant',
      'mixtral-8x7b-32768',
      'gemma2-9b-it'
    ]
  },
  moonshot: {
    name: 'Moonshot AI',
    apiBase: 'https://api.moonshot.cn/v1',
    defaultModel: 'moonshot-v1-8k',
    supportedModels: [
      'moonshot-v1-8k',
      'moonshot-v1-32k',
      'moonshot-v1-128k',
      'moonshotai/kimi-k2-instruct'
    ]
  },
  openai: {
    name: 'OpenAI',
    apiBase: 'https://api.openai.com/v1',
    defaultModel: 'gpt-4o-mini',
    supportedModels: [
      'gpt-4o',
      'gpt-4o-mini',
      'gpt-4-turbo',
      'gpt-3.5-turbo'
    ]
  }
}

/**
 * 默认LLM配置
 */
export const DEFAULT_LLM_CONFIG: LLMConfig = {
  provider: 'moonshot',
  apiKey: process.env.MOONSHOT_API_KEY || '',
  apiBase: LLM_PROVIDERS.moonshot.apiBase,
  model: 'moonshotai/kimi-k2-instruct',
  maxTokens: 4000,
  temperature: 0.7,
  timeout: 30000
}

/**
 * 从环境变量获取LLM配置
 */
export function getLLMConfigFromEnv(): LLMConfig {
  // 优先使用Groq配置（如果可用）
  if (process.env.GROQ_API_KEY) {
    return {
      provider: 'groq',
      apiKey: process.env.GROQ_API_KEY,
      apiBase: process.env.GROQ_API_BASE || LLM_PROVIDERS.groq.apiBase,
      model: process.env.GROQ_MODEL || LLM_PROVIDERS.groq.defaultModel,
      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
      timeout: parseInt(process.env.LLM_TIMEOUT || '30000')
    }
  }

  // 使用Moonshot配置
  if (process.env.MOONSHOT_API_KEY) {
    return {
      provider: 'moonshot',
      apiKey: process.env.MOONSHOT_API_KEY,
      apiBase: process.env.MOONSHOT_API_BASE || LLM_PROVIDERS.moonshot.apiBase,
      model: process.env.MOONSHOT_MODEL || 'moonshotai/kimi-k2-instruct',
      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
      timeout: parseInt(process.env.LLM_TIMEOUT || '30000')
    }
  }

  // 使用OpenAI配置
  if (process.env.OPENAI_API_KEY) {
    return {
      provider: 'openai',
      apiKey: process.env.OPENAI_API_KEY,
      apiBase: process.env.OPENAI_API_BASE || LLM_PROVIDERS.openai.apiBase,
      model: process.env.OPENAI_MODEL || LLM_PROVIDERS.openai.defaultModel,
      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
      timeout: parseInt(process.env.LLM_TIMEOUT || '30000')
    }
  }

  // 返回默认配置（开发模式）
  return {
    ...DEFAULT_LLM_CONFIG,
    apiKey: 'mock-api-key' // 开发模式下的模拟key
  }
}

/**
 * 验证LLM配置
 */
export function validateLLMConfig(config: LLMConfig): boolean {
  if (!config.apiKey || config.apiKey === 'mock-api-key') {
    console.warn('LLM API Key未配置，将使用模拟模式')
    return false
  }

  if (!config.model) {
    console.error('LLM模型未指定')
    return false
  }

  const provider = LLM_PROVIDERS[config.provider]
  if (!provider) {
    console.error(`不支持的LLM提供商: ${config.provider}`)
    return false
  }

  if (!provider.supportedModels.includes(config.model)) {
    console.warn(`模型 ${config.model} 可能不被 ${provider.name} 支持`)
  }

  return true
}

/**
 * 获取当前LLM配置
 */
export function getCurrentLLMConfig(): LLMConfig {
  const config = getLLMConfigFromEnv()
  
  if (!validateLLMConfig(config)) {
    console.log('使用模拟LLM配置进行开发')
  }

  return config
}
