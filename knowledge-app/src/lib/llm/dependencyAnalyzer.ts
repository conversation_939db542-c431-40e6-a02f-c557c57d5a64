/**
 * 依赖知识分析服务
 * 当用户遇到不懂的概念时，分析并识别需要的前置知识
 */

import { llmService } from './service'
import { 
  DependencyAnalysisRequest,
  DependencyAnalysisResponse,
  AnalyzedDependency,
  LLMMessage 
} from './types'

/**
 * 依赖分析服务类
 */
export class DependencyAnalyzerService {
  
  /**
   * 分析知识依赖
   */
  async analyzeDependencies(request: DependencyAnalysisRequest): Promise<DependencyAnalysisResponse> {
    const prompt = this.buildDependencyAnalysisPrompt(request)
    
    try {
      const response = await llmService.chat({
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3, // 较低的温度确保分析的准确性
        maxTokens: 3000
      })

      return this.parseDependencyResponse(response.content, request)
    } catch (error) {
      console.error('依赖分析失败:', error)
      throw new Error('无法分析知识依赖，请稍后重试')
    }
  }

  /**
   * 快速检查概念依赖
   */
  async quickDependencyCheck(
    currentTopic: string, 
    unknownConcept: string
  ): Promise<AnalyzedDependency[]> {
    const prompt = `用户正在学习"${currentTopic}"，但不理解"${unknownConcept}"这个概念。

请分析学习"${unknownConcept}"需要的前置知识，要求：
1. 列出3-5个最重要的前置概念
2. 按重要性和学习顺序排序
3. 估算每个概念的学习时间和难度
4. 提供简短的概念描述

请以JSON数组格式返回，每个依赖包含：concept, importance(1-10), difficulty(1-10), description, estimatedTime(分钟)`

    try {
      const response = await llmService.chat({
        messages: [
          {
            role: 'system',
            content: '你是一个专业的学习顾问，擅长分析知识结构和学习依赖关系。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        maxTokens: 2000
      })

      return this.parseQuickDependencyResponse(response.content)
    } catch (error) {
      console.error('快速依赖检查失败:', error)
      return []
    }
  }

  /**
   * 评估学习准备度
   */
  async assessLearningReadiness(
    targetTopic: string,
    currentKnowledge: string[],
    userLevel: string = 'beginner'
  ): Promise<{
    readiness: number // 0-100
    missingConcepts: string[]
    recommendations: string[]
    estimatedPrepTime: number
  }> {
    const prompt = `评估用户学习"${targetTopic}"的准备度：

用户当前掌握的知识：
${currentKnowledge.map(k => `- ${k}`).join('\n')}

用户水平：${userLevel}

请分析：
1. 学习准备度（0-100分）
2. 缺失的关键概念
3. 学习建议
4. 预计准备时间（分钟）

请以JSON格式返回：{readiness, missingConcepts, recommendations, estimatedPrepTime}`

    try {
      const response = await llmService.chat({
        messages: [
          {
            role: 'system',
            content: '你是一个专业的学习评估专家，能够准确评估学习者的知识准备度。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        maxTokens: 1500
      })

      return this.parseReadinessResponse(response.content)
    } catch (error) {
      console.error('学习准备度评估失败:', error)
      return {
        readiness: 50,
        missingConcepts: [],
        recommendations: ['建议先掌握基础概念'],
        estimatedPrepTime: 60
      }
    }
  }

  /**
   * 构建依赖分析提示词
   */
  private buildDependencyAnalysisPrompt(request: DependencyAnalysisRequest): string {
    const { currentTopic, unknownConcepts, userContext, currentLevel } = request

    let prompt = `用户正在学习"${currentTopic}"，但遇到了以下不理解的概念：
${unknownConcepts.map(concept => `- ${concept}`).join('\n')}

${userContext ? `用户背景：${userContext}` : ''}
${currentLevel ? `当前水平：${currentLevel}` : ''}

请进行详细的依赖分析：

1. 对每个不理解的概念，分析其前置知识要求
2. 识别概念之间的依赖关系
3. 按学习优先级排序
4. 估算学习时间和难度
5. 提供学习建议和资源

要求：
- 分析要准确、实用
- 考虑概念的重要性和学习顺序
- 提供具体的学习路径
- 估算合理的时间投入

请以结构化格式返回分析结果。`

    return prompt
  }

  /**
   * 获取系统提示词
   */
  private getSystemPrompt(): string {
    return `你是一个专业的知识结构分析专家，具有以下能力：

1. 深度理解各学科的知识体系和依赖关系
2. 准确识别学习某个概念所需的前置知识
3. 合理评估学习难度和时间投入
4. 提供实用的学习路径和建议

你的分析应该：
- 基于教育学和认知科学原理
- 考虑学习者的认知负荷
- 提供循序渐进的学习路径
- 注重实际应用和理解深度

请始终以学习者的最佳利益为出发点，提供准确、实用的分析结果。`
  }

  /**
   * 解析依赖分析响应
   */
  private parseDependencyResponse(
    content: string, 
    request: DependencyAnalysisRequest
  ): DependencyAnalysisResponse {
    try {
      // 尝试解析JSON格式
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        return this.normalizeDependencyResponse(parsed)
      }

      // 解析文本格式
      return this.parseTextDependencyResponse(content, request)
    } catch (error) {
      console.warn('解析依赖分析响应失败，使用文本解析:', error)
      return this.parseTextDependencyResponse(content, request)
    }
  }

  /**
   * 解析文本格式的依赖分析响应
   */
  private parseTextDependencyResponse(
    content: string, 
    request: DependencyAnalysisRequest
  ): DependencyAnalysisResponse {
    const dependencies: AnalyzedDependency[] = []
    
    // 为每个未知概念创建基础依赖项
    request.unknownConcepts.forEach((concept, index) => {
      dependencies.push({
        concept,
        importance: 8 - index, // 按顺序递减重要性
        difficulty: 5,
        description: `学习${concept}的相关知识`,
        estimatedTime: 30,
        learningPath: [`理解${concept}的基本概念`, `掌握${concept}的应用`],
        resources: [`${concept}相关教程`, `${concept}实践案例`]
      })
    })

    return {
      dependencies,
      recommendedOrder: request.unknownConcepts,
      totalEstimatedTime: dependencies.reduce((sum, dep) => sum + (dep.estimatedTime || 0), 0),
      explanation: '基于文本分析生成的学习建议，建议按照概念的基础程度逐步学习。'
    }
  }

  /**
   * 解析快速依赖检查响应
   */
  private parseQuickDependencyResponse(content: string): AnalyzedDependency[] {
    try {
      const jsonMatch = content.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        return parsed.map((item: any) => this.normalizeAnalyzedDependency(item))
      }
    } catch (error) {
      console.warn('解析快速依赖检查响应失败:', error)
    }

    return []
  }

  /**
   * 解析学习准备度响应
   */
  private parseReadinessResponse(content: string): {
    readiness: number
    missingConcepts: string[]
    recommendations: string[]
    estimatedPrepTime: number
  } {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        return {
          readiness: parseInt(parsed.readiness) || 50,
          missingConcepts: Array.isArray(parsed.missingConcepts) ? parsed.missingConcepts : [],
          recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations : [],
          estimatedPrepTime: parseInt(parsed.estimatedPrepTime) || 60
        }
      }
    } catch (error) {
      console.warn('解析学习准备度响应失败:', error)
    }

    return {
      readiness: 50,
      missingConcepts: [],
      recommendations: ['建议先掌握基础概念'],
      estimatedPrepTime: 60
    }
  }

  /**
   * 标准化依赖分析响应
   */
  private normalizeDependencyResponse(obj: any): DependencyAnalysisResponse {
    return {
      dependencies: Array.isArray(obj.dependencies) ? 
        obj.dependencies.map((dep: any) => this.normalizeAnalyzedDependency(dep)) : [],
      recommendedOrder: Array.isArray(obj.recommendedOrder) ? obj.recommendedOrder : [],
      totalEstimatedTime: parseInt(obj.totalEstimatedTime) || 0,
      explanation: obj.explanation || '依赖分析完成'
    }
  }

  /**
   * 标准化分析依赖对象
   */
  private normalizeAnalyzedDependency(obj: any): AnalyzedDependency {
    return {
      concept: obj.concept || obj.name || '未知概念',
      importance: parseInt(obj.importance) || 5,
      difficulty: parseInt(obj.difficulty) || 5,
      description: obj.description || obj.desc || '',
      estimatedTime: parseInt(obj.estimatedTime || obj.time) || 30,
      learningPath: Array.isArray(obj.learningPath) ? obj.learningPath : [],
      resources: Array.isArray(obj.resources) ? obj.resources : []
    }
  }
}

// 导出单例实例
export const dependencyAnalyzer = new DependencyAnalyzerService()
