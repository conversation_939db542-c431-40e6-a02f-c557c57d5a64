/**
 * 学习路径规划服务
 * 基于知识依赖分析，智能规划最优的学习路径和顺序
 */

import { llmService } from './service'
import { dependencyAnalyzer } from './dependencyAnalyzer'
import { 
  LearningPathRequest,
  LearningPath,
  LearningStep,
  DependencyAnalysisRequest
} from './types'

/**
 * 学习路径规划服务类
 */
export class PathPlannerService {
  
  /**
   * 生成完整的学习路径
   */
  async generateLearningPath(request: LearningPathRequest): Promise<LearningPath> {
    try {
      // 1. 分析知识依赖
      const dependencyAnalysis = await this.analyzeLearningDependencies(request)
      
      // 2. 生成学习步骤
      const steps = await this.generateLearningSteps(request, dependencyAnalysis)
      
      // 3. 优化学习路径
      const optimizedSteps = this.optimizeLearningPath(steps, request)
      
      // 4. 生成里程碑
      const milestones = this.generateMilestones(optimizedSteps)
      
      return {
        id: this.generatePathId(),
        title: `${request.targetTopic} 学习路径`,
        description: this.generatePathDescription(request),
        totalTime: optimizedSteps.reduce((sum, step) => sum + step.estimatedTime, 0),
        difficulty: this.calculateOverallDifficulty(optimizedSteps),
        steps: optimizedSteps,
        milestones
      }
    } catch (error) {
      console.error('学习路径生成失败:', error)
      throw new Error('无法生成学习路径，请稍后重试')
    }
  }

  /**
   * 生成自适应学习路径
   * 根据用户的学习进度动态调整
   */
  async generateAdaptivePath(
    targetTopic: string,
    currentProgress: { [concept: string]: number }, // 概念 -> 掌握程度(0-100)
    learningHistory: string[], // 已学习的概念
    timeConstraints?: { totalTime?: number; sessionLength?: number }
  ): Promise<LearningPath> {
    const prompt = this.buildAdaptivePathPrompt(
      targetTopic, 
      currentProgress, 
      learningHistory, 
      timeConstraints
    )
    
    try {
      const response = await llmService.chat({
        messages: [
          {
            role: 'system',
            content: this.getAdaptiveSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.4,
        maxTokens: 4000
      })

      return this.parseAdaptivePathResponse(response.content, targetTopic)
    } catch (error) {
      console.error('自适应路径生成失败:', error)
      throw new Error('无法生成自适应学习路径')
    }
  }

  /**
   * 优化现有学习路径
   */
  async optimizeExistingPath(
    currentPath: LearningPath,
    userFeedback: {
      completedSteps: string[]
      strugglingConcepts: string[]
      timeSpent: { [stepId: string]: number }
      preferences: any
    }
  ): Promise<LearningPath> {
    const prompt = `基于用户反馈优化学习路径：

当前路径：${currentPath.title}
已完成步骤：${userFeedback.completedSteps.join(', ')}
困难概念：${userFeedback.strugglingConcepts.join(', ')}

请优化路径，调整：
1. 困难概念的学习方法
2. 步骤的顺序和时间分配
3. 添加必要的复习和练习
4. 提供替代学习方案

请以JSON格式返回优化后的学习路径。`

    try {
      const response = await llmService.chat({
        messages: [
          {
            role: 'system',
            content: '你是一个专业的学习路径优化专家，能够根据用户反馈调整学习计划。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        maxTokens: 3000
      })

      return this.parseOptimizedPathResponse(response.content, currentPath)
    } catch (error) {
      console.error('路径优化失败:', error)
      return currentPath // 返回原路径
    }
  }

  /**
   * 分析学习依赖关系
   */
  private async analyzeLearningDependencies(request: LearningPathRequest) {
    const dependencyRequest: DependencyAnalysisRequest = {
      currentTopic: request.targetTopic,
      unknownConcepts: request.missingKnowledge,
      userContext: `当前掌握：${request.currentKnowledge.join(', ')}`,
      currentLevel: this.inferUserLevel(request)
    }

    return await dependencyAnalyzer.analyzeDependencies(dependencyRequest)
  }

  /**
   * 生成学习步骤
   */
  private async generateLearningSteps(
    request: LearningPathRequest,
    dependencyAnalysis: any
  ): Promise<LearningStep[]> {
    const prompt = `基于依赖分析结果，为"${request.targetTopic}"生成详细的学习步骤：

目标：${request.targetTopic}
当前知识：${request.currentKnowledge.join(', ')}
缺失知识：${request.missingKnowledge.join(', ')}
依赖分析：${JSON.stringify(dependencyAnalysis, null, 2)}

要求：
1. 创建循序渐进的学习步骤
2. 每个步骤包含明确的学习目标
3. 估算合理的学习时间
4. 标注步骤类型（概念/练习/项目/复习）
5. 定义前置要求

请以JSON数组格式返回学习步骤。`

    try {
      const response = await llmService.chat({
        messages: [
          {
            role: 'system',
            content: '你是一个专业的课程设计专家，擅长创建结构化的学习步骤。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.5,
        maxTokens: 3000
      })

      return this.parseLearningStepsResponse(response.content)
    } catch (error) {
      console.error('学习步骤生成失败:', error)
      return this.generateDefaultSteps(request)
    }
  }

  /**
   * 优化学习路径
   */
  private optimizeLearningPath(
    steps: LearningStep[], 
    request: LearningPathRequest
  ): LearningStep[] {
    // 根据时间约束调整
    if (request.timeConstraints?.totalTime) {
      steps = this.adjustForTimeConstraints(steps, request.timeConstraints.totalTime)
    }

    // 根据学习偏好调整
    if (request.preferences?.difficulty === 'gradual') {
      steps = this.sortByDifficultyGradual(steps)
    }

    // 添加项目实践
    if (request.preferences?.includeProjects) {
      steps = this.addProjectSteps(steps)
    }

    return steps
  }

  /**
   * 生成学习里程碑
   */
  private generateMilestones(steps: LearningStep[]): string[] {
    const milestones: string[] = []
    const totalSteps = steps.length
    
    // 每25%进度设置一个里程碑
    for (let i = 1; i <= 4; i++) {
      const milestoneIndex = Math.floor((totalSteps * i) / 4) - 1
      if (milestoneIndex >= 0 && milestoneIndex < totalSteps) {
        milestones.push(`完成${steps[milestoneIndex].title}`)
      }
    }

    return milestones
  }

  /**
   * 构建自适应路径提示词
   */
  private buildAdaptivePathPrompt(
    targetTopic: string,
    currentProgress: { [concept: string]: number },
    learningHistory: string[],
    timeConstraints?: any
  ): string {
    return `为用户生成自适应学习路径：

目标主题：${targetTopic}

当前进度：
${Object.entries(currentProgress).map(([concept, progress]) => 
  `- ${concept}: ${progress}%`
).join('\n')}

学习历史：${learningHistory.join(', ')}

${timeConstraints ? `时间约束：${JSON.stringify(timeConstraints)}` : ''}

请生成一个自适应的学习路径，要求：
1. 基于当前进度调整学习重点
2. 避免重复已掌握的内容
3. 强化薄弱环节
4. 提供个性化的学习建议

请以JSON格式返回完整的学习路径。`
  }

  /**
   * 获取自适应系统提示词
   */
  private getAdaptiveSystemPrompt(): string {
    return `你是一个智能学习路径规划专家，具有以下能力：

1. 分析学习者的当前状态和进度
2. 识别知识盲点和薄弱环节
3. 设计个性化的学习路径
4. 动态调整学习策略和重点
5. 优化学习效率和效果

你的规划应该：
- 基于学习科学和认知心理学原理
- 考虑个体差异和学习偏好
- 提供可操作的具体步骤
- 包含适当的复习和巩固机制
- 设置合理的学习目标和里程碑`
  }

  /**
   * 解析自适应路径响应
   */
  private parseAdaptivePathResponse(content: string, targetTopic: string): LearningPath {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        return this.normalizeLearningPath(parsed, targetTopic)
      }
    } catch (error) {
      console.warn('解析自适应路径响应失败:', error)
    }

    // 返回默认路径
    return this.generateDefaultPath(targetTopic)
  }

  /**
   * 解析优化路径响应
   */
  private parseOptimizedPathResponse(content: string, originalPath: LearningPath): LearningPath {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        return this.normalizeLearningPath(parsed, originalPath.title)
      }
    } catch (error) {
      console.warn('解析优化路径响应失败:', error)
    }

    return originalPath
  }

  /**
   * 解析学习步骤响应
   */
  private parseLearningStepsResponse(content: string): LearningStep[] {
    try {
      const jsonMatch = content.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        return parsed.map((step: any) => this.normalizeLearningStep(step))
      }
    } catch (error) {
      console.warn('解析学习步骤响应失败:', error)
    }

    return []
  }

  /**
   * 标准化学习路径对象
   */
  private normalizeLearningPath(obj: any, title: string): LearningPath {
    return {
      id: obj.id || this.generatePathId(),
      title: obj.title || title,
      description: obj.description || '',
      totalTime: parseInt(obj.totalTime) || 0,
      difficulty: parseInt(obj.difficulty) || 5,
      steps: Array.isArray(obj.steps) ? 
        obj.steps.map((step: any) => this.normalizeLearningStep(step)) : [],
      milestones: Array.isArray(obj.milestones) ? obj.milestones : []
    }
  }

  /**
   * 标准化学习步骤对象
   */
  private normalizeLearningStep(obj: any): LearningStep {
    return {
      id: obj.id || this.generateStepId(),
      title: obj.title || obj.name || '学习步骤',
      description: obj.description || obj.desc || '',
      type: obj.type || 'concept',
      estimatedTime: parseInt(obj.estimatedTime || obj.time) || 30,
      difficulty: parseInt(obj.difficulty) || 5,
      prerequisites: Array.isArray(obj.prerequisites) ? obj.prerequisites : [],
      resources: Array.isArray(obj.resources) ? obj.resources : [],
      objectives: Array.isArray(obj.objectives) ? obj.objectives : []
    }
  }

  /**
   * 生成默认学习步骤
   */
  private generateDefaultSteps(request: LearningPathRequest): LearningStep[] {
    return [
      {
        id: this.generateStepId(),
        title: `${request.targetTopic} 基础概念`,
        description: `学习${request.targetTopic}的基本概念和原理`,
        type: 'concept',
        estimatedTime: 60,
        difficulty: 3,
        prerequisites: [],
        objectives: [`理解${request.targetTopic}的基本概念`]
      },
      {
        id: this.generateStepId(),
        title: `${request.targetTopic} 实践练习`,
        description: `通过练习巩固${request.targetTopic}的知识`,
        type: 'practice',
        estimatedTime: 90,
        difficulty: 5,
        prerequisites: [`${request.targetTopic} 基础概念`],
        objectives: [`掌握${request.targetTopic}的实际应用`]
      }
    ]
  }

  /**
   * 生成默认学习路径
   */
  private generateDefaultPath(targetTopic: string): LearningPath {
    const steps = [
      {
        id: this.generateStepId(),
        title: `${targetTopic} 入门`,
        description: `${targetTopic}的基础学习`,
        type: 'concept' as const,
        estimatedTime: 60,
        difficulty: 3,
        prerequisites: [],
        objectives: [`了解${targetTopic}基础`]
      }
    ]

    return {
      id: this.generatePathId(),
      title: `${targetTopic} 学习路径`,
      description: `系统学习${targetTopic}的完整路径`,
      totalTime: 60,
      difficulty: 3,
      steps,
      milestones: [`完成${targetTopic}入门`]
    }
  }

  /**
   * 辅助方法
   */
  private generatePathId(): string {
    return `path_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateStepId(): string {
    return `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generatePathDescription(request: LearningPathRequest): string {
    return `为学习${request.targetTopic}量身定制的学习路径，基于您当前的知识水平和学习目标。`
  }

  private calculateOverallDifficulty(steps: LearningStep[]): number {
    if (steps.length === 0) return 5
    const avgDifficulty = steps.reduce((sum, step) => sum + step.difficulty, 0) / steps.length
    return Math.round(avgDifficulty)
  }

  private inferUserLevel(request: LearningPathRequest): string {
    const knowledgeCount = request.currentKnowledge.length
    if (knowledgeCount === 0) return 'beginner'
    if (knowledgeCount < 5) return 'beginner'
    if (knowledgeCount < 15) return 'intermediate'
    return 'advanced'
  }

  private adjustForTimeConstraints(steps: LearningStep[], totalTime: number): LearningStep[] {
    const currentTotal = steps.reduce((sum, step) => sum + step.estimatedTime, 0)
    if (currentTotal <= totalTime) return steps

    const ratio = totalTime / currentTotal
    return steps.map(step => ({
      ...step,
      estimatedTime: Math.round(step.estimatedTime * ratio)
    }))
  }

  private sortByDifficultyGradual(steps: LearningStep[]): LearningStep[] {
    return [...steps].sort((a, b) => a.difficulty - b.difficulty)
  }

  private addProjectSteps(steps: LearningStep[]): LearningStep[] {
    // 在适当位置插入项目步骤
    const projectStep: LearningStep = {
      id: this.generateStepId(),
      title: '综合项目实践',
      description: '通过实际项目巩固所学知识',
      type: 'project',
      estimatedTime: 120,
      difficulty: 7,
      prerequisites: steps.slice(0, Math.floor(steps.length * 0.7)).map(s => s.title),
      objectives: ['应用所学知识解决实际问题']
    }

    const insertIndex = Math.floor(steps.length * 0.8)
    const result = [...steps]
    result.splice(insertIndex, 0, projectStep)
    return result
  }
}

// 导出单例实例
export const pathPlanner = new PathPlannerService()
