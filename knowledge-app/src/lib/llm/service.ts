/**
 * LLM服务实现
 * 支持多种LLM提供商的统一接口
 */

import { LLMConfig, LLM_PROVIDERS, getCurrentLLMConfig } from './config'
import { 
  LLMService, 
  LLMMessage, 
  LLMResponse, 
  LLMStreamResponse, 
  LLMRequestOptions, 
  LLMError 
} from './types'

/**
 * 基础LLM服务实现
 */
export class BaseLLMService implements LLMService {
  protected config: LLMConfig

  constructor(config?: LLMConfig) {
    this.config = config || getCurrentLLMConfig()
  }

  /**
   * 发送聊天请求
   */
  async chat(options: LLMRequestOptions): Promise<LLMResponse> {
    // 如果是模拟模式，返回模拟响应
    if (this.config.apiKey === 'mock-api-key') {
      return this.getMockResponse(options)
    }

    try {
      const response = await this.makeRequest(options)
      return this.parseResponse(response)
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * 发送流式聊天请求
   */
  async* chatStream(options: LLMRequestOptions): AsyncGenerator<LLMStreamResponse, void, unknown> {
    // 如果是模拟模式，返回模拟流响应
    if (this.config.apiKey === 'mock-api-key') {
      yield* this.getMockStreamResponse(options)
      return
    }

    try {
      const response = await this.makeStreamRequest(options)
      yield* this.parseStreamResponse(response)
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * 检查服务是否可用
   */
  async isAvailable(): Promise<boolean> {
    if (this.config.apiKey === 'mock-api-key') {
      return true // 模拟模式总是可用
    }

    try {
      const testResponse = await this.chat({
        messages: [{ role: 'user', content: 'Hello' }],
        maxTokens: 10
      })
      return !!testResponse.content
    } catch (error) {
      console.warn('LLM服务不可用:', error)
      return false
    }
  }

  /**
   * 获取支持的模型列表
   */
  getSupportedModels(): string[] {
    const provider = LLM_PROVIDERS[this.config.provider]
    return provider ? provider.supportedModels : []
  }

  /**
   * 获取服务提供商名称
   */
  getProviderName(): string {
    const provider = LLM_PROVIDERS[this.config.provider]
    return provider ? provider.name : this.config.provider
  }

  /**
   * 发送HTTP请求到LLM API
   */
  protected async makeRequest(options: LLMRequestOptions): Promise<Response> {
    const provider = LLM_PROVIDERS[this.config.provider]
    const url = `${this.config.apiBase || provider.apiBase}/chat/completions`

    const requestBody = {
      model: this.config.model,
      messages: options.messages,
      max_tokens: options.maxTokens || this.config.maxTokens,
      temperature: options.temperature ?? this.config.temperature,
      stream: false,
      ...(options.stop && { stop: options.stop }),
      ...(options.topP && { top_p: options.topP }),
      ...(options.frequencyPenalty && { frequency_penalty: options.frequencyPenalty }),
      ...(options.presencePenalty && { presence_penalty: options.presencePenalty })
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        ...provider.headers
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return response
  }

  /**
   * 发送流式HTTP请求到LLM API
   */
  protected async makeStreamRequest(options: LLMRequestOptions): Promise<Response> {
    const provider = LLM_PROVIDERS[this.config.provider]
    const url = `${this.config.apiBase || provider.apiBase}/chat/completions`

    const requestBody = {
      model: this.config.model,
      messages: options.messages,
      max_tokens: options.maxTokens || this.config.maxTokens,
      temperature: options.temperature ?? this.config.temperature,
      stream: true,
      ...(options.stop && { stop: options.stop }),
      ...(options.topP && { top_p: options.topP }),
      ...(options.frequencyPenalty && { frequency_penalty: options.frequencyPenalty }),
      ...(options.presencePenalty && { presence_penalty: options.presencePenalty })
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        ...provider.headers
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return response
  }

  /**
   * 解析LLM响应
   */
  protected async parseResponse(response: Response): Promise<LLMResponse> {
    const data = await response.json()
    
    return {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage,
      model: data.model,
      finish_reason: data.choices[0]?.finish_reason
    }
  }

  /**
   * 解析流式LLM响应
   */
  protected async* parseStreamResponse(response: Response): AsyncGenerator<LLMStreamResponse, void, unknown> {
    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法读取响应流')
    }

    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              return
            }

            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices[0]?.delta?.content || ''
              
              yield {
                content,
                done: false,
                usage: parsed.usage
              }
            } catch (error) {
              console.warn('解析流数据失败:', error)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    yield {
      content: '',
      done: true
    }
  }

  /**
   * 处理错误
   */
  protected handleError(error: any): LLMError {
    if (error.name === 'AbortError') {
      return {
        code: 'timeout',
        message: 'LLM请求超时',
        type: 'network_error',
        details: error
      }
    }

    if (error.message?.includes('401')) {
      return {
        code: 'unauthorized',
        message: 'API密钥无效或已过期',
        type: 'authentication_error',
        details: error
      }
    }

    if (error.message?.includes('429')) {
      return {
        code: 'rate_limit',
        message: 'API调用频率超限',
        type: 'rate_limit',
        details: error
      }
    }

    return {
      code: 'unknown',
      message: error.message || 'LLM服务错误',
      type: 'api_error',
      details: error
    }
  }

  /**
   * 获取模拟响应（开发模式）
   */
  protected getMockResponse(options: LLMRequestOptions): LLMResponse {
    const userMessage = options.messages.find(m => m.role === 'user')?.content || ''
    
    let mockContent = '这是一个模拟的LLM响应。'
    
    if (userMessage.includes('知识') || userMessage.includes('学习')) {
      mockContent = '基于您的请求，我建议从基础概念开始学习，然后逐步深入到高级主题。'
    } else if (userMessage.includes('依赖') || userMessage.includes('前置')) {
      mockContent = '分析您提到的概念，需要先掌握以下前置知识：1. 基础概念 2. 相关理论 3. 实践技能'
    }

    return {
      content: mockContent,
      usage: {
        prompt_tokens: userMessage.length,
        completion_tokens: mockContent.length,
        total_tokens: userMessage.length + mockContent.length
      },
      model: this.config.model,
      finish_reason: 'stop'
    }
  }

  /**
   * 获取模拟流响应（开发模式）
   */
  protected async* getMockStreamResponse(options: LLMRequestOptions): AsyncGenerator<LLMStreamResponse, void, unknown> {
    const response = this.getMockResponse(options)
    const words = response.content.split('')
    
    for (let i = 0; i < words.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 50)) // 模拟流式延迟
      
      yield {
        content: words[i],
        done: false
      }
    }

    yield {
      content: '',
      done: true,
      usage: response.usage
    }
  }
}

/**
 * 全局LLM服务实例
 */
export const llmService = new BaseLLMService()
