import { supabase } from './supabase'
import { KnowledgePoint, KnowledgeDependency, UserProgress, LearningSession, TestQuestion } from '@/types'

// 开发模式下的模拟数据
const mockKnowledgePoints: KnowledgePoint[] = [
  {
    id: '1',
    name: '基础算术',
    description: '加减乘除的基本运算',
    difficulty_level: 1,
    tags: ['数学', '基础'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    user_id: 'mock-user'
  },
  {
    id: '2',
    name: 'JavaScript基础',
    description: 'JavaScript语法和基本概念',
    difficulty_level: 3,
    tags: ['编程', 'JavaScript'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    user_id: 'mock-user'
  }
]

const mockDependencies: KnowledgeDependency[] = [
  {
    id: 'dep1',
    prerequisite_id: '1', // 基础算术 -> JavaScript基础
    dependent_id: '2',
    created_at: new Date().toISOString()
  }
]
const mockProgress: UserProgress[] = [
  {
    id: 'progress1',
    user_id: 'mock-user',
    knowledge_point_id: '1',
    mastery_level: 85,
    notes: '基础算术已经掌握得很好',
    last_reviewed: new Date().toISOString(),
    test_scores: [80, 85, 90],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'progress2',
    user_id: 'mock-user',
    knowledge_point_id: '2',
    mastery_level: 45,
    notes: 'JavaScript基础还需要更多练习',
    last_reviewed: new Date().toISOString(),
    test_scores: [40, 45, 50],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
]

// 检查是否为开发模式
const isDevMode = !supabase

// Knowledge Points API
export const knowledgePointsAPI = {
  // Get all knowledge points for current user
  async getAll(): Promise<KnowledgePoint[]> {
    if (isDevMode) {
      return Promise.resolve([...mockKnowledgePoints])
    }

    const { data, error } = await supabase!
      .from('knowledge_points')
      .select('*')
      .order('created_at', { ascending: true })

    if (error) throw error
    return data || []
  },

  // Get knowledge point by ID
  async getById(id: string): Promise<KnowledgePoint | null> {
    const { data, error } = await supabase
      .from('knowledge_points')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  // Create new knowledge point
  async create(knowledgePoint: Omit<KnowledgePoint, 'id' | 'created_at' | 'updated_at'>): Promise<KnowledgePoint> {
    if (isDevMode) {
      const newPoint: KnowledgePoint = {
        ...knowledgePoint,
        id: Date.now().toString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      mockKnowledgePoints.push(newPoint)
      return Promise.resolve(newPoint)
    }

    const { data, error } = await supabase!
      .from('knowledge_points')
      .insert(knowledgePoint)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update knowledge point
  async update(id: string, updates: Partial<KnowledgePoint>): Promise<KnowledgePoint> {
    if (isDevMode) {
      const index = mockKnowledgePoints.findIndex(point => point.id === id)
      if (index === -1) throw new Error('知识点不存在')

      const updatedPoint = {
        ...mockKnowledgePoints[index],
        ...updates,
        updated_at: new Date().toISOString()
      }
      mockKnowledgePoints[index] = updatedPoint
      return Promise.resolve(updatedPoint)
    }

    const { data, error } = await supabase
      .from('knowledge_points')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete knowledge point
  async delete(id: string): Promise<void> {
    if (isDevMode) {
      const index = mockKnowledgePoints.findIndex(point => point.id === id)
      if (index === -1) throw new Error('知识点不存在')

      mockKnowledgePoints.splice(index, 1)
      // 同时删除相关的依赖关系
      const dependencyIndexes = []
      for (let i = mockDependencies.length - 1; i >= 0; i--) {
        if (mockDependencies[i].prerequisite_id === id || mockDependencies[i].dependent_id === id) {
          dependencyIndexes.push(i)
        }
      }
      dependencyIndexes.forEach(index => mockDependencies.splice(index, 1))
      return Promise.resolve()
    }

    const { error } = await supabase
      .from('knowledge_points')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  // Search knowledge points by name or tags
  async search(query: string): Promise<KnowledgePoint[]> {
    const { data, error } = await supabase
      .from('knowledge_points')
      .select('*')
      .or(`name.ilike.%${query}%, description.ilike.%${query}%`)
      .order('created_at', { ascending: true })
    
    if (error) throw error
    return data || []
  }
}

// Knowledge Dependencies API
export const dependenciesAPI = {
  // Get all dependencies
  async getAll(): Promise<KnowledgeDependency[]> {
    if (isDevMode) {
      return Promise.resolve([...mockDependencies])
    }

    const { data, error } = await supabase!
      .from('knowledge_dependencies')
      .select('*')
      .order('created_at', { ascending: true })

    if (error) throw error
    return data || []
  },

  // Get prerequisites for a knowledge point
  async getPrerequisites(knowledgePointId: string): Promise<KnowledgePoint[]> {
    const { data, error } = await supabase
      .from('knowledge_dependencies')
      .select(`
        prerequisite_id,
        knowledge_points!knowledge_dependencies_prerequisite_id_fkey(*)
      `)
      .eq('dependent_id', knowledgePointId)
    
    if (error) throw error
    return data?.map(item => item.knowledge_points).filter(Boolean) || []
  },

  // Get dependents for a knowledge point
  async getDependents(knowledgePointId: string): Promise<KnowledgePoint[]> {
    const { data, error } = await supabase
      .from('knowledge_dependencies')
      .select(`
        dependent_id,
        knowledge_points!knowledge_dependencies_dependent_id_fkey(*)
      `)
      .eq('prerequisite_id', knowledgePointId)
    
    if (error) throw error
    return data?.map(item => item.knowledge_points).filter(Boolean) || []
  },

  // Create dependency
  async create(prerequisiteId: string, dependentId: string): Promise<KnowledgeDependency> {
    if (isDevMode) {
      const newDependency: KnowledgeDependency = {
        id: Date.now().toString(),
        prerequisite_id: prerequisiteId,
        dependent_id: dependentId,
        created_at: new Date().toISOString()
      }
      mockDependencies.push(newDependency)
      return Promise.resolve(newDependency)
    }

    const { data, error } = await supabase!
      .from('knowledge_dependencies')
      .insert({
        prerequisite_id: prerequisiteId,
        dependent_id: dependentId
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete dependency
  async delete(prerequisiteId: string, dependentId: string): Promise<void> {
    if (isDevMode) {
      const index = mockDependencies.findIndex(dep =>
        dep.prerequisite_id === prerequisiteId && dep.dependent_id === dependentId
      )
      if (index !== -1) {
        mockDependencies.splice(index, 1)
      }
      return Promise.resolve()
    }

    const { error } = await supabase!
      .from('knowledge_dependencies')
      .delete()
      .eq('prerequisite_id', prerequisiteId)
      .eq('dependent_id', dependentId)

    if (error) throw error
  }
}

// User Progress API
export const progressAPI = {
  // Get all progress for current user
  async getAll(): Promise<UserProgress[]> {
    if (isDevMode) {
      return Promise.resolve([...mockProgress])
    }

    const { data, error } = await supabase!
      .from('user_progress')
      .select('*')
      .order('updated_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  // Get progress for specific knowledge point
  async getByKnowledgePoint(knowledgePointId: string): Promise<UserProgress | null> {
    const { data, error } = await supabase
      .from('user_progress')
      .select('*')
      .eq('knowledge_point_id', knowledgePointId)
      .single()
    
    if (error && error.code !== 'PGRST116') throw error // PGRST116 is "not found"
    return data
  },

  // Update or create progress
  async upsert(progress: Omit<UserProgress, 'id' | 'created_at' | 'updated_at'>): Promise<UserProgress> {
    const { data, error } = await supabase
      .from('user_progress')
      .upsert(progress, {
        onConflict: 'user_id,knowledge_point_id'
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete progress
  async delete(knowledgePointId: string): Promise<void> {
    const { error } = await supabase
      .from('user_progress')
      .delete()
      .eq('knowledge_point_id', knowledgePointId)
    
    if (error) throw error
  }
}

// Learning Sessions API
export const sessionsAPI = {
  // Get all sessions for current user
  async getAll(): Promise<LearningSession[]> {
    const { data, error } = await supabase
      .from('learning_sessions')
      .select('*')
      .order('start_time', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get sessions for specific knowledge point
  async getByKnowledgePoint(knowledgePointId: string): Promise<LearningSession[]> {
    const { data, error } = await supabase
      .from('learning_sessions')
      .select('*')
      .eq('knowledge_point_id', knowledgePointId)
      .order('start_time', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Create new session
  async create(session: Omit<LearningSession, 'id' | 'created_at'>): Promise<LearningSession> {
    const { data, error } = await supabase
      .from('learning_sessions')
      .insert(session)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update session
  async update(id: string, updates: Partial<LearningSession>): Promise<LearningSession> {
    const { data, error } = await supabase
      .from('learning_sessions')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}

// Test Questions API
export const questionsAPI = {
  // Get questions for knowledge point
  async getByKnowledgePoint(knowledgePointId: string): Promise<TestQuestion[]> {
    const { data, error } = await supabase
      .from('test_questions')
      .select('*')
      .eq('knowledge_point_id', knowledgePointId)
      .order('difficulty', { ascending: true })
    
    if (error) throw error
    return data || []
  },

  // Create new question
  async create(question: Omit<TestQuestion, 'id' | 'created_at'>): Promise<TestQuestion> {
    const { data, error } = await supabase
      .from('test_questions')
      .insert(question)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update question
  async update(id: string, updates: Partial<TestQuestion>): Promise<TestQuestion> {
    const { data, error } = await supabase
      .from('test_questions')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete question
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('test_questions')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}
