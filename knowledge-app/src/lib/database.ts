import { supabase } from './supabase'
import { KnowledgePoint, KnowledgeDependency, UserProgress, LearningSession, TestQuestion } from '@/types'

// Knowledge Points API
export const knowledgePointsAPI = {
  // Get all knowledge points for current user
  async getAll(): Promise<KnowledgePoint[]> {
    const { data, error } = await supabase
      .from('knowledge_points')
      .select('*')
      .order('created_at', { ascending: true })
    
    if (error) throw error
    return data || []
  },

  // Get knowledge point by ID
  async getById(id: string): Promise<KnowledgePoint | null> {
    const { data, error } = await supabase
      .from('knowledge_points')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  // Create new knowledge point
  async create(knowledgePoint: Omit<KnowledgePoint, 'id' | 'created_at' | 'updated_at'>): Promise<KnowledgePoint> {
    const { data, error } = await supabase
      .from('knowledge_points')
      .insert(knowledgePoint)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update knowledge point
  async update(id: string, updates: Partial<KnowledgePoint>): Promise<KnowledgePoint> {
    const { data, error } = await supabase
      .from('knowledge_points')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete knowledge point
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('knowledge_points')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  },

  // Search knowledge points by name or tags
  async search(query: string): Promise<KnowledgePoint[]> {
    const { data, error } = await supabase
      .from('knowledge_points')
      .select('*')
      .or(`name.ilike.%${query}%, description.ilike.%${query}%`)
      .order('created_at', { ascending: true })
    
    if (error) throw error
    return data || []
  }
}

// Knowledge Dependencies API
export const dependenciesAPI = {
  // Get all dependencies
  async getAll(): Promise<KnowledgeDependency[]> {
    const { data, error } = await supabase
      .from('knowledge_dependencies')
      .select('*')
      .order('created_at', { ascending: true })
    
    if (error) throw error
    return data || []
  },

  // Get prerequisites for a knowledge point
  async getPrerequisites(knowledgePointId: string): Promise<KnowledgePoint[]> {
    const { data, error } = await supabase
      .from('knowledge_dependencies')
      .select(`
        prerequisite_id,
        knowledge_points!knowledge_dependencies_prerequisite_id_fkey(*)
      `)
      .eq('dependent_id', knowledgePointId)
    
    if (error) throw error
    return data?.map(item => item.knowledge_points).filter(Boolean) || []
  },

  // Get dependents for a knowledge point
  async getDependents(knowledgePointId: string): Promise<KnowledgePoint[]> {
    const { data, error } = await supabase
      .from('knowledge_dependencies')
      .select(`
        dependent_id,
        knowledge_points!knowledge_dependencies_dependent_id_fkey(*)
      `)
      .eq('prerequisite_id', knowledgePointId)
    
    if (error) throw error
    return data?.map(item => item.knowledge_points).filter(Boolean) || []
  },

  // Create dependency
  async create(prerequisiteId: string, dependentId: string): Promise<KnowledgeDependency> {
    const { data, error } = await supabase
      .from('knowledge_dependencies')
      .insert({
        prerequisite_id: prerequisiteId,
        dependent_id: dependentId
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete dependency
  async delete(prerequisiteId: string, dependentId: string): Promise<void> {
    const { error } = await supabase
      .from('knowledge_dependencies')
      .delete()
      .eq('prerequisite_id', prerequisiteId)
      .eq('dependent_id', dependentId)
    
    if (error) throw error
  }
}

// User Progress API
export const progressAPI = {
  // Get all progress for current user
  async getAll(): Promise<UserProgress[]> {
    const { data, error } = await supabase
      .from('user_progress')
      .select('*')
      .order('updated_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get progress for specific knowledge point
  async getByKnowledgePoint(knowledgePointId: string): Promise<UserProgress | null> {
    const { data, error } = await supabase
      .from('user_progress')
      .select('*')
      .eq('knowledge_point_id', knowledgePointId)
      .single()
    
    if (error && error.code !== 'PGRST116') throw error // PGRST116 is "not found"
    return data
  },

  // Update or create progress
  async upsert(progress: Omit<UserProgress, 'id' | 'created_at' | 'updated_at'>): Promise<UserProgress> {
    const { data, error } = await supabase
      .from('user_progress')
      .upsert(progress, {
        onConflict: 'user_id,knowledge_point_id'
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete progress
  async delete(knowledgePointId: string): Promise<void> {
    const { error } = await supabase
      .from('user_progress')
      .delete()
      .eq('knowledge_point_id', knowledgePointId)
    
    if (error) throw error
  }
}

// Learning Sessions API
export const sessionsAPI = {
  // Get all sessions for current user
  async getAll(): Promise<LearningSession[]> {
    const { data, error } = await supabase
      .from('learning_sessions')
      .select('*')
      .order('start_time', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get sessions for specific knowledge point
  async getByKnowledgePoint(knowledgePointId: string): Promise<LearningSession[]> {
    const { data, error } = await supabase
      .from('learning_sessions')
      .select('*')
      .eq('knowledge_point_id', knowledgePointId)
      .order('start_time', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Create new session
  async create(session: Omit<LearningSession, 'id' | 'created_at'>): Promise<LearningSession> {
    const { data, error } = await supabase
      .from('learning_sessions')
      .insert(session)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update session
  async update(id: string, updates: Partial<LearningSession>): Promise<LearningSession> {
    const { data, error } = await supabase
      .from('learning_sessions')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}

// Test Questions API
export const questionsAPI = {
  // Get questions for knowledge point
  async getByKnowledgePoint(knowledgePointId: string): Promise<TestQuestion[]> {
    const { data, error } = await supabase
      .from('test_questions')
      .select('*')
      .eq('knowledge_point_id', knowledgePointId)
      .order('difficulty', { ascending: true })
    
    if (error) throw error
    return data || []
  },

  // Create new question
  async create(question: Omit<TestQuestion, 'id' | 'created_at'>): Promise<TestQuestion> {
    const { data, error } = await supabase
      .from('test_questions')
      .insert(question)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update question
  async update(id: string, updates: Partial<TestQuestion>): Promise<TestQuestion> {
    const { data, error } = await supabase
      .from('test_questions')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete question
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('test_questions')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}
