'use client'

import React, { useRef, useEffect, useState, useCallback } from 'react'
import dynamic from 'next/dynamic'
import { KnowledgeGraph, GraphNode, GraphLink } from '@/types'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { ZoomIn, ZoomOut, RotateCcw, Maximize2, Settings } from 'lucide-react'

// 动态导入ForceGraph2D以避免SSR问题
const ForceGraph2D = dynamic(() => import('react-force-graph-2d'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96 bg-gray-100 dark:bg-gray-800 rounded-lg">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-2"></div>
        <p className="text-gray-600 dark:text-gray-400">加载图谱可视化...</p>
      </div>
    </div>
  )
})

interface KnowledgeGraphVisualizationProps {
  graphData: KnowledgeGraph
  onNodeClick?: (node: GraphNode) => void
  onNodeHover?: (node: GraphNode | null) => void
  onLinkClick?: (link: GraphLink) => void
  className?: string
  height?: number
  width?: number
}

export function KnowledgeGraphVisualization({
  graphData,
  onNodeClick,
  onNodeHover,
  onLinkClick,
  className = '',
  height = 600,
  width
}: KnowledgeGraphVisualizationProps) {
  const fgRef = useRef<any>()
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null)
  const [hoveredNode, setHoveredNode] = useState<GraphNode | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [graphSettings, setGraphSettings] = useState({
    nodeSize: 8,
    linkWidth: 2,
    chargeStrength: -300,
    linkDistance: 100,
    showLabels: true,
    showArrows: true
  })

  // 节点颜色映射
  const getNodeColor = useCallback((node: GraphNode) => {
    if (selectedNode?.id === node.id) return '#4f46e5' // 选中状态 - 靛蓝色
    if (hoveredNode?.id === node.id) return '#7c3aed' // 悬停状态 - 紫色
    
    // 根据掌握程度着色
    const mastery = node.mastery_level || 0
    if (mastery >= 80) return '#10b981' // 已掌握 - 绿色
    if (mastery >= 60) return '#f59e0b' // 部分掌握 - 黄色
    if (mastery >= 40) return '#f97316' // 初步了解 - 橙色
    return '#ef4444' // 未学习 - 红色
  }, [selectedNode, hoveredNode])

  // 节点大小映射
  const getNodeSize = useCallback((node: GraphNode) => {
    const baseSize = graphSettings.nodeSize
    const difficultyMultiplier = 1 + (node.difficulty_level - 1) * 0.1
    return baseSize * difficultyMultiplier
  }, [graphSettings.nodeSize])

  // 链接颜色
  const getLinkColor = useCallback((link: GraphLink) => {
    return '#94a3b8' // 灰色
  }, [])

  // 处理节点点击
  const handleNodeClick = useCallback((node: any) => {
    setSelectedNode(node)
    onNodeClick?.(node)
  }, [onNodeClick])

  // 处理节点悬停 - 简化版本
  const handleNodeHover = useCallback((node: any) => {
    setHoveredNode(node)
    onNodeHover?.(node)
  }, [onNodeHover])

  // 处理链接点击
  const handleLinkClick = useCallback((link: any) => {
    onLinkClick?.(link)
  }, [onLinkClick])

  // 缩放控制
  const handleZoomIn = () => {
    if (fgRef.current) {
      const currentZoom = fgRef.current.zoom()
      fgRef.current.zoom(currentZoom * 1.2)
    }
  }

  const handleZoomOut = () => {
    if (fgRef.current) {
      const currentZoom = fgRef.current.zoom()
      fgRef.current.zoom(currentZoom * 0.8)
    }
  }

  const handleResetView = () => {
    if (fgRef.current) {
      fgRef.current.zoomToFit(400)
    }
  }

  const handleCenterGraph = () => {
    if (fgRef.current) {
      fgRef.current.centerAt(0, 0)
      fgRef.current.zoom(1)
    }
  }

  // 初始化图谱布局
  useEffect(() => {
    if (fgRef.current && graphData.nodes.length > 0) {
      // 延迟设置初始缩放，确保图谱已渲染
      setTimeout(() => {
        if (fgRef.current) {
          fgRef.current.zoomToFit(400, 50)
        }
      }, 1000)
    }
  }, [graphData.nodes.length])

  // 简化的节点渲染
  const nodeCanvasObject = useCallback((node: any, ctx: CanvasRenderingContext2D, globalScale: number) => {
    const label = node.name
    const fontSize = 12 / globalScale
    const nodeSize = getNodeSize(node)

    // 绘制节点圆圈
    ctx.beginPath()
    ctx.arc(node.x, node.y, nodeSize, 0, 2 * Math.PI, false)
    ctx.fillStyle = getNodeColor(node)
    ctx.fill()

    // 绘制边框
    ctx.strokeStyle = '#ffffff'
    ctx.lineWidth = 2 / globalScale
    ctx.stroke()

    // 绘制标签（如果启用）
    if (graphSettings.showLabels && globalScale > 0.5) {
      ctx.font = `${fontSize}px Sans-Serif`
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillStyle = '#374151'
      ctx.fillText(label, node.x, node.y + nodeSize + fontSize)
    }

    // 绘制掌握程度指示器
    if (node.mastery_level > 0) {
      const progressAngle = (node.mastery_level / 100) * 2 * Math.PI
      ctx.beginPath()
      ctx.arc(node.x, node.y, nodeSize + 2, -Math.PI / 2, -Math.PI / 2 + progressAngle, false)
      ctx.strokeStyle = '#10b981'
      ctx.lineWidth = 3 / globalScale
      ctx.stroke()
    }
  }, [getNodeColor, getNodeSize, graphSettings.showLabels])

  // 自定义链接渲染
  const linkCanvasObject = useCallback((link: any, ctx: CanvasRenderingContext2D, globalScale: number) => {
    const { source, target } = link
    
    // 绘制链接线
    ctx.beginPath()
    ctx.moveTo(source.x, source.y)
    ctx.lineTo(target.x, target.y)
    ctx.strokeStyle = getLinkColor(link)
    ctx.lineWidth = graphSettings.linkWidth / globalScale
    ctx.stroke()
    
    // 绘制箭头（如果启用）
    if (graphSettings.showArrows) {
      const arrowLength = 10 / globalScale
      const arrowAngle = Math.PI / 6
      
      const angle = Math.atan2(target.y - source.y, target.x - source.x)
      const nodeRadius = getNodeSize(target)
      
      // 箭头起点（在目标节点边缘）
      const arrowStartX = target.x - Math.cos(angle) * nodeRadius
      const arrowStartY = target.y - Math.sin(angle) * nodeRadius
      
      // 绘制箭头
      ctx.beginPath()
      ctx.moveTo(arrowStartX, arrowStartY)
      ctx.lineTo(
        arrowStartX - arrowLength * Math.cos(angle - arrowAngle),
        arrowStartY - arrowLength * Math.sin(angle - arrowAngle)
      )
      ctx.moveTo(arrowStartX, arrowStartY)
      ctx.lineTo(
        arrowStartX - arrowLength * Math.cos(angle + arrowAngle),
        arrowStartY - arrowLength * Math.sin(angle + arrowAngle)
      )
      ctx.strokeStyle = getLinkColor(link)
      ctx.lineWidth = graphSettings.linkWidth / globalScale
      ctx.stroke()
    }
  }, [getLinkColor, getNodeSize, graphSettings.linkWidth, graphSettings.showArrows])

  return (
    <Card className={`relative ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              知识图谱
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {graphData.nodes.length} 个知识点，{graphData.links.length} 个依赖关系
            </p>
          </div>
          
          {/* 控制按钮 */}
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={handleResetView}>
              <Maximize2 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={handleCenterGraph}>
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        {/* 设置面板 */}
        {showSettings && (
          <div className="p-4 bg-gray-50 dark:bg-gray-800 border-b">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-1">节点大小</label>
                <input
                  type="range"
                  min="4"
                  max="16"
                  value={graphSettings.nodeSize}
                  onChange={(e) => setGraphSettings(prev => ({ ...prev, nodeSize: parseInt(e.target.value) }))}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-1">连线宽度</label>
                <input
                  type="range"
                  min="1"
                  max="5"
                  value={graphSettings.linkWidth}
                  onChange={(e) => setGraphSettings(prev => ({ ...prev, linkWidth: parseInt(e.target.value) }))}
                  className="w-full"
                />
              </div>
              <div className="flex items-center gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={graphSettings.showLabels}
                    onChange={(e) => setGraphSettings(prev => ({ ...prev, showLabels: e.target.checked }))}
                    className="mr-2"
                  />
                  显示标签
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={graphSettings.showArrows}
                    onChange={(e) => setGraphSettings(prev => ({ ...prev, showArrows: e.target.checked }))}
                    className="mr-2"
                  />
                  显示箭头
                </label>
              </div>
            </div>
          </div>
        )}
        
        {/* 图谱可视化 */}
        <div className="relative">
          <ForceGraph2D
            ref={fgRef}
            graphData={graphData}
            width={width}
            height={height}
            nodeCanvasObject={nodeCanvasObject}
            linkCanvasObject={linkCanvasObject}
            onNodeClick={handleNodeClick}
            onNodeHover={handleNodeHover}
            onLinkClick={handleLinkClick}
            // 简化的物理引擎参数
            d3AlphaDecay={0.02}
            d3VelocityDecay={0.3}
            d3Force={{
              charge: { strength: graphSettings.chargeStrength },
              link: { distance: graphSettings.linkDistance }
            }}
            // 基础交互设置
            enableNodeDrag={true}
            enableZoomInteraction={true}
            enablePanInteraction={true}
            // 渲染设置
            backgroundColor="transparent"
            cooldownTicks={50}
          />
          
          {/* 图例 */}
          <div className="absolute top-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 text-xs">
            <h4 className="font-semibold mb-2 text-gray-900 dark:text-white">图例</h4>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-gray-700 dark:text-gray-300">已掌握 (≥80%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span className="text-gray-700 dark:text-gray-300">部分掌握 (60-79%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                <span className="text-gray-700 dark:text-gray-300">初步了解 (40-59%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span className="text-gray-700 dark:text-gray-300">未学习 (&lt;40%)</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
