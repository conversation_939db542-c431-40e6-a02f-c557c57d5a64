'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { <PERSON>, Card<PERSON>eader, CardContent, CardFooter } from '@/components/ui/Card'
import { KnowledgePoint } from '@/types'
import { X, Plus } from 'lucide-react'

interface KnowledgePointFormProps {
  knowledgePoint?: KnowledgePoint | null
  onSubmit: (data: Omit<KnowledgePoint, 'id' | 'created_at' | 'updated_at'>) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function KnowledgePointForm({
  knowledgePoint,
  onSubmit,
  onCancel,
  isLoading = false
}: KnowledgePointFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    difficulty_level: 1,
    tags: [] as string[],
    user_id: '' // 这个会在提交时设置
  })
  
  const [newTag, setNewTag] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 初始化表单数据
  useEffect(() => {
    if (knowledgePoint) {
      setFormData({
        name: knowledgePoint.name,
        description: knowledgePoint.description || '',
        difficulty_level: knowledgePoint.difficulty_level,
        tags: knowledgePoint.tags || [],
        user_id: knowledgePoint.user_id
      })
    } else {
      setFormData({
        name: '',
        description: '',
        difficulty_level: 1,
        tags: [],
        user_id: ''
      })
    }
    setErrors({})
  }, [knowledgePoint])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = '知识点名称不能为空'
    }

    if (formData.difficulty_level < 1 || formData.difficulty_level > 10) {
      newErrors.difficulty_level = '难度级别必须在1-10之间'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('提交表单失败:', error)
    }
  }

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const addTag = () => {
    const tag = newTag.trim()
    if (tag && !formData.tags.includes(tag)) {
      handleInputChange('tags', [...formData.tags, tag])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    handleInputChange('tags', formData.tags.filter(tag => tag !== tagToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {knowledgePoint ? '编辑知识点' : '创建新知识点'}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {/* 知识点名称 */}
          <Input
            label="知识点名称 *"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            error={errors.name}
            placeholder="输入知识点名称"
            disabled={isLoading}
          />

          {/* 描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              描述
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="描述这个知识点的内容和要点"
              rows={3}
              disabled={isLoading}
              className="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder:text-gray-400 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-500 dark:focus:border-indigo-400 dark:focus:ring-indigo-400"
            />
          </div>

          {/* 难度级别 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              难度级别 *
            </label>
            <div className="flex items-center space-x-4">
              <input
                type="range"
                min="1"
                max="10"
                value={formData.difficulty_level}
                onChange={(e) => handleInputChange('difficulty_level', parseInt(e.target.value))}
                disabled={isLoading}
                className="flex-1"
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 min-w-[3rem]">
                {formData.difficulty_level}/10
              </span>
            </div>
            <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
              <span>简单</span>
              <span>中等</span>
              <span>困难</span>
            </div>
            {errors.difficulty_level && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.difficulty_level}
              </p>
            )}
          </div>

          {/* 标签 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              标签
            </label>
            
            {/* 现有标签 */}
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      disabled={isLoading}
                      className="ml-1 text-indigo-600 hover:text-indigo-800 dark:text-indigo-300 dark:hover:text-indigo-100"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            )}
            
            {/* 添加新标签 */}
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入标签名称"
                disabled={isLoading}
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addTag}
                disabled={!newTag.trim() || isLoading}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              按回车键或点击加号添加标签
            </p>
          </div>
        </CardContent>

        <CardFooter className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            取消
          </Button>
          <Button
            type="submit"
            isLoading={isLoading}
            disabled={isLoading}
          >
            {knowledgePoint ? '更新' : '创建'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
