'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, CardFooter } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { KnowledgePoint } from '@/types'
import { BookOpen, Edit, Trash2, Target } from 'lucide-react'

interface KnowledgePointCardProps {
  knowledgePoint: KnowledgePoint
  masteryLevel?: number
  onEdit?: (knowledgePoint: KnowledgePoint) => void
  onDelete?: (id: string) => void
  onSelect?: (knowledgePoint: KnowledgePoint) => void
  isSelected?: boolean
  showActions?: boolean
}

export function KnowledgePointCard({
  knowledgePoint,
  masteryLevel = 0,
  onEdit,
  onDelete,
  onSelect,
  isSelected = false,
  showActions = true
}: KnowledgePointCardProps) {
  
  const getDifficultyColor = (level: number) => {
    if (level <= 3) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    if (level <= 6) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  
  const getMasteryColor = (level: number) => {
    if (level >= 80) return 'bg-green-500'
    if (level >= 60) return 'bg-yellow-500'
    if (level >= 40) return 'bg-orange-500'
    return 'bg-red-500'
  }
  
  const getDifficultyLabel = (level: number) => {
    if (level <= 3) return '简单'
    if (level <= 6) return '中等'
    return '困难'
  }

  return (
    <Card 
      className={`transition-all duration-200 hover:shadow-md cursor-pointer ${
        isSelected ? 'ring-2 ring-indigo-500 border-indigo-500' : ''
      }`}
      onClick={() => onSelect?.(knowledgePoint)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">
              {knowledgePoint.name}
            </h3>
            <div className="flex items-center gap-2 mb-2">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(knowledgePoint.difficulty_level)}`}>
                <Target className="w-3 h-3 mr-1" />
                {getDifficultyLabel(knowledgePoint.difficulty_level)}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                难度 {knowledgePoint.difficulty_level}/10
              </span>
            </div>
          </div>
          {showActions && (
            <div className="flex items-center gap-1 ml-2">
              {onEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onEdit(knowledgePoint)
                  }}
                  className="h-8 w-8 p-0"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onDelete(knowledgePoint.id)
                  }}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {knowledgePoint.description && (
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
            {knowledgePoint.description}
          </p>
        )}
        
        {/* 掌握程度进度条 */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              掌握程度
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {masteryLevel}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${getMasteryColor(masteryLevel)}`}
              style={{ width: `${masteryLevel}%` }}
            />
          </div>
        </div>
        
        {/* 标签 */}
        {knowledgePoint.tags && knowledgePoint.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {knowledgePoint.tags.slice(0, 3).map((tag, index) => (
              <span 
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
              >
                {tag}
              </span>
            ))}
            {knowledgePoint.tags.length > 3 && (
              <span className="text-xs text-gray-500 dark:text-gray-400">
                +{knowledgePoint.tags.length - 3} 更多
              </span>
            )}
          </div>
        )}
      </CardContent>
      
      <CardFooter className="pt-0">
        <div className="flex items-center justify-between w-full text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center">
            <BookOpen className="w-3 h-3 mr-1" />
            创建于 {new Date(knowledgePoint.created_at).toLocaleDateString()}
          </div>
          {masteryLevel >= 80 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              已掌握
            </span>
          )}
        </div>
      </CardFooter>
    </Card>
  )
}
