'use client'

import React, { useState, useMemo } from 'react'
import { KnowledgePointCard } from './KnowledgePointCard'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { useKnowledgeGraph } from '@/hooks/useKnowledgeGraph'
import { KnowledgePoint } from '@/types'
import { Search, Plus, Filter, SortAsc, SortDesc } from 'lucide-react'

interface KnowledgePointListProps {
  onCreateNew?: () => void
  onEditPoint?: (point: KnowledgePoint) => void
  onDeletePoint?: (id: string) => void
  showActions?: boolean
}

type SortField = 'name' | 'difficulty' | 'mastery' | 'created_at'
type SortOrder = 'asc' | 'desc'

export function KnowledgePointList({
  onCreateNew,
  onEditPoint,
  onDeletePoint,
  showActions = true
}: KnowledgePointListProps) {
  const {
    knowledgePoints,
    selectedKnowledgePoint,
    setSelectedKnowledgePoint,
    isLoading,
    error
  } = useKnowledgeGraph()

  // 本地状态
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [difficultyFilter, setDifficultyFilter] = useState<number | null>(null)
  const [sortField, setSortField] = useState<SortField>('created_at')
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc')
  const [showFilters, setShowFilters] = useState(false)

  // 获取所有标签
  const allTags = useMemo(() => {
    const tags = new Set<string>()
    knowledgePoints.forEach(point => {
      point.tags?.forEach(tag => tags.add(tag))
    })
    return Array.from(tags).sort()
  }, [knowledgePoints])

  // 过滤和排序逻辑
  const filteredAndSortedPoints = useMemo(() => {
    let filtered = knowledgePoints

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(point =>
        point.name.toLowerCase().includes(query) ||
        point.description?.toLowerCase().includes(query) ||
        point.tags?.some(tag => tag.toLowerCase().includes(query))
      )
    }

    // 标签过滤
    if (selectedTags.length > 0) {
      filtered = filtered.filter(point =>
        selectedTags.every(tag => point.tags?.includes(tag))
      )
    }

    // 难度过滤
    if (difficultyFilter !== null) {
      filtered = filtered.filter(point => point.difficulty_level === difficultyFilter)
    }

    // 排序
    filtered.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortField) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'difficulty':
          aValue = a.difficulty_level
          bValue = b.difficulty_level
          break
        case 'mastery':
          // 这里需要从store获取掌握程度，暂时使用0
          aValue = 0
          bValue = 0
          break
        case 'created_at':
          aValue = new Date(a.created_at).getTime()
          bValue = new Date(b.created_at).getTime()
          break
        default:
          return 0
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [knowledgePoints, searchQuery, selectedTags, difficultyFilter, sortField, sortOrder])

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortOrder('asc')
    }
  }

  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  const clearFilters = () => {
    setSearchQuery('')
    setSelectedTags([])
    setDifficultyFilter(null)
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 dark:text-red-400">{error}</p>
        <Button 
          variant="outline" 
          onClick={() => window.location.reload()}
          className="mt-4"
        >
          重试
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 头部操作栏 */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex-1 max-w-md">
          <Input
            placeholder="搜索知识点..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
          
          {onCreateNew && (
            <Button onClick={onCreateNew}>
              <Plus className="h-4 w-4 mr-2" />
              新建知识点
            </Button>
          )}
        </div>
      </div>

      {/* 筛选面板 */}
      {showFilters && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-4">
          <div className="flex flex-wrap items-center gap-4">
            {/* 排序选项 */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">排序:</span>
              {(['name', 'difficulty', 'created_at'] as SortField[]).map(field => (
                <Button
                  key={field}
                  variant={sortField === field ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => handleSort(field)}
                  className="flex items-center gap-1"
                >
                  {field === 'name' && '名称'}
                  {field === 'difficulty' && '难度'}
                  {field === 'created_at' && '创建时间'}
                  {sortField === field && (
                    sortOrder === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                  )}
                </Button>
              ))}
            </div>

            {/* 难度筛选 */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">难度:</span>
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(level => (
                <Button
                  key={level}
                  variant={difficultyFilter === level ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setDifficultyFilter(difficultyFilter === level ? null : level)}
                >
                  {level}
                </Button>
              ))}
            </div>
          </div>

          {/* 标签筛选 */}
          {allTags.length > 0 && (
            <div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">标签:</span>
              <div className="flex flex-wrap gap-2">
                {allTags.map(tag => (
                  <Button
                    key={tag}
                    variant={selectedTags.includes(tag) ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => toggleTag(tag)}
                  >
                    {tag}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* 清除筛选 */}
          <div className="flex justify-end">
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              清除筛选
            </Button>
          </div>
        </div>
      )}

      {/* 统计信息 */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        显示 {filteredAndSortedPoints.length} / {knowledgePoints.length} 个知识点
      </div>

      {/* 知识点列表 */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-48"></div>
            </div>
          ))}
        </div>
      ) : filteredAndSortedPoints.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {knowledgePoints.length === 0 ? '还没有知识点' : '没有找到匹配的知识点'}
          </p>
          {onCreateNew && knowledgePoints.length === 0 && (
            <Button onClick={onCreateNew}>
              <Plus className="h-4 w-4 mr-2" />
              创建第一个知识点
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredAndSortedPoints.map(point => (
            <KnowledgePointCard
              key={point.id}
              knowledgePoint={point}
              masteryLevel={0} // TODO: 从store获取实际掌握程度
              onEdit={onEditPoint}
              onDelete={onDeletePoint}
              onSelect={setSelectedKnowledgePoint}
              isSelected={selectedKnowledgePoint?.id === point.id}
              showActions={showActions}
            />
          ))}
        </div>
      )}
    </div>
  )
}
