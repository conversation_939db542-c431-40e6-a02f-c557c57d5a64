'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { 
  Brain, 
  HelpCircle, 
  CheckCircle, 
  Clock, 
  Target,
  ArrowRight,
  Lightbulb,
  BookOpen,
  TrendingUp
} from 'lucide-react'

interface LearningSession {
  id: string
  targetTopic: string
  currentStep: string
  status: string
  knowledgeGraph: { [concept: string]: number }
  blockedConcepts: string[]
}

interface AdaptiveLearningInterfaceProps {
  userId: string
}

export function AdaptiveLearningInterface({ userId }: AdaptiveLearningInterfaceProps) {
  const [session, setSession] = useState<LearningSession | null>(null)
  const [loading, setLoading] = useState(false)
  const [newTopic, setNewTopic] = useState('')
  const [difficulty, setDifficulty] = useState('')
  const [userQuestion, setUserQuestion] = useState('')
  const [response, setResponse] = useState<any>(null)

  // 开始新的学习会话
  const startLearningSession = async () => {
    if (!newTopic.trim()) return

    setLoading(true)
    try {
      const res = await fetch('/api/adaptive/start-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          targetTopic: newTopic,
          userContext: {
            currentKnowledge: [],
            learningGoals: [newTopic],
            timeAvailable: 60,
            preferredStyle: 'visual'
          }
        })
      })

      if (res.ok) {
        const data = await res.json()
        setSession(data.session)
        setNewTopic('')
      }
    } catch (error) {
      console.error('启动学习会话失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 报告学习困难
  const reportDifficulty = async () => {
    if (!session || !difficulty.trim()) return

    setLoading(true)
    try {
      const res = await fetch('/api/adaptive/report-difficulty', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: session.id,
          difficulty: {
            concept: difficulty,
            description: `用户在理解"${difficulty}"时遇到困难`,
            userQuestion: userQuestion || undefined,
            context: `正在学习${session.targetTopic}`,
            timestamp: new Date()
          }
        })
      })

      if (res.ok) {
        const data = await res.json()
        setResponse(data.response)
        setDifficulty('')
        setUserQuestion('')
        
        // 更新会话状态
        const updatedSession = await fetchSession(session.id)
        if (updatedSession) setSession(updatedSession)
      }
    } catch (error) {
      console.error('报告困难失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 完成学习步骤
  const completeStep = async (stepId: string, masteryLevel: number) => {
    if (!session) return

    setLoading(true)
    try {
      const res = await fetch('/api/adaptive/complete-step', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: session.id,
          stepId,
          masteryLevel,
          timeSpent: 30 // 假设30分钟
        })
      })

      if (res.ok) {
        const data = await res.json()
        setResponse(data.response)
        
        // 更新会话状态
        const updatedSession = await fetchSession(session.id)
        if (updatedSession) setSession(updatedSession)
      }
    } catch (error) {
      console.error('完成步骤失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取学习建议
  const getRecommendation = async () => {
    if (!session) return

    setLoading(true)
    try {
      const res = await fetch('/api/adaptive/get-recommendation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId: session.id })
      })

      if (res.ok) {
        const data = await res.json()
        setResponse(data.response)
      }
    } catch (error) {
      console.error('获取建议失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取会话信息
  const fetchSession = async (sessionId: string) => {
    try {
      const res = await fetch(`/api/adaptive/session/${sessionId}`)
      if (res.ok) {
        const data = await res.json()
        return data.session
      }
    } catch (error) {
      console.error('获取会话失败:', error)
    }
    return null
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          AI自适应学习系统
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          智能分析学习困难，动态规划学习路径
        </p>
      </div>

      {/* 开始新学习会话 */}
      {!session && (
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold flex items-center">
              <Target className="w-5 h-5 mr-2 text-indigo-600" />
              开始新的学习旅程
            </h2>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input
              placeholder="输入您想学习的主题，例如：Python机器学习"
              value={newTopic}
              onChange={(e) => setNewTopic(e.target.value)}
              disabled={loading}
            />
            <Button
              onClick={startLearningSession}
              disabled={loading || !newTopic.trim()}
              className="w-full"
            >
              {loading ? '启动中...' : '开始学习'}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* 当前学习会话 */}
      {session && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 会话状态 */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold flex items-center">
                <BookOpen className="w-5 h-5 mr-2 text-green-600" />
                当前学习会话
              </h2>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="font-medium">学习目标</p>
                <p className="text-gray-600 dark:text-gray-400">{session.targetTopic}</p>
              </div>
              
              <div>
                <p className="font-medium">当前阶段</p>
                <p className="text-gray-600 dark:text-gray-400">{session.currentStep}</p>
              </div>

              <div>
                <p className="font-medium">学习状态</p>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  session.status === 'active' ? 'bg-green-100 text-green-800' :
                  session.status === 'paused' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {session.status === 'active' ? '进行中' : 
                   session.status === 'paused' ? '已暂停' : '其他'}
                </span>
              </div>

              {/* 知识掌握情况 */}
              {Object.keys(session.knowledgeGraph).length > 0 && (
                <div>
                  <p className="font-medium mb-2">知识掌握情况</p>
                  <div className="space-y-2">
                    {Object.entries(session.knowledgeGraph).map(([concept, level]) => (
                      <div key={concept} className="flex items-center justify-between">
                        <span className="text-sm">{concept}</span>
                        <div className="flex items-center">
                          <div className="w-20 bg-gray-200 rounded-full h-2 mr-2">
                            <div 
                              className="bg-indigo-600 h-2 rounded-full" 
                              style={{ width: `${level}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-500">{level}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 遇到困难的概念 */}
              {session.blockedConcepts.length > 0 && (
                <div>
                  <p className="font-medium text-orange-600">需要加强的概念</p>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {session.blockedConcepts.map(concept => (
                      <span 
                        key={concept}
                        className="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs"
                      >
                        {concept}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 学习操作 */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold flex items-center">
                <HelpCircle className="w-5 h-5 mr-2 text-blue-600" />
                学习助手
              </h2>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 报告困难 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">遇到困难的概念</label>
                <Input
                  placeholder="例如：神经网络反向传播"
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value)}
                  disabled={loading}
                />
                <Input
                  placeholder="具体问题（可选）"
                  value={userQuestion}
                  onChange={(e) => setUserQuestion(e.target.value)}
                  disabled={loading}
                />
                <Button
                  onClick={reportDifficulty}
                  disabled={loading || !difficulty.trim()}
                  variant="outline"
                  className="w-full"
                >
                  <HelpCircle className="w-4 h-4 mr-2" />
                  寻求帮助
                </Button>
              </div>

              {/* 获取建议 */}
              <Button
                onClick={getRecommendation}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                <Lightbulb className="w-4 h-4 mr-2" />
                获取学习建议
              </Button>

              {/* 模拟完成步骤 */}
              <div className="pt-4 border-t">
                <p className="text-sm font-medium mb-2">模拟完成学习步骤</p>
                <div className="flex gap-2">
                  <Button
                    onClick={() => completeStep('mock-step', 80)}
                    disabled={loading}
                    size="sm"
                    variant="outline"
                  >
                    完成步骤 (80%)
                  </Button>
                  <Button
                    onClick={() => completeStep('mock-step', 60)}
                    disabled={loading}
                    size="sm"
                    variant="outline"
                  >
                    完成步骤 (60%)
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* AI响应显示 */}
      {response && (
        <Card className="border-indigo-200 bg-indigo-50 dark:bg-indigo-900/20">
          <CardHeader>
            <h3 className="text-lg font-semibold flex items-center">
              <Brain className="w-5 h-5 mr-2 text-indigo-600" />
              AI学习助手回复
              <span className="ml-auto text-sm text-gray-500">
                类型: {response.type}
              </span>
            </h3>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 响应内容 */}
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
              <pre className="whitespace-pre-wrap text-sm">
                {JSON.stringify(response.content, null, 2)}
              </pre>
            </div>

            {/* 下一步建议 */}
            {response.nextSteps && response.nextSteps.length > 0 && (
              <div>
                <p className="font-medium mb-2 flex items-center">
                  <ArrowRight className="w-4 h-4 mr-2" />
                  建议的下一步
                </p>
                <ul className="space-y-1">
                  {response.nextSteps.map((step: string, index: number) => (
                    <li key={index} className="flex items-center text-sm">
                      <CheckCircle className="w-3 h-3 mr-2 text-green-600" />
                      {step}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* 时间估算 */}
            {response.estimatedTime > 0 && (
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="w-4 h-4 mr-2" />
                预计时间: {response.estimatedTime} 分钟
              </div>
            )}

            {/* 置信度 */}
            {response.confidence && (
              <div className="flex items-center text-sm text-gray-600">
                <TrendingUp className="w-4 h-4 mr-2" />
                AI置信度: {Math.round(response.confidence * 100)}%
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
