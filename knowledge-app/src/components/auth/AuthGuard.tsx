'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Brain } from 'lucide-react'

interface AuthGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
}

/**
 * 认证保护组件
 * 用于保护需要登录才能访问的页面
 */
export function AuthGuard({ 
  children, 
  fallback,
  redirectTo = '/auth/signin' 
}: AuthGuardProps) {
  const router = useRouter()
  const { isAuthenticated, loading } = useAuth()

  useEffect(() => {
    // 如果加载完成且用户未认证，重定向到登录页
    if (!loading && !isAuthenticated) {
      router.push(redirectTo)
    }
  }, [loading, isAuthenticated, router, redirectTo])

  // 显示加载状态
  if (loading) {
    return fallback || (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Brain className="w-12 h-12 text-indigo-600 mx-auto mb-4 animate-pulse" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Learn Everything
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            正在验证身份...
          </p>
          <div className="mt-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          </div>
        </div>
      </div>
    )
  }

  // 如果用户未认证，显示空内容（即将重定向）
  if (!isAuthenticated) {
    return null
  }

  // 用户已认证，显示受保护的内容
  return <>{children}</>
}

/**
 * 反向认证保护组件
 * 用于保护已登录用户不应该访问的页面（如登录页）
 */
export function GuestGuard({ 
  children, 
  redirectTo = '/knowledge' 
}: {
  children: React.ReactNode
  redirectTo?: string
}) {
  const router = useRouter()
  const { isAuthenticated, loading } = useAuth()

  useEffect(() => {
    // 如果加载完成且用户已认证，重定向到指定页面
    if (!loading && isAuthenticated) {
      router.push(redirectTo)
    }
  }, [loading, isAuthenticated, router, redirectTo])

  // 显示加载状态
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <Brain className="w-12 h-12 text-indigo-600 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600 dark:text-gray-400">
            正在加载...
          </p>
        </div>
      </div>
    )
  }

  // 如果用户已认证，显示空内容（即将重定向）
  if (isAuthenticated) {
    return null
  }

  // 用户未认证，显示内容
  return <>{children}</>
}
