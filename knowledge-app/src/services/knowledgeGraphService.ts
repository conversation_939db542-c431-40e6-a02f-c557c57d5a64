import { knowledgePointsAPI, dependenciesAPI, progressAPI } from '@/lib/database'
import { KnowledgePoint, KnowledgeDependency, KnowledgeGraph, LearningRecommendation } from '@/types'

/**
 * Knowledge Graph Service
 * 处理知识图谱的业务逻辑，确保数据一致性和业务规则
 */
export class KnowledgeGraphService {
  
  /**
   * 验证是否会产生循环依赖
   * 使用深度优先搜索检测环路
   */
  private async detectCycle(
    newPrerequisiteId: string, 
    newDependentId: string, 
    existingDependencies: KnowledgeDependency[]
  ): Promise<boolean> {
    // 构建邻接表
    const graph = new Map<string, string[]>()
    
    // 添加现有依赖
    existingDependencies.forEach(dep => {
      if (!graph.has(dep.prerequisite_id)) {
        graph.set(dep.prerequisite_id, [])
      }
      graph.get(dep.prerequisite_id)!.push(dep.dependent_id)
    })
    
    // 添加新的依赖
    if (!graph.has(newPrerequisiteId)) {
      graph.set(newPrerequisiteId, [])
    }
    graph.get(newPrerequisiteId)!.push(newDependentId)
    
    // DFS检测环路
    const visited = new Set<string>()
    const recursionStack = new Set<string>()
    
    const dfs = (node: string): boolean => {
      if (recursionStack.has(node)) return true // 发现环路
      if (visited.has(node)) return false
      
      visited.add(node)
      recursionStack.add(node)
      
      const neighbors = graph.get(node) || []
      for (const neighbor of neighbors) {
        if (dfs(neighbor)) return true
      }
      
      recursionStack.delete(node)
      return false
    }
    
    // 检查所有节点
    for (const node of graph.keys()) {
      if (!visited.has(node)) {
        if (dfs(node)) return true
      }
    }
    
    return false
  }
  
  /**
   * 安全地添加依赖关系
   * 确保不会产生循环依赖
   */
  async addDependency(prerequisiteId: string, dependentId: string): Promise<{
    success: boolean
    dependency?: KnowledgeDependency
    error?: string
  }> {
    try {
      // 验证知识点存在
      const [prerequisite, dependent] = await Promise.all([
        knowledgePointsAPI.getById(prerequisiteId),
        knowledgePointsAPI.getById(dependentId)
      ])
      
      if (!prerequisite) {
        return { success: false, error: '前置知识点不存在' }
      }
      
      if (!dependent) {
        return { success: false, error: '依赖知识点不存在' }
      }
      
      // 检查是否已存在该依赖
      const existingDependencies = await dependenciesAPI.getAll()
      const exists = existingDependencies.some(
        dep => dep.prerequisite_id === prerequisiteId && dep.dependent_id === dependentId
      )
      
      if (exists) {
        return { success: false, error: '依赖关系已存在' }
      }
      
      // 检测循环依赖
      const hasCycle = await this.detectCycle(prerequisiteId, dependentId, existingDependencies)
      if (hasCycle) {
        return { success: false, error: '添加此依赖会产生循环依赖' }
      }
      
      // 创建依赖关系
      const dependency = await dependenciesAPI.create(prerequisiteId, dependentId)
      
      return { success: true, dependency }
    } catch (error) {
      console.error('添加依赖关系失败:', error)
      return { success: false, error: '添加依赖关系失败' }
    }
  }
  
  /**
   * 获取知识图谱数据
   * 包含节点和边的信息
   */
  async getKnowledgeGraph(): Promise<KnowledgeGraph> {
    try {
      const [knowledgePoints, dependencies, userProgress] = await Promise.all([
        knowledgePointsAPI.getAll(),
        dependenciesAPI.getAll(),
        progressAPI.getAll()
      ])
      
      // 构建节点
      const nodes = knowledgePoints.map(point => {
        const progress = userProgress.find(p => p.knowledge_point_id === point.id)
        return {
          id: point.id,
          name: point.name,
          group: point.difficulty_level,
          mastery_level: progress?.mastery_level || 0,
          difficulty_level: point.difficulty_level
        }
      })
      
      // 构建边
      const links = dependencies.map(dep => ({
        source: dep.prerequisite_id,
        target: dep.dependent_id
      }))
      
      return { nodes, links }
    } catch (error) {
      console.error('获取知识图谱失败:', error)
      return { nodes: [], links: [] }
    }
  }
  
  /**
   * 计算拓扑排序
   * 用于确定学习顺序
   */
  async getTopologicalOrder(): Promise<string[]> {
    try {
      const dependencies = await dependenciesAPI.getAll()
      const knowledgePoints = await knowledgePointsAPI.getAll()
      
      // 构建邻接表和入度表
      const graph = new Map<string, string[]>()
      const inDegree = new Map<string, number>()
      
      // 初始化
      knowledgePoints.forEach(point => {
        graph.set(point.id, [])
        inDegree.set(point.id, 0)
      })
      
      // 构建图
      dependencies.forEach(dep => {
        graph.get(dep.prerequisite_id)!.push(dep.dependent_id)
        inDegree.set(dep.dependent_id, (inDegree.get(dep.dependent_id) || 0) + 1)
      })
      
      // Kahn算法进行拓扑排序
      const queue: string[] = []
      const result: string[] = []
      
      // 找到所有入度为0的节点
      for (const [nodeId, degree] of inDegree.entries()) {
        if (degree === 0) {
          queue.push(nodeId)
        }
      }
      
      while (queue.length > 0) {
        const current = queue.shift()!
        result.push(current)
        
        // 处理当前节点的所有邻居
        const neighbors = graph.get(current) || []
        for (const neighbor of neighbors) {
          const newDegree = inDegree.get(neighbor)! - 1
          inDegree.set(neighbor, newDegree)
          
          if (newDegree === 0) {
            queue.push(neighbor)
          }
        }
      }
      
      return result
    } catch (error) {
      console.error('计算拓扑排序失败:', error)
      return []
    }
  }
  
  /**
   * 获取学习推荐
   * 基于当前掌握情况推荐下一步学习内容
   */
  async getLearningRecommendations(limit: number = 5): Promise<LearningRecommendation[]> {
    try {
      const [knowledgePoints, dependencies, userProgress] = await Promise.all([
        knowledgePointsAPI.getAll(),
        dependenciesAPI.getAll(),
        progressAPI.getAll()
      ])
      
      const recommendations: LearningRecommendation[] = []
      
      for (const point of knowledgePoints) {
        const progress = userProgress.find(p => p.knowledge_point_id === point.id)
        const currentMastery = progress?.mastery_level || 0
        
        // 跳过已掌握的知识点 (>= 80%)
        if (currentMastery >= 80) continue
        
        // 检查前置条件是否满足
        const prerequisites = dependencies
          .filter(dep => dep.dependent_id === point.id)
          .map(dep => dep.prerequisite_id)
        
        const allPrerequisitesMet = prerequisites.every(prereqId => {
          const prereqProgress = userProgress.find(p => p.knowledge_point_id === prereqId)
          return (prereqProgress?.mastery_level || 0) >= 80
        })
        
        if (allPrerequisitesMet) {
          // 计算优先级
          let priority = 100 - currentMastery // 掌握程度越低，优先级越高
          priority += (10 - point.difficulty_level) * 5 // 难度越低，优先级越高
          
          // 估算学习时间（基于难度）
          const estimatedTime = point.difficulty_level * 30 // 每个难度级别30分钟
          
          recommendations.push({
            knowledge_point: point,
            reason: prerequisites.length > 0 
              ? `前置条件已满足，可以开始学习` 
              : `基础知识点，建议优先学习`,
            priority,
            estimated_time: estimatedTime
          })
        }
      }
      
      // 按优先级排序并限制数量
      return recommendations
        .sort((a, b) => b.priority - a.priority)
        .slice(0, limit)
        
    } catch (error) {
      console.error('获取学习推荐失败:', error)
      return []
    }
  }
  
  /**
   * 删除知识点及其相关依赖
   * 确保数据一致性
   */
  async deleteKnowledgePoint(id: string): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      // 检查是否有其他知识点依赖于此知识点
      const dependencies = await dependenciesAPI.getAll()
      const hasDependents = dependencies.some(dep => dep.prerequisite_id === id)
      
      if (hasDependents) {
        return { 
          success: false, 
          error: '无法删除：其他知识点依赖于此知识点，请先移除相关依赖关系' 
        }
      }
      
      // 删除知识点（数据库外键约束会自动删除相关的依赖关系和进度记录）
      await knowledgePointsAPI.delete(id)
      
      return { success: true }
    } catch (error) {
      console.error('删除知识点失败:', error)
      return { success: false, error: '删除知识点失败' }
    }
  }
}

// 导出单例实例
export const knowledgeGraphService = new KnowledgeGraphService()
