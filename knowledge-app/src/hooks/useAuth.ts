import { useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { authService } from '@/lib/auth'
import { useAuthStore } from '@/store/useAuthStore'

/**
 * 认证相关的自定义Hook
 * 提供登录、登出、注册等功能
 */
export function useAuth() {
  const router = useRouter()
  const {
    user,
    session,
    loading,
    isAuthenticated,
    setUser,
    setSession,
    setLoading,
    clearAuth
  } = useAuthStore()

  /**
   * 初始化认证状态
   */
  const initializeAuth = useCallback(async () => {
    setLoading(true)
    
    try {
      const [currentUser, currentSession] = await Promise.all([
        authService.getCurrentUser(),
        authService.getCurrentSession()
      ])
      
      setUser(currentUser)
      setSession(currentSession)
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      clearAuth()
    } finally {
      setLoading(false)
    }
  }, [setUser, setSession, setLoading, clearAuth])

  /**
   * 用户登录
   */
  const signIn = useCallback(async (email: string, password: string) => {
    setLoading(true)
    
    try {
      const { data, error } = await authService.signIn(email, password)
      
      if (error) {
        throw new Error(error.message)
      }
      
      if (data.user && data.session) {
        const authUser = {
          id: data.user.id,
          email: data.user.email || '',
          name: data.user.user_metadata?.name || data.user.email?.split('@')[0],
          avatar_url: data.user.user_metadata?.avatar_url,
          created_at: data.user.created_at || new Date().toISOString()
        }

        console.log('设置用户状态:', authUser)
        setUser(authUser)
        setSession(data.session)
        console.log('用户状态已设置')

        return { success: true }
      }
      
      throw new Error('登录失败')
    } catch (error) {
      console.error('登录失败:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '登录失败' 
      }
    } finally {
      setLoading(false)
    }
  }, [setUser, setSession, setLoading])

  /**
   * 用户注册
   */
  const signUp = useCallback(async (email: string, password: string, name?: string) => {
    setLoading(true)
    
    try {
      const { data, error } = await authService.signUp(email, password, { name })
      
      if (error) {
        throw new Error(error.message)
      }
      
      // 注册成功，如果有session则直接登录
      if (data.session && data.user) {
        const authUser = {
          id: data.user.id,
          email: data.user.email || '',
          name: data.user.user_metadata?.name || data.user.email?.split('@')[0],
          avatar_url: data.user.user_metadata?.avatar_url,
          created_at: data.user.created_at || new Date().toISOString()
        }

        setUser(authUser)
        setSession(data.session)

        return {
          success: true,
          needsVerification: false,
          message: '注册成功'
        }
      }

      // 需要邮箱验证
      return {
        success: true,
        needsVerification: true,
        message: '请检查邮箱并点击验证链接'
      }
    } catch (error) {
      console.error('注册失败:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '注册失败' 
      }
    } finally {
      setLoading(false)
    }
  }, [setLoading])

  /**
   * 第三方登录
   */
  const signInWithGoogle = useCallback(async () => {
    setLoading(true)
    
    try {
      const { data, error } = await authService.signInWithGoogle()
      
      if (error) {
        throw new Error(error.message)
      }
      
      // OAuth登录会重定向，这里不需要处理结果
      return { success: true }
    } catch (error) {
      console.error('Google登录失败:', error)
      setLoading(false)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Google登录失败' 
      }
    }
  }, [setLoading])

  /**
   * 用户登出
   */
  const signOut = useCallback(async () => {
    setLoading(true)
    
    try {
      const { error } = await authService.signOut()
      
      if (error) {
        throw new Error(error.message)
      }
      
      clearAuth()
      router.push('/auth/signin')
      
      return { success: true }
    } catch (error) {
      console.error('登出失败:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '登出失败' 
      }
    } finally {
      setLoading(false)
    }
  }, [clearAuth, router, setLoading])

  /**
   * 更新用户信息
   */
  const updateProfile = useCallback(async (updates: { 
    name?: string
    email?: string
    avatar_url?: string 
  }) => {
    if (!user) return { success: false, error: '用户未登录' }
    
    setLoading(true)
    
    try {
      const { data, error } = await authService.updateUser({
        email: updates.email,
        data: {
          name: updates.name,
          avatar_url: updates.avatar_url
        }
      })
      
      if (error) {
        throw new Error(error.message)
      }
      
      if (data.user) {
        const updatedUser = {
          ...user,
          email: data.user.email || user.email,
          name: data.user.user_metadata?.name || user.name,
          avatar_url: data.user.user_metadata?.avatar_url || user.avatar_url
        }
        
        setUser(updatedUser)
      }
      
      return { success: true }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '更新失败' 
      }
    } finally {
      setLoading(false)
    }
  }, [user, setUser, setLoading])

  /**
   * 重置密码
   */
  const resetPassword = useCallback(async (email: string) => {
    setLoading(true)
    
    try {
      const { error } = await authService.resetPassword(email)
      
      if (error) {
        throw new Error(error.message)
      }
      
      return { 
        success: true, 
        message: '密码重置邮件已发送，请检查邮箱' 
      }
    } catch (error) {
      console.error('重置密码失败:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '重置密码失败' 
      }
    } finally {
      setLoading(false)
    }
  }, [setLoading])

  // 监听认证状态变化
  useEffect(() => {
    const { data: { subscription } } = authService.onAuthStateChange(
      async (event, session) => {
        console.log('认证状态变化:', event, session)
        
        if (event === 'SIGNED_IN' && session) {
          const authUser = {
            id: session.user.id,
            email: session.user.email || '',
            name: session.user.user_metadata?.name || session.user.email?.split('@')[0],
            avatar_url: session.user.user_metadata?.avatar_url,
            created_at: session.user.created_at || new Date().toISOString()
          }
          
          setUser(authUser)
          setSession(session)
        } else if (event === 'SIGNED_OUT') {
          clearAuth()
        }
        
        setLoading(false)
      }
    )

    // 初始化认证状态
    initializeAuth()

    return () => {
      subscription.unsubscribe()
    }
  }, [initializeAuth, setUser, setSession, clearAuth, setLoading])

  return {
    // State
    user,
    session,
    loading,
    isAuthenticated,
    
    // Actions
    signIn,
    signUp,
    signInWithGoogle,
    signOut,
    updateProfile,
    resetPassword,
    
    // Utils
    initializeAuth
  }
}
