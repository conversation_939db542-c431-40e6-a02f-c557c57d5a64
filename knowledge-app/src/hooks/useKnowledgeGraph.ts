import { useState, useEffect, useCallback } from 'react'
import { knowledgeGraphService } from '@/services/knowledgeGraphService'
import { knowledgePointsAPI } from '@/lib/database'
import { useKnowledgeStore } from '@/store/useKnowledgeStore'
import { KnowledgePoint, KnowledgeGraph, LearningRecommendation } from '@/types'

/**
 * 知识图谱相关的自定义Hook
 * 提供UI组件需要的所有知识图谱操作
 */
export function useKnowledgeGraph() {
  const {
    knowledgePoints,
    dependencies,
    selectedKnowledgePoint,
    isLoading,
    error,
    setKnowledgePoints,
    setDependencies,
    addKnowledgePoint,
    updateKnowledgePoint,
    deleteKnowledgePoint: deleteFromStore,
    addDependency,
    removeDependency,
    setSelectedKnowledgePoint,
    setLoading,
    setError,
    getKnowledgeGraph,
    getRecommendations
  } = useKnowledgeStore()

  // 本地状态
  const [recommendations, setRecommendations] = useState<LearningRecommendation[]>([])
  const [topologicalOrder, setTopologicalOrder] = useState<string[]>([])

  /**
   * 加载所有知识图谱数据
   */
  const loadKnowledgeGraph = useCallback(async () => {
    console.log('开始加载知识图谱数据...')
    setLoading(true)
    setError(null)

    try {
      const { dependenciesAPI } = await import('@/lib/database')
      const [points, dependencies] = await Promise.all([
        knowledgePointsAPI.getAll(),
        dependenciesAPI.getAll()
      ])

      console.log('加载到的知识点:', points)
      console.log('加载到的依赖关系:', dependencies)

      setKnowledgePoints(points)
      setDependencies(dependencies)

    } catch (err) {
      console.error('加载知识图谱失败:', err)
      setError('加载知识图谱失败')
    } finally {
      setLoading(false)
    }
  }, [setKnowledgePoints, setDependencies, setLoading, setError])

  /**
   * 创建新知识点
   */
  const createKnowledgePoint = useCallback(async (
    pointData: Omit<KnowledgePoint, 'id' | 'created_at' | 'updated_at'>
  ) => {
    setLoading(true)
    setError(null)
    
    try {
      const newPoint = await knowledgePointsAPI.create(pointData)
      addKnowledgePoint(newPoint)
      return { success: true, data: newPoint }
    } catch (err) {
      console.error('创建知识点失败:', err)
      setError('创建知识点失败')
      return { success: false, error: '创建知识点失败' }
    } finally {
      setLoading(false)
    }
  }, [addKnowledgePoint, setLoading, setError])

  /**
   * 更新知识点
   */
  const updateKnowledgePointData = useCallback(async (
    id: string, 
    updates: Partial<KnowledgePoint>
  ) => {
    setLoading(true)
    setError(null)
    
    try {
      const updatedPoint = await knowledgePointsAPI.update(id, updates)
      updateKnowledgePoint(id, updatedPoint)
      return { success: true, data: updatedPoint }
    } catch (err) {
      console.error('更新知识点失败:', err)
      setError('更新知识点失败')
      return { success: false, error: '更新知识点失败' }
    } finally {
      setLoading(false)
    }
  }, [updateKnowledgePoint, setLoading, setError])

  /**
   * 删除知识点
   */
  const deleteKnowledgePoint = useCallback(async (id: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await knowledgeGraphService.deleteKnowledgePoint(id)
      
      if (result.success) {
        deleteFromStore(id)
        // 如果删除的是当前选中的知识点，清除选择
        if (selectedKnowledgePoint?.id === id) {
          setSelectedKnowledgePoint(null)
        }
        return { success: true }
      } else {
        setError(result.error || '删除知识点失败')
        return { success: false, error: result.error }
      }
    } catch (err) {
      console.error('删除知识点失败:', err)
      setError('删除知识点失败')
      return { success: false, error: '删除知识点失败' }
    } finally {
      setLoading(false)
    }
  }, [deleteFromStore, selectedKnowledgePoint, setSelectedKnowledgePoint, setLoading, setError])

  /**
   * 添加依赖关系
   */
  const createDependency = useCallback(async (prerequisiteId: string, dependentId: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await knowledgeGraphService.addDependency(prerequisiteId, dependentId)
      
      if (result.success && result.dependency) {
        addDependency(result.dependency)
        return { success: true, data: result.dependency }
      } else {
        setError(result.error || '添加依赖关系失败')
        return { success: false, error: result.error }
      }
    } catch (err) {
      console.error('添加依赖关系失败:', err)
      setError('添加依赖关系失败')
      return { success: false, error: '添加依赖关系失败' }
    } finally {
      setLoading(false)
    }
  }, [addDependency, setLoading, setError])

  /**
   * 删除依赖关系
   */
  const deleteDependency = useCallback(async (prerequisiteId: string, dependentId: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const { dependenciesAPI } = await import('@/lib/database')
      await dependenciesAPI.delete(prerequisiteId, dependentId)
      removeDependency(prerequisiteId, dependentId)
      return { success: true }
    } catch (err) {
      console.error('删除依赖关系失败:', err)
      setError('删除依赖关系失败')
      return { success: false, error: '删除依赖关系失败' }
    } finally {
      setLoading(false)
    }
  }, [removeDependency, setLoading, setError])

  /**
   * 获取学习推荐
   */
  const loadRecommendations = useCallback(async (limit: number = 5) => {
    try {
      const recs = await knowledgeGraphService.getLearningRecommendations(limit)
      setRecommendations(recs)
      return recs
    } catch (err) {
      console.error('获取学习推荐失败:', err)
      return []
    }
  }, [])

  /**
   * 获取拓扑排序
   */
  const loadTopologicalOrder = useCallback(async () => {
    try {
      const order = await knowledgeGraphService.getTopologicalOrder()
      setTopologicalOrder(order)
      return order
    } catch (err) {
      console.error('获取拓扑排序失败:', err)
      return []
    }
  }, [])

  /**
   * 搜索知识点
   */
  const searchKnowledgePoints = useCallback(async (query: string) => {
    if (!query.trim()) return knowledgePoints
    
    try {
      const results = await knowledgePointsAPI.search(query)
      return results
    } catch (err) {
      console.error('搜索知识点失败:', err)
      return []
    }
  }, [knowledgePoints])

  // 初始化时加载数据
  useEffect(() => {
    loadKnowledgeGraph()
  }, [loadKnowledgeGraph])

  // 返回所有需要的状态和方法
  return {
    // 状态
    knowledgePoints,
    dependencies,
    selectedKnowledgePoint,
    recommendations,
    topologicalOrder,
    isLoading,
    error,
    
    // 计算属性
    knowledgeGraph: getKnowledgeGraph(),
    storeRecommendations: getRecommendations(),
    
    // 方法
    loadKnowledgeGraph,
    createKnowledgePoint,
    updateKnowledgePoint: updateKnowledgePointData,
    deleteKnowledgePoint,
    createDependency,
    deleteDependency,
    loadRecommendations,
    loadTopologicalOrder,
    searchKnowledgePoints,
    setSelectedKnowledgePoint,
    
    // 工具方法
    clearError: () => setError(null),
    refreshData: loadKnowledgeGraph
  }
}

/**
 * 简化版Hook，只提供只读数据
 * 适用于只需要展示数据的组件
 */
export function useKnowledgeGraphData() {
  const { 
    knowledgePoints, 
    dependencies, 
    isLoading, 
    error,
    getKnowledgeGraph,
    getRecommendations 
  } = useKnowledgeStore()

  return {
    knowledgePoints,
    dependencies,
    knowledgeGraph: getKnowledgeGraph(),
    recommendations: getRecommendations(),
    isLoading,
    error
  }
}
