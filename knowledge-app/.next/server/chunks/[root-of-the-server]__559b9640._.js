module.exports = {

"[project]/.next-internal/server/app/api/llm/test/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/llm/config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * LLM配置管理
 * 支持多种LLM提供商和模型的配置
 */ __turbopack_context__.s({
    "DEFAULT_LLM_CONFIG": ()=>DEFAULT_LLM_CONFIG,
    "LLM_PROVIDERS": ()=>LLM_PROVIDERS,
    "getCurrentLLMConfig": ()=>getCurrentLLMConfig,
    "getLLMConfigFromEnv": ()=>getLLMConfigFromEnv,
    "validateLLMConfig": ()=>validateLLMConfig
});
const LLM_PROVIDERS = {
    groq: {
        name: 'Groq',
        apiBase: 'https://api.groq.com/openai/v1',
        defaultModel: 'llama-3.1-70b-versatile',
        supportedModels: [
            'llama-3.1-70b-versatile',
            'llama-3.1-8b-instant',
            'mixtral-8x7b-32768',
            'gemma2-9b-it'
        ]
    },
    moonshot: {
        name: 'Moonshot AI',
        apiBase: 'https://api.moonshot.cn/v1',
        defaultModel: 'moonshot-v1-8k',
        supportedModels: [
            'moonshot-v1-8k',
            'moonshot-v1-32k',
            'moonshot-v1-128k',
            'moonshotai/kimi-k2-instruct'
        ]
    },
    openai: {
        name: 'OpenAI',
        apiBase: 'https://api.openai.com/v1',
        defaultModel: 'gpt-4o-mini',
        supportedModels: [
            'gpt-4o',
            'gpt-4o-mini',
            'gpt-4-turbo',
            'gpt-3.5-turbo'
        ]
    }
};
const DEFAULT_LLM_CONFIG = {
    provider: 'moonshot',
    apiKey: process.env.MOONSHOT_API_KEY || '',
    apiBase: LLM_PROVIDERS.moonshot.apiBase,
    model: 'moonshotai/kimi-k2-instruct',
    maxTokens: 4000,
    temperature: 0.7,
    timeout: 30000
};
function getLLMConfigFromEnv() {
    // 优先使用Groq配置（如果可用）
    if (process.env.GROQ_API_KEY) {
        return {
            provider: 'groq',
            apiKey: process.env.GROQ_API_KEY,
            apiBase: process.env.GROQ_API_BASE || LLM_PROVIDERS.groq.apiBase,
            model: process.env.GROQ_MODEL || LLM_PROVIDERS.groq.defaultModel,
            maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
            temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
            timeout: parseInt(process.env.LLM_TIMEOUT || '30000')
        };
    }
    // 使用Moonshot配置
    if (process.env.MOONSHOT_API_KEY) {
        return {
            provider: 'moonshot',
            apiKey: process.env.MOONSHOT_API_KEY,
            apiBase: process.env.MOONSHOT_API_BASE || LLM_PROVIDERS.moonshot.apiBase,
            model: process.env.MOONSHOT_MODEL || 'moonshotai/kimi-k2-instruct',
            maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
            temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
            timeout: parseInt(process.env.LLM_TIMEOUT || '30000')
        };
    }
    // 使用OpenAI配置
    if (process.env.OPENAI_API_KEY) {
        return {
            provider: 'openai',
            apiKey: process.env.OPENAI_API_KEY,
            apiBase: process.env.OPENAI_API_BASE || LLM_PROVIDERS.openai.apiBase,
            model: process.env.OPENAI_MODEL || LLM_PROVIDERS.openai.defaultModel,
            maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
            temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
            timeout: parseInt(process.env.LLM_TIMEOUT || '30000')
        };
    }
    // 返回默认配置（开发模式）
    return {
        ...DEFAULT_LLM_CONFIG,
        apiKey: 'mock-api-key' // 开发模式下的模拟key
    };
}
function validateLLMConfig(config) {
    if (!config.apiKey || config.apiKey === 'mock-api-key') {
        console.warn('LLM API Key未配置，将使用模拟模式');
        return false;
    }
    if (!config.model) {
        console.error('LLM模型未指定');
        return false;
    }
    const provider = LLM_PROVIDERS[config.provider];
    if (!provider) {
        console.error(`不支持的LLM提供商: ${config.provider}`);
        return false;
    }
    if (!provider.supportedModels.includes(config.model)) {
        console.warn(`模型 ${config.model} 可能不被 ${provider.name} 支持`);
    }
    return true;
}
function getCurrentLLMConfig() {
    const config = getLLMConfigFromEnv();
    if (!validateLLMConfig(config)) {
        console.log('使用模拟LLM配置进行开发');
    }
    return config;
}
}),
"[project]/src/lib/llm/service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * LLM服务实现
 * 支持多种LLM提供商的统一接口
 */ __turbopack_context__.s({
    "BaseLLMService": ()=>BaseLLMService,
    "llmService": ()=>llmService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/config.ts [app-route] (ecmascript)");
;
class BaseLLMService {
    config;
    constructor(config){
        this.config = config || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCurrentLLMConfig"])();
    }
    /**
   * 发送聊天请求
   */ async chat(options) {
        // 如果是模拟模式，返回模拟响应
        if (this.config.apiKey === 'mock-api-key') {
            return this.getMockResponse(options);
        }
        try {
            const response = await this.makeRequest(options);
            return this.parseResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    }
    /**
   * 发送流式聊天请求
   */ async *chatStream(options) {
        // 如果是模拟模式，返回模拟流响应
        if (this.config.apiKey === 'mock-api-key') {
            yield* this.getMockStreamResponse(options);
            return;
        }
        try {
            const response = await this.makeStreamRequest(options);
            yield* this.parseStreamResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    }
    /**
   * 检查服务是否可用
   */ async isAvailable() {
        if (this.config.apiKey === 'mock-api-key') {
            return true // 模拟模式总是可用
            ;
        }
        try {
            const testResponse = await this.chat({
                messages: [
                    {
                        role: 'user',
                        content: 'Hello'
                    }
                ],
                maxTokens: 10
            });
            return !!testResponse.content;
        } catch (error) {
            console.warn('LLM服务不可用:', error);
            return false;
        }
    }
    /**
   * 获取支持的模型列表
   */ getSupportedModels() {
        const provider = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LLM_PROVIDERS"][this.config.provider];
        return provider ? provider.supportedModels : [];
    }
    /**
   * 获取服务提供商名称
   */ getProviderName() {
        const provider = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LLM_PROVIDERS"][this.config.provider];
        return provider ? provider.name : this.config.provider;
    }
    /**
   * 发送HTTP请求到LLM API
   */ async makeRequest(options) {
        const provider = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LLM_PROVIDERS"][this.config.provider];
        const url = `${this.config.apiBase || provider.apiBase}/chat/completions`;
        const requestBody = {
            model: this.config.model,
            messages: options.messages,
            max_tokens: options.maxTokens || this.config.maxTokens,
            temperature: options.temperature ?? this.config.temperature,
            stream: false,
            ...options.stop && {
                stop: options.stop
            },
            ...options.topP && {
                top_p: options.topP
            },
            ...options.frequencyPenalty && {
                frequency_penalty: options.frequencyPenalty
            },
            ...options.presencePenalty && {
                presence_penalty: options.presencePenalty
            }
        };
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.config.apiKey}`,
                ...provider.headers
            },
            body: JSON.stringify(requestBody),
            signal: AbortSignal.timeout(this.config.timeout || 30000)
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response;
    }
    /**
   * 发送流式HTTP请求到LLM API
   */ async makeStreamRequest(options) {
        const provider = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LLM_PROVIDERS"][this.config.provider];
        const url = `${this.config.apiBase || provider.apiBase}/chat/completions`;
        const requestBody = {
            model: this.config.model,
            messages: options.messages,
            max_tokens: options.maxTokens || this.config.maxTokens,
            temperature: options.temperature ?? this.config.temperature,
            stream: true,
            ...options.stop && {
                stop: options.stop
            },
            ...options.topP && {
                top_p: options.topP
            },
            ...options.frequencyPenalty && {
                frequency_penalty: options.frequencyPenalty
            },
            ...options.presencePenalty && {
                presence_penalty: options.presencePenalty
            }
        };
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.config.apiKey}`,
                ...provider.headers
            },
            body: JSON.stringify(requestBody),
            signal: AbortSignal.timeout(this.config.timeout || 30000)
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response;
    }
    /**
   * 解析LLM响应
   */ async parseResponse(response) {
        const data = await response.json();
        return {
            content: data.choices[0]?.message?.content || '',
            usage: data.usage,
            model: data.model,
            finish_reason: data.choices[0]?.finish_reason
        };
    }
    /**
   * 解析流式LLM响应
   */ async *parseStreamResponse(response) {
        const reader = response.body?.getReader();
        if (!reader) {
            throw new Error('无法读取响应流');
        }
        const decoder = new TextDecoder();
        let buffer = '';
        try {
            while(true){
                const { done, value } = await reader.read();
                if (done) break;
                buffer += decoder.decode(value, {
                    stream: true
                });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';
                for (const line of lines){
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') {
                            return;
                        }
                        try {
                            const parsed = JSON.parse(data);
                            const content = parsed.choices[0]?.delta?.content || '';
                            yield {
                                content,
                                done: false,
                                usage: parsed.usage
                            };
                        } catch (error) {
                            console.warn('解析流数据失败:', error);
                        }
                    }
                }
            }
        } finally{
            reader.releaseLock();
        }
        yield {
            content: '',
            done: true
        };
    }
    /**
   * 处理错误
   */ handleError(error) {
        if (error.name === 'AbortError') {
            return {
                code: 'timeout',
                message: 'LLM请求超时',
                type: 'network_error',
                details: error
            };
        }
        if (error.message?.includes('401')) {
            return {
                code: 'unauthorized',
                message: 'API密钥无效或已过期',
                type: 'authentication_error',
                details: error
            };
        }
        if (error.message?.includes('429')) {
            return {
                code: 'rate_limit',
                message: 'API调用频率超限',
                type: 'rate_limit',
                details: error
            };
        }
        return {
            code: 'unknown',
            message: error.message || 'LLM服务错误',
            type: 'api_error',
            details: error
        };
    }
    /**
   * 获取模拟响应（开发模式）
   */ getMockResponse(options) {
        const userMessage = options.messages.find((m)=>m.role === 'user')?.content || '';
        let mockContent = '这是一个模拟的LLM响应。';
        if (userMessage.includes('知识') || userMessage.includes('学习')) {
            mockContent = '基于您的请求，我建议从基础概念开始学习，然后逐步深入到高级主题。';
        } else if (userMessage.includes('依赖') || userMessage.includes('前置')) {
            mockContent = '分析您提到的概念，需要先掌握以下前置知识：1. 基础概念 2. 相关理论 3. 实践技能';
        }
        return {
            content: mockContent,
            usage: {
                prompt_tokens: userMessage.length,
                completion_tokens: mockContent.length,
                total_tokens: userMessage.length + mockContent.length
            },
            model: this.config.model,
            finish_reason: 'stop'
        };
    }
    /**
   * 获取模拟流响应（开发模式）
   */ async *getMockStreamResponse(options) {
        const response = this.getMockResponse(options);
        const words = response.content.split('');
        for(let i = 0; i < words.length; i++){
            await new Promise((resolve)=>setTimeout(resolve, 50)); // 模拟流式延迟
            yield {
                content: words[i],
                done: false
            };
        }
        yield {
            content: '',
            done: true,
            usage: response.usage
        };
    }
}
const llmService = new BaseLLMService();
}),
"[project]/src/app/api/llm/test/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/service.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const { message } = await request.json();
        if (!message) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '消息内容不能为空'
            }, {
                status: 400
            });
        }
        // 测试LLM服务
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
            messages: [
                {
                    role: 'user',
                    content: message
                }
            ],
            maxTokens: 100,
            temperature: 0.7
        });
        // 检查服务可用性
        const isAvailable = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].isAvailable();
        const supportedModels = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].getSupportedModels();
        const providerName = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].getProviderName();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            response: response.content,
            serviceInfo: {
                isAvailable,
                providerName,
                supportedModels,
                usage: response.usage
            },
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('LLM测试失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'LLM服务测试失败',
            details: error instanceof Error ? error.message : '未知错误'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__559b9640._.js.map