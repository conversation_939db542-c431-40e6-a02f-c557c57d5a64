{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/llm/config.ts"], "sourcesContent": ["/**\n * LLM配置管理\n * 支持多种LLM提供商和模型的配置\n */\n\nexport interface LLMConfig {\n  provider: 'groq' | 'moonshot' | 'openai' | 'anthropic'\n  apiKey: string\n  apiBase?: string\n  model: string\n  maxTokens?: number\n  temperature?: number\n  timeout?: number\n}\n\nexport interface LLMProviderConfig {\n  name: string\n  apiBase: string\n  defaultModel: string\n  supportedModels: string[]\n  headers?: Record<string, string>\n}\n\n/**\n * 支持的LLM提供商配置\n */\nexport const LLM_PROVIDERS: Record<string, LLMProviderConfig> = {\n  groq: {\n    name: 'Groq',\n    apiBase: 'https://api.groq.com/openai/v1',\n    defaultModel: 'llama-3.1-70b-versatile',\n    supportedModels: [\n      'llama-3.1-70b-versatile',\n      'llama-3.1-8b-instant',\n      'mixtral-8x7b-32768',\n      'gemma2-9b-it'\n    ]\n  },\n  moonshot: {\n    name: 'Moonshot AI',\n    apiBase: 'https://api.moonshot.cn/v1',\n    defaultModel: 'moonshot-v1-8k',\n    supportedModels: [\n      'moonshot-v1-8k',\n      'moonshot-v1-32k',\n      'moonshot-v1-128k',\n      'moonshotai/kimi-k2-instruct'\n    ]\n  },\n  openai: {\n    name: 'OpenAI',\n    apiBase: 'https://api.openai.com/v1',\n    defaultModel: 'gpt-4o-mini',\n    supportedModels: [\n      'gpt-4o',\n      'gpt-4o-mini',\n      'gpt-4-turbo',\n      'gpt-3.5-turbo'\n    ]\n  }\n}\n\n/**\n * 默认LLM配置\n */\nexport const DEFAULT_LLM_CONFIG: LLMConfig = {\n  provider: 'moonshot',\n  apiKey: process.env.MOONSHOT_API_KEY || '',\n  apiBase: LLM_PROVIDERS.moonshot.apiBase,\n  model: 'moonshotai/kimi-k2-instruct',\n  maxTokens: 4000,\n  temperature: 0.7,\n  timeout: 30000\n}\n\n/**\n * 从环境变量获取LLM配置\n */\nexport function getLLMConfigFromEnv(): LLMConfig {\n  // 优先使用Groq配置（如果可用）\n  if (process.env.GROQ_API_KEY) {\n    return {\n      provider: 'groq',\n      apiKey: process.env.GROQ_API_KEY,\n      apiBase: process.env.GROQ_API_BASE || LLM_PROVIDERS.groq.apiBase,\n      model: process.env.GROQ_MODEL || LLM_PROVIDERS.groq.defaultModel,\n      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),\n      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),\n      timeout: parseInt(process.env.LLM_TIMEOUT || '30000')\n    }\n  }\n\n  // 使用Moonshot配置\n  if (process.env.MOONSHOT_API_KEY) {\n    return {\n      provider: 'moonshot',\n      apiKey: process.env.MOONSHOT_API_KEY,\n      apiBase: process.env.MOONSHOT_API_BASE || LLM_PROVIDERS.moonshot.apiBase,\n      model: process.env.MOONSHOT_MODEL || 'moonshotai/kimi-k2-instruct',\n      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),\n      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),\n      timeout: parseInt(process.env.LLM_TIMEOUT || '30000')\n    }\n  }\n\n  // 使用OpenAI配置\n  if (process.env.OPENAI_API_KEY) {\n    return {\n      provider: 'openai',\n      apiKey: process.env.OPENAI_API_KEY,\n      apiBase: process.env.OPENAI_API_BASE || LLM_PROVIDERS.openai.apiBase,\n      model: process.env.OPENAI_MODEL || LLM_PROVIDERS.openai.defaultModel,\n      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),\n      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),\n      timeout: parseInt(process.env.LLM_TIMEOUT || '30000')\n    }\n  }\n\n  // 返回默认配置（开发模式）\n  return {\n    ...DEFAULT_LLM_CONFIG,\n    apiKey: 'mock-api-key' // 开发模式下的模拟key\n  }\n}\n\n/**\n * 验证LLM配置\n */\nexport function validateLLMConfig(config: LLMConfig): boolean {\n  if (!config.apiKey || config.apiKey === 'mock-api-key') {\n    console.warn('LLM API Key未配置，将使用模拟模式')\n    return false\n  }\n\n  if (!config.model) {\n    console.error('LLM模型未指定')\n    return false\n  }\n\n  const provider = LLM_PROVIDERS[config.provider]\n  if (!provider) {\n    console.error(`不支持的LLM提供商: ${config.provider}`)\n    return false\n  }\n\n  if (!provider.supportedModels.includes(config.model)) {\n    console.warn(`模型 ${config.model} 可能不被 ${provider.name} 支持`)\n  }\n\n  return true\n}\n\n/**\n * 获取当前LLM配置\n */\nexport function getCurrentLLMConfig(): LLMConfig {\n  const config = getLLMConfigFromEnv()\n  \n  if (!validateLLMConfig(config)) {\n    console.log('使用模拟LLM配置进行开发')\n  }\n\n  return config\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAuBM,MAAM,gBAAmD;IAC9D,MAAM;QACJ,MAAM;QACN,SAAS;QACT,cAAc;QACd,iBAAiB;YACf;YACA;YACA;YACA;SACD;IACH;IACA,UAAU;QACR,MAAM;QACN,SAAS;QACT,cAAc;QACd,iBAAiB;YACf;YACA;YACA;YACA;SACD;IACH;IACA,QAAQ;QACN,MAAM;QACN,SAAS;QACT,cAAc;QACd,iBAAiB;YACf;YACA;YACA;YACA;SACD;IACH;AACF;AAKO,MAAM,qBAAgC;IAC3C,UAAU;IACV,QAAQ,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IACxC,SAAS,cAAc,QAAQ,CAAC,OAAO;IACvC,OAAO;IACP,WAAW;IACX,aAAa;IACb,SAAS;AACX;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO;YACL,UAAU;YACV,QAAQ,QAAQ,GAAG,CAAC,YAAY;YAChC,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI,cAAc,IAAI,CAAC,OAAO;YAChE,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI,cAAc,IAAI,CAAC,YAAY;YAChE,WAAW,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;YAClD,aAAa,WAAW,QAAQ,GAAG,CAAC,eAAe,IAAI;YACvD,SAAS,SAAS,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC/C;IACF;IAEA,eAAe;IACf,IAAI,QAAQ,GAAG,CAAC,gBAAgB,EAAE;QAChC,OAAO;YACL,UAAU;YACV,QAAQ,QAAQ,GAAG,CAAC,gBAAgB;YACpC,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI,cAAc,QAAQ,CAAC,OAAO;YACxE,OAAO,QAAQ,GAAG,CAAC,cAAc,IAAI;YACrC,WAAW,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;YAClD,aAAa,WAAW,QAAQ,GAAG,CAAC,eAAe,IAAI;YACvD,SAAS,SAAS,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC/C;IACF;IAEA,aAAa;IACb,IAAI,QAAQ,GAAG,CAAC,cAAc,EAAE;QAC9B,OAAO;YACL,UAAU;YACV,QAAQ,QAAQ,GAAG,CAAC,cAAc;YAClC,SAAS,QAAQ,GAAG,CAAC,eAAe,IAAI,cAAc,MAAM,CAAC,OAAO;YACpE,OAAO,QAAQ,GAAG,CAAC,YAAY,IAAI,cAAc,MAAM,CAAC,YAAY;YACpE,WAAW,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;YAClD,aAAa,WAAW,QAAQ,GAAG,CAAC,eAAe,IAAI;YACvD,SAAS,SAAS,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC/C;IACF;IAEA,eAAe;IACf,OAAO;QACL,GAAG,kBAAkB;QACrB,QAAQ,eAAe,cAAc;IACvC;AACF;AAKO,SAAS,kBAAkB,MAAiB;IACjD,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,gBAAgB;QACtD,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI,CAAC,OAAO,KAAK,EAAE;QACjB,QAAQ,KAAK,CAAC;QACd,OAAO;IACT;IAEA,MAAM,WAAW,aAAa,CAAC,OAAO,QAAQ,CAAC;IAC/C,IAAI,CAAC,UAAU;QACb,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,OAAO,QAAQ,EAAE;QAC9C,OAAO;IACT;IAEA,IAAI,CAAC,SAAS,eAAe,CAAC,QAAQ,CAAC,OAAO,KAAK,GAAG;QACpD,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,GAAG,CAAC;IAC5D;IAEA,OAAO;AACT;AAKO,SAAS;IACd,MAAM,SAAS;IAEf,IAAI,CAAC,kBAAkB,SAAS;QAC9B,QAAQ,GAAG,CAAC;IACd;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/llm/service.ts"], "sourcesContent": ["/**\n * LLM服务实现\n * 支持多种LLM提供商的统一接口\n */\n\nimport { LLMConfig, LLM_PROVIDERS, getCurrentLLMConfig } from './config'\nimport { \n  LLMService, \n  LLMMessage, \n  LLMResponse, \n  LLMStreamResponse, \n  LLMRequestOptions, \n  LLMError \n} from './types'\n\n/**\n * 基础LLM服务实现\n */\nexport class BaseLLMService implements LLMService {\n  protected config: LLMConfig\n\n  constructor(config?: LLMConfig) {\n    this.config = config || getCurrentLLMConfig()\n  }\n\n  /**\n   * 发送聊天请求\n   */\n  async chat(options: LLMRequestOptions): Promise<LLMResponse> {\n    // 如果是模拟模式，返回模拟响应\n    if (this.config.apiKey === 'mock-api-key') {\n      return this.getMockResponse(options)\n    }\n\n    try {\n      const response = await this.makeRequest(options)\n      return this.parseResponse(response)\n    } catch (error) {\n      throw this.handleError(error)\n    }\n  }\n\n  /**\n   * 发送流式聊天请求\n   */\n  async* chatStream(options: LLMRequestOptions): AsyncGenerator<LLMStreamResponse, void, unknown> {\n    // 如果是模拟模式，返回模拟流响应\n    if (this.config.apiKey === 'mock-api-key') {\n      yield* this.getMockStreamResponse(options)\n      return\n    }\n\n    try {\n      const response = await this.makeStreamRequest(options)\n      yield* this.parseStreamResponse(response)\n    } catch (error) {\n      throw this.handleError(error)\n    }\n  }\n\n  /**\n   * 检查服务是否可用\n   */\n  async isAvailable(): Promise<boolean> {\n    if (this.config.apiKey === 'mock-api-key') {\n      return true // 模拟模式总是可用\n    }\n\n    try {\n      const testResponse = await this.chat({\n        messages: [{ role: 'user', content: 'Hello' }],\n        maxTokens: 10\n      })\n      return !!testResponse.content\n    } catch (error) {\n      console.warn('LLM服务不可用:', error)\n      return false\n    }\n  }\n\n  /**\n   * 获取支持的模型列表\n   */\n  getSupportedModels(): string[] {\n    const provider = LLM_PROVIDERS[this.config.provider]\n    return provider ? provider.supportedModels : []\n  }\n\n  /**\n   * 获取服务提供商名称\n   */\n  getProviderName(): string {\n    const provider = LLM_PROVIDERS[this.config.provider]\n    return provider ? provider.name : this.config.provider\n  }\n\n  /**\n   * 发送HTTP请求到LLM API\n   */\n  protected async makeRequest(options: LLMRequestOptions): Promise<Response> {\n    const provider = LLM_PROVIDERS[this.config.provider]\n    const url = `${this.config.apiBase || provider.apiBase}/chat/completions`\n\n    const requestBody = {\n      model: this.config.model,\n      messages: options.messages,\n      max_tokens: options.maxTokens || this.config.maxTokens,\n      temperature: options.temperature ?? this.config.temperature,\n      stream: false,\n      ...(options.stop && { stop: options.stop }),\n      ...(options.topP && { top_p: options.topP }),\n      ...(options.frequencyPenalty && { frequency_penalty: options.frequencyPenalty }),\n      ...(options.presencePenalty && { presence_penalty: options.presencePenalty })\n    }\n\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${this.config.apiKey}`,\n        ...provider.headers\n      },\n      body: JSON.stringify(requestBody),\n      signal: AbortSignal.timeout(this.config.timeout || 30000)\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    return response\n  }\n\n  /**\n   * 发送流式HTTP请求到LLM API\n   */\n  protected async makeStreamRequest(options: LLMRequestOptions): Promise<Response> {\n    const provider = LLM_PROVIDERS[this.config.provider]\n    const url = `${this.config.apiBase || provider.apiBase}/chat/completions`\n\n    const requestBody = {\n      model: this.config.model,\n      messages: options.messages,\n      max_tokens: options.maxTokens || this.config.maxTokens,\n      temperature: options.temperature ?? this.config.temperature,\n      stream: true,\n      ...(options.stop && { stop: options.stop }),\n      ...(options.topP && { top_p: options.topP }),\n      ...(options.frequencyPenalty && { frequency_penalty: options.frequencyPenalty }),\n      ...(options.presencePenalty && { presence_penalty: options.presencePenalty })\n    }\n\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${this.config.apiKey}`,\n        ...provider.headers\n      },\n      body: JSON.stringify(requestBody),\n      signal: AbortSignal.timeout(this.config.timeout || 30000)\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    return response\n  }\n\n  /**\n   * 解析LLM响应\n   */\n  protected async parseResponse(response: Response): Promise<LLMResponse> {\n    const data = await response.json()\n    \n    return {\n      content: data.choices[0]?.message?.content || '',\n      usage: data.usage,\n      model: data.model,\n      finish_reason: data.choices[0]?.finish_reason\n    }\n  }\n\n  /**\n   * 解析流式LLM响应\n   */\n  protected async* parseStreamResponse(response: Response): AsyncGenerator<LLMStreamResponse, void, unknown> {\n    const reader = response.body?.getReader()\n    if (!reader) {\n      throw new Error('无法读取响应流')\n    }\n\n    const decoder = new TextDecoder()\n    let buffer = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        buffer += decoder.decode(value, { stream: true })\n        const lines = buffer.split('\\n')\n        buffer = lines.pop() || ''\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6)\n            if (data === '[DONE]') {\n              return\n            }\n\n            try {\n              const parsed = JSON.parse(data)\n              const content = parsed.choices[0]?.delta?.content || ''\n              \n              yield {\n                content,\n                done: false,\n                usage: parsed.usage\n              }\n            } catch (error) {\n              console.warn('解析流数据失败:', error)\n            }\n          }\n        }\n      }\n    } finally {\n      reader.releaseLock()\n    }\n\n    yield {\n      content: '',\n      done: true\n    }\n  }\n\n  /**\n   * 处理错误\n   */\n  protected handleError(error: any): LLMError {\n    if (error.name === 'AbortError') {\n      return {\n        code: 'timeout',\n        message: 'LLM请求超时',\n        type: 'network_error',\n        details: error\n      }\n    }\n\n    if (error.message?.includes('401')) {\n      return {\n        code: 'unauthorized',\n        message: 'API密钥无效或已过期',\n        type: 'authentication_error',\n        details: error\n      }\n    }\n\n    if (error.message?.includes('429')) {\n      return {\n        code: 'rate_limit',\n        message: 'API调用频率超限',\n        type: 'rate_limit',\n        details: error\n      }\n    }\n\n    return {\n      code: 'unknown',\n      message: error.message || 'LLM服务错误',\n      type: 'api_error',\n      details: error\n    }\n  }\n\n  /**\n   * 获取模拟响应（开发模式）\n   */\n  protected getMockResponse(options: LLMRequestOptions): LLMResponse {\n    const userMessage = options.messages.find(m => m.role === 'user')?.content || ''\n    \n    let mockContent = '这是一个模拟的LLM响应。'\n    \n    if (userMessage.includes('知识') || userMessage.includes('学习')) {\n      mockContent = '基于您的请求，我建议从基础概念开始学习，然后逐步深入到高级主题。'\n    } else if (userMessage.includes('依赖') || userMessage.includes('前置')) {\n      mockContent = '分析您提到的概念，需要先掌握以下前置知识：1. 基础概念 2. 相关理论 3. 实践技能'\n    }\n\n    return {\n      content: mockContent,\n      usage: {\n        prompt_tokens: userMessage.length,\n        completion_tokens: mockContent.length,\n        total_tokens: userMessage.length + mockContent.length\n      },\n      model: this.config.model,\n      finish_reason: 'stop'\n    }\n  }\n\n  /**\n   * 获取模拟流响应（开发模式）\n   */\n  protected async* getMockStreamResponse(options: LLMRequestOptions): AsyncGenerator<LLMStreamResponse, void, unknown> {\n    const response = this.getMockResponse(options)\n    const words = response.content.split('')\n    \n    for (let i = 0; i < words.length; i++) {\n      await new Promise(resolve => setTimeout(resolve, 50)) // 模拟流式延迟\n      \n      yield {\n        content: words[i],\n        done: false\n      }\n    }\n\n    yield {\n      content: '',\n      done: true,\n      usage: response.usage\n    }\n  }\n}\n\n/**\n * 全局LLM服务实例\n */\nexport const llmService = new BaseLLMService()\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAaO,MAAM;IACD,OAAiB;IAE3B,YAAY,MAAkB,CAAE;QAC9B,IAAI,CAAC,MAAM,GAAG,UAAU,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD;IAC5C;IAEA;;GAEC,GACD,MAAM,KAAK,OAA0B,EAAwB;QAC3D,iBAAiB;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,gBAAgB;YACzC,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;YACxC,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,CAAC,WAAW,CAAC;QACzB;IACF;IAEA;;GAEC,GACD,OAAO,WAAW,OAA0B,EAAoD;QAC9F,kBAAkB;QAClB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,gBAAgB;YACzC,OAAO,IAAI,CAAC,qBAAqB,CAAC;YAClC;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAC9C,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,CAAC,WAAW,CAAC;QACzB;IACF;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,gBAAgB;YACzC,OAAO,KAAK,WAAW;;QACzB;QAEA,IAAI;YACF,MAAM,eAAe,MAAM,IAAI,CAAC,IAAI,CAAC;gBACnC,UAAU;oBAAC;wBAAE,MAAM;wBAAQ,SAAS;oBAAQ;iBAAE;gBAC9C,WAAW;YACb;YACA,OAAO,CAAC,CAAC,aAAa,OAAO;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,aAAa;YAC1B,OAAO;QACT;IACF;IAEA;;GAEC,GACD,qBAA+B;QAC7B,MAAM,WAAW,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpD,OAAO,WAAW,SAAS,eAAe,GAAG,EAAE;IACjD;IAEA;;GAEC,GACD,kBAA0B;QACxB,MAAM,WAAW,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpD,OAAO,WAAW,SAAS,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ;IACxD;IAEA;;GAEC,GACD,MAAgB,YAAY,OAA0B,EAAqB;QACzE,MAAM,WAAW,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,SAAS,OAAO,CAAC,iBAAiB,CAAC;QAEzE,MAAM,cAAc;YAClB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,UAAU,QAAQ,QAAQ;YAC1B,YAAY,QAAQ,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;YACtD,aAAa,QAAQ,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;YAC3D,QAAQ;YACR,GAAI,QAAQ,IAAI,IAAI;gBAAE,MAAM,QAAQ,IAAI;YAAC,CAAC;YAC1C,GAAI,QAAQ,IAAI,IAAI;gBAAE,OAAO,QAAQ,IAAI;YAAC,CAAC;YAC3C,GAAI,QAAQ,gBAAgB,IAAI;gBAAE,mBAAmB,QAAQ,gBAAgB;YAAC,CAAC;YAC/E,GAAI,QAAQ,eAAe,IAAI;gBAAE,kBAAkB,QAAQ,eAAe;YAAC,CAAC;QAC9E;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC/C,GAAG,SAAS,OAAO;YACrB;YACA,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI;QACrD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAgB,kBAAkB,OAA0B,EAAqB;QAC/E,MAAM,WAAW,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,SAAS,OAAO,CAAC,iBAAiB,CAAC;QAEzE,MAAM,cAAc;YAClB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,UAAU,QAAQ,QAAQ;YAC1B,YAAY,QAAQ,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;YACtD,aAAa,QAAQ,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;YAC3D,QAAQ;YACR,GAAI,QAAQ,IAAI,IAAI;gBAAE,MAAM,QAAQ,IAAI;YAAC,CAAC;YAC1C,GAAI,QAAQ,IAAI,IAAI;gBAAE,OAAO,QAAQ,IAAI;YAAC,CAAC;YAC3C,GAAI,QAAQ,gBAAgB,IAAI;gBAAE,mBAAmB,QAAQ,gBAAgB;YAAC,CAAC;YAC/E,GAAI,QAAQ,eAAe,IAAI;gBAAE,kBAAkB,QAAQ,eAAe;YAAC,CAAC;QAC9E;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC/C,GAAG,SAAS,OAAO;YACrB;YACA,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI;QACrD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAgB,cAAc,QAAkB,EAAwB;QACtE,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL,SAAS,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;YAC9C,OAAO,KAAK,KAAK;YACjB,OAAO,KAAK,KAAK;YACjB,eAAe,KAAK,OAAO,CAAC,EAAE,EAAE;QAClC;IACF;IAEA;;GAEC,GACD,OAAiB,oBAAoB,QAAkB,EAAoD;QACzG,MAAM,SAAS,SAAS,IAAI,EAAE;QAC9B,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,UAAU,IAAI;QACpB,IAAI,SAAS;QAEb,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,UAAU,QAAQ,MAAM,CAAC,OAAO;oBAAE,QAAQ;gBAAK;gBAC/C,MAAM,QAAQ,OAAO,KAAK,CAAC;gBAC3B,SAAS,MAAM,GAAG,MAAM;gBAExB,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;wBACxB,IAAI,SAAS,UAAU;4BACrB;wBACF;wBAEA,IAAI;4BACF,MAAM,SAAS,KAAK,KAAK,CAAC;4BAC1B,MAAM,UAAU,OAAO,OAAO,CAAC,EAAE,EAAE,OAAO,WAAW;4BAErD,MAAM;gCACJ;gCACA,MAAM;gCACN,OAAO,OAAO,KAAK;4BACrB;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,IAAI,CAAC,YAAY;wBAC3B;oBACF;gBACF;YACF;QACF,SAAU;YACR,OAAO,WAAW;QACpB;QAEA,MAAM;YACJ,SAAS;YACT,MAAM;QACR;IACF;IAEA;;GAEC,GACD,AAAU,YAAY,KAAU,EAAY;QAC1C,IAAI,MAAM,IAAI,KAAK,cAAc;YAC/B,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;QACF;QAEA,IAAI,MAAM,OAAO,EAAE,SAAS,QAAQ;YAClC,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;QACF;QAEA,IAAI,MAAM,OAAO,EAAE,SAAS,QAAQ;YAClC,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS,MAAM,OAAO,IAAI;YAC1B,MAAM;YACN,SAAS;QACX;IACF;IAEA;;GAEC,GACD,AAAU,gBAAgB,OAA0B,EAAe;QACjE,MAAM,cAAc,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,WAAW;QAE9E,IAAI,cAAc;QAElB,IAAI,YAAY,QAAQ,CAAC,SAAS,YAAY,QAAQ,CAAC,OAAO;YAC5D,cAAc;QAChB,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS,YAAY,QAAQ,CAAC,OAAO;YACnE,cAAc;QAChB;QAEA,OAAO;YACL,SAAS;YACT,OAAO;gBACL,eAAe,YAAY,MAAM;gBACjC,mBAAmB,YAAY,MAAM;gBACrC,cAAc,YAAY,MAAM,GAAG,YAAY,MAAM;YACvD;YACA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,OAAiB,sBAAsB,OAA0B,EAAoD;QACnH,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QACtC,MAAM,QAAQ,SAAS,OAAO,CAAC,KAAK,CAAC;QAErC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,MAAK,SAAS;YAE/D,MAAM;gBACJ,SAAS,KAAK,CAAC,EAAE;gBACjB,MAAM;YACR;QACF;QAEA,MAAM;YACJ,SAAS;YACT,MAAM;YACN,OAAO,SAAS,KAAK;QACvB;IACF;AACF;AAKO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/llm/knowledgeGenerator.ts"], "sourcesContent": ["/**\n * 智能知识生成服务\n * 基于用户兴趣和需求，使用LLM生成个性化学习内容\n */\n\nimport { llmService } from './service'\nimport { \n  KnowledgeGenerationRequest, \n  GeneratedKnowledge,\n  LLMMessage \n} from './types'\n\n/**\n * 知识生成服务类\n */\nexport class KnowledgeGeneratorService {\n  \n  /**\n   * 生成知识内容\n   */\n  async generateKnowledge(request: KnowledgeGenerationRequest): Promise<GeneratedKnowledge> {\n    const prompt = this.buildKnowledgeGenerationPrompt(request)\n    \n    try {\n      const response = await llmService.chat({\n        messages: [\n          {\n            role: 'system',\n            content: this.getSystemPrompt()\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.7,\n        maxTokens: 3000\n      })\n\n      return this.parseKnowledgeResponse(response.content, request)\n    } catch (error) {\n      console.error('知识生成失败:', error)\n      throw new Error('无法生成知识内容，请稍后重试')\n    }\n  }\n\n  /**\n   * 批量生成相关知识点\n   */\n  async generateRelatedKnowledge(\n    baseTopic: string, \n    count: number = 5\n  ): Promise<GeneratedKnowledge[]> {\n    const prompt = `请为主题\"${baseTopic}\"生成${count}个相关的知识点，每个知识点应该：\n1. 与主题相关但各有侧重\n2. 难度递进或并列\n3. 包含实用的学习内容\n\n请以JSON数组格式返回，每个知识点包含：title, description, difficulty_level, estimated_time, prerequisites, tags`\n\n    try {\n      const response = await llmService.chat({\n        messages: [\n          {\n            role: 'system',\n            content: this.getSystemPrompt()\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.8,\n        maxTokens: 4000\n      })\n\n      return this.parseMultipleKnowledgeResponse(response.content)\n    } catch (error) {\n      console.error('批量知识生成失败:', error)\n      throw new Error('无法生成相关知识点，请稍后重试')\n    }\n  }\n\n  /**\n   * 根据用户兴趣推荐学习主题\n   */\n  async recommendTopics(\n    userInterests: string[], \n    currentLevel: string = 'beginner'\n  ): Promise<string[]> {\n    const prompt = `基于用户兴趣：${userInterests.join(', ')}，当前水平：${currentLevel}\n请推荐10个适合的学习主题，要求：\n1. 与用户兴趣相关\n2. 适合当前水平\n3. 有实际应用价值\n4. 学习路径清晰\n\n请以简洁的主题名称列表返回，每行一个主题。`\n\n    try {\n      const response = await llmService.chat({\n        messages: [\n          {\n            role: 'system',\n            content: '你是一个专业的学习顾问，擅长根据用户兴趣推荐合适的学习主题。'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.6,\n        maxTokens: 1000\n      })\n\n      return this.parseTopicRecommendations(response.content)\n    } catch (error) {\n      console.error('主题推荐失败:', error)\n      return [] // 返回空数组而不是抛出错误\n    }\n  }\n\n  /**\n   * 构建知识生成提示词\n   */\n  private buildKnowledgeGenerationPrompt(request: KnowledgeGenerationRequest): string {\n    const { topic, userLevel = 'beginner', context, preferences } = request\n\n    let prompt = `请为主题\"${topic}\"生成详细的学习内容。\n\n用户水平：${userLevel}\n${context ? `背景信息：${context}` : ''}\n\n要求：\n1. 内容要准确、实用、易懂\n2. 适合${userLevel}水平的学习者\n3. 包含清晰的概念解释和实例\n4. 估算学习时间和难度等级（1-10）\n5. 列出必要的前置知识\n6. 添加相关标签\n\n${preferences?.includeExamples ? '请包含具体的代码示例或实际案例。' : ''}\n${preferences?.includeExercises ? '请包含练习题或实践任务。' : ''}\n${preferences?.focusAreas?.length ? `重点关注：${preferences.focusAreas.join(', ')}` : ''}\n\n请以结构化的格式返回，包含：\n- 标题\n- 描述\n- 详细内容\n- 难度等级（1-10）\n- 估计学习时间（分钟）\n- 前置知识列表\n- 标签列表\n${preferences?.includeExamples ? '- 示例列表' : ''}\n${preferences?.includeExercises ? '- 练习列表' : ''}`\n\n    return prompt\n  }\n\n  /**\n   * 获取系统提示词\n   */\n  private getSystemPrompt(): string {\n    return `你是一个专业的教育内容生成专家，擅长创建高质量的学习材料。你的任务是：\n\n1. 生成准确、实用的学习内容\n2. 根据用户水平调整内容难度\n3. 提供清晰的学习路径和时间估算\n4. 确保内容的逻辑性和连贯性\n5. 包含实际应用和案例\n\n请始终以学习者的角度思考，确保内容易于理解和实践。`\n  }\n\n  /**\n   * 解析知识生成响应\n   */\n  private parseKnowledgeResponse(\n    content: string, \n    request: KnowledgeGenerationRequest\n  ): GeneratedKnowledge {\n    try {\n      // 尝试解析JSON格式的响应\n      if (content.includes('{') && content.includes('}')) {\n        const jsonMatch = content.match(/\\{[\\s\\S]*\\}/)\n        if (jsonMatch) {\n          const parsed = JSON.parse(jsonMatch[0])\n          return this.normalizeKnowledgeObject(parsed)\n        }\n      }\n\n      // 如果不是JSON格式，则解析文本格式\n      return this.parseTextKnowledgeResponse(content, request)\n    } catch (error) {\n      console.warn('解析知识响应失败，使用文本解析:', error)\n      return this.parseTextKnowledgeResponse(content, request)\n    }\n  }\n\n  /**\n   * 解析文本格式的知识响应\n   */\n  private parseTextKnowledgeResponse(\n    content: string, \n    request: KnowledgeGenerationRequest\n  ): GeneratedKnowledge {\n    const lines = content.split('\\n').filter(line => line.trim())\n    \n    return {\n      title: request.topic,\n      description: `关于${request.topic}的学习内容`,\n      content: content,\n      difficulty_level: this.extractDifficultyLevel(content),\n      estimated_time: this.extractEstimatedTime(content),\n      prerequisites: this.extractPrerequisites(content),\n      tags: this.extractTags(content, request.topic),\n      examples: request.preferences?.includeExamples ? this.extractExamples(content) : undefined,\n      exercises: request.preferences?.includeExercises ? this.extractExercises(content) : undefined\n    }\n  }\n\n  /**\n   * 解析多个知识点响应\n   */\n  private parseMultipleKnowledgeResponse(content: string): GeneratedKnowledge[] {\n    try {\n      // 尝试解析JSON数组\n      const jsonMatch = content.match(/\\[[\\s\\S]*\\]/)\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0])\n        return parsed.map((item: any) => this.normalizeKnowledgeObject(item))\n      }\n\n      // 如果不是JSON格式，返回空数组\n      return []\n    } catch (error) {\n      console.warn('解析多个知识点响应失败:', error)\n      return []\n    }\n  }\n\n  /**\n   * 解析主题推荐\n   */\n  private parseTopicRecommendations(content: string): string[] {\n    return content\n      .split('\\n')\n      .map(line => line.trim())\n      .filter(line => line && !line.startsWith('#'))\n      .map(line => line.replace(/^\\d+\\.\\s*/, '').replace(/^-\\s*/, ''))\n      .slice(0, 10) // 限制最多10个主题\n  }\n\n  /**\n   * 标准化知识对象\n   */\n  private normalizeKnowledgeObject(obj: any): GeneratedKnowledge {\n    return {\n      title: obj.title || obj.name || '未命名知识点',\n      description: obj.description || obj.desc || '',\n      content: obj.content || obj.details || obj.description || '',\n      difficulty_level: parseInt(obj.difficulty_level || obj.difficulty || '5'),\n      estimated_time: parseInt(obj.estimated_time || obj.time || '30'),\n      prerequisites: Array.isArray(obj.prerequisites) ? obj.prerequisites : [],\n      tags: Array.isArray(obj.tags) ? obj.tags : [],\n      examples: Array.isArray(obj.examples) ? obj.examples : undefined,\n      exercises: Array.isArray(obj.exercises) ? obj.exercises : undefined\n    }\n  }\n\n  /**\n   * 提取难度等级\n   */\n  private extractDifficultyLevel(content: string): number {\n    const match = content.match(/难度[：:]\\s*(\\d+)/i) || \n                  content.match(/difficulty[：:]\\s*(\\d+)/i)\n    return match ? parseInt(match[1]) : 5\n  }\n\n  /**\n   * 提取估计时间\n   */\n  private extractEstimatedTime(content: string): number {\n    const match = content.match(/时间[：:]\\s*(\\d+)/i) || \n                  content.match(/time[：:]\\s*(\\d+)/i) ||\n                  content.match(/(\\d+)\\s*分钟/i)\n    return match ? parseInt(match[1]) : 30\n  }\n\n  /**\n   * 提取前置知识\n   */\n  private extractPrerequisites(content: string): string[] {\n    const section = this.extractSection(content, ['前置知识', 'prerequisites', '先决条件'])\n    if (!section) return []\n    \n    return section\n      .split(/[,，\\n]/)\n      .map(item => item.trim())\n      .filter(item => item && item.length > 1)\n  }\n\n  /**\n   * 提取标签\n   */\n  private extractTags(content: string, topic: string): string[] {\n    const section = this.extractSection(content, ['标签', 'tags', '关键词'])\n    const tags = section ? \n      section.split(/[,，\\n]/).map(item => item.trim()).filter(item => item) :\n      [topic]\n    \n    return [...new Set(tags)] // 去重\n  }\n\n  /**\n   * 提取示例\n   */\n  private extractExamples(content: string): string[] {\n    const section = this.extractSection(content, ['示例', 'examples', '案例'])\n    if (!section) return []\n    \n    return section\n      .split(/\\n\\n/)\n      .map(item => item.trim())\n      .filter(item => item && item.length > 10)\n  }\n\n  /**\n   * 提取练习\n   */\n  private extractExercises(content: string): string[] {\n    const section = this.extractSection(content, ['练习', 'exercises', '任务'])\n    if (!section) return []\n    \n    return section\n      .split(/\\n\\n/)\n      .map(item => item.trim())\n      .filter(item => item && item.length > 5)\n  }\n\n  /**\n   * 提取内容段落\n   */\n  private extractSection(content: string, keywords: string[]): string | null {\n    for (const keyword of keywords) {\n      const regex = new RegExp(`${keyword}[：:]([\\\\s\\\\S]*?)(?=\\\\n\\\\n|\\\\n[^\\\\s]|$)`, 'i')\n      const match = content.match(regex)\n      if (match) {\n        return match[1].trim()\n      }\n    }\n    return null\n  }\n}\n\n// 导出单例实例\nexport const knowledgeGenerator = new KnowledgeGeneratorService()\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAUO,MAAM;IAEX;;GAEC,GACD,MAAM,kBAAkB,OAAmC,EAA+B;QACxF,MAAM,SAAS,IAAI,CAAC,8BAA8B,CAAC;QAEnD,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS,IAAI,CAAC,eAAe;oBAC/B;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,WAAW;YACb;YAEA,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,OAAO,EAAE;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,yBACJ,SAAiB,EACjB,QAAgB,CAAC,EACc;QAC/B,MAAM,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,EAAE,MAAM;;;;;8FAK8C,CAAC;QAE3F,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS,IAAI,CAAC,eAAe;oBAC/B;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,WAAW;YACb;YAEA,OAAO,IAAI,CAAC,8BAA8B,CAAC,SAAS,OAAO;QAC7D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,gBACJ,aAAuB,EACvB,eAAuB,UAAU,EACd;QACnB,MAAM,SAAS,CAAC,OAAO,EAAE,cAAc,IAAI,CAAC,MAAM,MAAM,EAAE,aAAa;;;;;;;qBAOtD,CAAC;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,WAAW;YACb;YAEA,OAAO,IAAI,CAAC,yBAAyB,CAAC,SAAS,OAAO;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,OAAO,EAAE,CAAC,eAAe;;QAC3B;IACF;IAEA;;GAEC,GACD,AAAQ,+BAA+B,OAAmC,EAAU;QAClF,MAAM,EAAE,KAAK,EAAE,YAAY,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG;QAEhE,IAAI,SAAS,CAAC,KAAK,EAAE,MAAM;;KAE1B,EAAE,UAAU;AACjB,EAAE,UAAU,CAAC,KAAK,EAAE,SAAS,GAAG,GAAG;;;;KAI9B,EAAE,UAAU;;;;;;AAMjB,EAAE,aAAa,kBAAkB,qBAAqB,GAAG;AACzD,EAAE,aAAa,mBAAmB,iBAAiB,GAAG;AACtD,EAAE,aAAa,YAAY,SAAS,CAAC,KAAK,EAAE,YAAY,UAAU,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG;;;;;;;;;;AAUrF,EAAE,aAAa,kBAAkB,WAAW,GAAG;AAC/C,EAAE,aAAa,mBAAmB,WAAW,IAAI;QAE7C,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,kBAA0B;QAChC,OAAO,CAAC;;;;;;;;yBAQa,CAAC;IACxB;IAEA;;GAEC,GACD,AAAQ,uBACN,OAAe,EACf,OAAmC,EACf;QACpB,IAAI;YACF,gBAAgB;YAChB,IAAI,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,MAAM;gBAClD,MAAM,YAAY,QAAQ,KAAK,CAAC;gBAChC,IAAI,WAAW;oBACb,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;oBACtC,OAAO,IAAI,CAAC,wBAAwB,CAAC;gBACvC;YACF;YAEA,qBAAqB;YACrB,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS;QAClD,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,oBAAoB;YACjC,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS;QAClD;IACF;IAEA;;GAEC,GACD,AAAQ,2BACN,OAAe,EACf,OAAmC,EACf;QACpB,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;QAE1D,OAAO;YACL,OAAO,QAAQ,KAAK;YACpB,aAAa,CAAC,EAAE,EAAE,QAAQ,KAAK,CAAC,KAAK,CAAC;YACtC,SAAS;YACT,kBAAkB,IAAI,CAAC,sBAAsB,CAAC;YAC9C,gBAAgB,IAAI,CAAC,oBAAoB,CAAC;YAC1C,eAAe,IAAI,CAAC,oBAAoB,CAAC;YACzC,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,QAAQ,KAAK;YAC7C,UAAU,QAAQ,WAAW,EAAE,kBAAkB,IAAI,CAAC,eAAe,CAAC,WAAW;YACjF,WAAW,QAAQ,WAAW,EAAE,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,WAAW;QACtF;IACF;IAEA;;GAEC,GACD,AAAQ,+BAA+B,OAAe,EAAwB;QAC5E,IAAI;YACF,aAAa;YACb,MAAM,YAAY,QAAQ,KAAK,CAAC;YAChC,IAAI,WAAW;gBACb,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;gBACtC,OAAO,OAAO,GAAG,CAAC,CAAC,OAAc,IAAI,CAAC,wBAAwB,CAAC;YACjE;YAEA,mBAAmB;YACnB,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,gBAAgB;YAC7B,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,0BAA0B,OAAe,EAAY;QAC3D,OAAO,QACJ,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,QAAQ,CAAC,KAAK,UAAU,CAAC,MACxC,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,SAAS,KAC3D,KAAK,CAAC,GAAG,IAAI,YAAY;;IAC9B;IAEA;;GAEC,GACD,AAAQ,yBAAyB,GAAQ,EAAsB;QAC7D,OAAO;YACL,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI;YAChC,aAAa,IAAI,WAAW,IAAI,IAAI,IAAI,IAAI;YAC5C,SAAS,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI,WAAW,IAAI;YAC1D,kBAAkB,SAAS,IAAI,gBAAgB,IAAI,IAAI,UAAU,IAAI;YACrE,gBAAgB,SAAS,IAAI,cAAc,IAAI,IAAI,IAAI,IAAI;YAC3D,eAAe,MAAM,OAAO,CAAC,IAAI,aAAa,IAAI,IAAI,aAAa,GAAG,EAAE;YACxE,MAAM,MAAM,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE;YAC7C,UAAU,MAAM,OAAO,CAAC,IAAI,QAAQ,IAAI,IAAI,QAAQ,GAAG;YACvD,WAAW,MAAM,OAAO,CAAC,IAAI,SAAS,IAAI,IAAI,SAAS,GAAG;QAC5D;IACF;IAEA;;GAEC,GACD,AAAQ,uBAAuB,OAAe,EAAU;QACtD,MAAM,QAAQ,QAAQ,KAAK,CAAC,sBACd,QAAQ,KAAK,CAAC;QAC5B,OAAO,QAAQ,SAAS,KAAK,CAAC,EAAE,IAAI;IACtC;IAEA;;GAEC,GACD,AAAQ,qBAAqB,OAAe,EAAU;QACpD,MAAM,QAAQ,QAAQ,KAAK,CAAC,sBACd,QAAQ,KAAK,CAAC,wBACd,QAAQ,KAAK,CAAC;QAC5B,OAAO,QAAQ,SAAS,KAAK,CAAC,EAAE,IAAI;IACtC;IAEA;;GAEC,GACD,AAAQ,qBAAqB,OAAe,EAAY;QACtD,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,SAAS;YAAC;YAAQ;YAAiB;SAAO;QAC9E,IAAI,CAAC,SAAS,OAAO,EAAE;QAEvB,OAAO,QACJ,KAAK,CAAC,UACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,QAAQ,KAAK,MAAM,GAAG;IAC1C;IAEA;;GAEC,GACD,AAAQ,YAAY,OAAe,EAAE,KAAa,EAAY;QAC5D,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,SAAS;YAAC;YAAM;YAAQ;SAAM;QAClE,MAAM,OAAO,UACX,QAAQ,KAAK,CAAC,UAAU,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,QAChE;YAAC;SAAM;QAET,OAAO;eAAI,IAAI,IAAI;SAAM,CAAC,KAAK;;IACjC;IAEA;;GAEC,GACD,AAAQ,gBAAgB,OAAe,EAAY;QACjD,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,SAAS;YAAC;YAAM;YAAY;SAAK;QACrE,IAAI,CAAC,SAAS,OAAO,EAAE;QAEvB,OAAO,QACJ,KAAK,CAAC,QACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,QAAQ,KAAK,MAAM,GAAG;IAC1C;IAEA;;GAEC,GACD,AAAQ,iBAAiB,OAAe,EAAY;QAClD,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,SAAS;YAAC;YAAM;YAAa;SAAK;QACtE,IAAI,CAAC,SAAS,OAAO,EAAE;QAEvB,OAAO,QACJ,KAAK,CAAC,QACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,QAAQ,KAAK,MAAM,GAAG;IAC1C;IAEA;;GAEC,GACD,AAAQ,eAAe,OAAe,EAAE,QAAkB,EAAiB;QACzE,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,QAAQ,IAAI,OAAO,GAAG,QAAQ,sCAAsC,CAAC,EAAE;YAC7E,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,OAAO;gBACT,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;YACtB;QACF;QACA,OAAO;IACT;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/llm/dependencyAnalyzer.ts"], "sourcesContent": ["/**\n * 依赖知识分析服务\n * 当用户遇到不懂的概念时，分析并识别需要的前置知识\n */\n\nimport { llmService } from './service'\nimport { \n  DependencyAnalysisRequest,\n  DependencyAnalysisResponse,\n  AnalyzedDependency,\n  LLMMessage \n} from './types'\n\n/**\n * 依赖分析服务类\n */\nexport class DependencyAnalyzerService {\n  \n  /**\n   * 分析知识依赖\n   */\n  async analyzeDependencies(request: DependencyAnalysisRequest): Promise<DependencyAnalysisResponse> {\n    const prompt = this.buildDependencyAnalysisPrompt(request)\n    \n    try {\n      const response = await llmService.chat({\n        messages: [\n          {\n            role: 'system',\n            content: this.getSystemPrompt()\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.3, // 较低的温度确保分析的准确性\n        maxTokens: 3000\n      })\n\n      return this.parseDependencyResponse(response.content, request)\n    } catch (error) {\n      console.error('依赖分析失败:', error)\n      throw new Error('无法分析知识依赖，请稍后重试')\n    }\n  }\n\n  /**\n   * 快速检查概念依赖\n   */\n  async quickDependencyCheck(\n    currentTopic: string, \n    unknownConcept: string\n  ): Promise<AnalyzedDependency[]> {\n    const prompt = `用户正在学习\"${currentTopic}\"，但不理解\"${unknownConcept}\"这个概念。\n\n请分析学习\"${unknownConcept}\"需要的前置知识，要求：\n1. 列出3-5个最重要的前置概念\n2. 按重要性和学习顺序排序\n3. 估算每个概念的学习时间和难度\n4. 提供简短的概念描述\n\n请以JSON数组格式返回，每个依赖包含：concept, importance(1-10), difficulty(1-10), description, estimatedTime(分钟)`\n\n    try {\n      const response = await llmService.chat({\n        messages: [\n          {\n            role: 'system',\n            content: '你是一个专业的学习顾问，擅长分析知识结构和学习依赖关系。'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.2,\n        maxTokens: 2000\n      })\n\n      return this.parseQuickDependencyResponse(response.content)\n    } catch (error) {\n      console.error('快速依赖检查失败:', error)\n      return []\n    }\n  }\n\n  /**\n   * 评估学习准备度\n   */\n  async assessLearningReadiness(\n    targetTopic: string,\n    currentKnowledge: string[],\n    userLevel: string = 'beginner'\n  ): Promise<{\n    readiness: number // 0-100\n    missingConcepts: string[]\n    recommendations: string[]\n    estimatedPrepTime: number\n  }> {\n    const prompt = `评估用户学习\"${targetTopic}\"的准备度：\n\n用户当前掌握的知识：\n${currentKnowledge.map(k => `- ${k}`).join('\\n')}\n\n用户水平：${userLevel}\n\n请分析：\n1. 学习准备度（0-100分）\n2. 缺失的关键概念\n3. 学习建议\n4. 预计准备时间（分钟）\n\n请以JSON格式返回：{readiness, missingConcepts, recommendations, estimatedPrepTime}`\n\n    try {\n      const response = await llmService.chat({\n        messages: [\n          {\n            role: 'system',\n            content: '你是一个专业的学习评估专家，能够准确评估学习者的知识准备度。'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.2,\n        maxTokens: 1500\n      })\n\n      return this.parseReadinessResponse(response.content)\n    } catch (error) {\n      console.error('学习准备度评估失败:', error)\n      return {\n        readiness: 50,\n        missingConcepts: [],\n        recommendations: ['建议先掌握基础概念'],\n        estimatedPrepTime: 60\n      }\n    }\n  }\n\n  /**\n   * 构建依赖分析提示词\n   */\n  private buildDependencyAnalysisPrompt(request: DependencyAnalysisRequest): string {\n    const { currentTopic, unknownConcepts, userContext, currentLevel } = request\n\n    let prompt = `用户正在学习\"${currentTopic}\"，但遇到了以下不理解的概念：\n${unknownConcepts.map(concept => `- ${concept}`).join('\\n')}\n\n${userContext ? `用户背景：${userContext}` : ''}\n${currentLevel ? `当前水平：${currentLevel}` : ''}\n\n请进行详细的依赖分析：\n\n1. 对每个不理解的概念，分析其前置知识要求\n2. 识别概念之间的依赖关系\n3. 按学习优先级排序\n4. 估算学习时间和难度\n5. 提供学习建议和资源\n\n要求：\n- 分析要准确、实用\n- 考虑概念的重要性和学习顺序\n- 提供具体的学习路径\n- 估算合理的时间投入\n\n请以结构化格式返回分析结果。`\n\n    return prompt\n  }\n\n  /**\n   * 获取系统提示词\n   */\n  private getSystemPrompt(): string {\n    return `你是一个专业的知识结构分析专家，具有以下能力：\n\n1. 深度理解各学科的知识体系和依赖关系\n2. 准确识别学习某个概念所需的前置知识\n3. 合理评估学习难度和时间投入\n4. 提供实用的学习路径和建议\n\n你的分析应该：\n- 基于教育学和认知科学原理\n- 考虑学习者的认知负荷\n- 提供循序渐进的学习路径\n- 注重实际应用和理解深度\n\n请始终以学习者的最佳利益为出发点，提供准确、实用的分析结果。`\n  }\n\n  /**\n   * 解析依赖分析响应\n   */\n  private parseDependencyResponse(\n    content: string, \n    request: DependencyAnalysisRequest\n  ): DependencyAnalysisResponse {\n    try {\n      // 尝试解析JSON格式\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/)\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0])\n        return this.normalizeDependencyResponse(parsed)\n      }\n\n      // 解析文本格式\n      return this.parseTextDependencyResponse(content, request)\n    } catch (error) {\n      console.warn('解析依赖分析响应失败，使用文本解析:', error)\n      return this.parseTextDependencyResponse(content, request)\n    }\n  }\n\n  /**\n   * 解析文本格式的依赖分析响应\n   */\n  private parseTextDependencyResponse(\n    content: string, \n    request: DependencyAnalysisRequest\n  ): DependencyAnalysisResponse {\n    const dependencies: AnalyzedDependency[] = []\n    \n    // 为每个未知概念创建基础依赖项\n    request.unknownConcepts.forEach((concept, index) => {\n      dependencies.push({\n        concept,\n        importance: 8 - index, // 按顺序递减重要性\n        difficulty: 5,\n        description: `学习${concept}的相关知识`,\n        estimatedTime: 30,\n        learningPath: [`理解${concept}的基本概念`, `掌握${concept}的应用`],\n        resources: [`${concept}相关教程`, `${concept}实践案例`]\n      })\n    })\n\n    return {\n      dependencies,\n      recommendedOrder: request.unknownConcepts,\n      totalEstimatedTime: dependencies.reduce((sum, dep) => sum + (dep.estimatedTime || 0), 0),\n      explanation: '基于文本分析生成的学习建议，建议按照概念的基础程度逐步学习。'\n    }\n  }\n\n  /**\n   * 解析快速依赖检查响应\n   */\n  private parseQuickDependencyResponse(content: string): AnalyzedDependency[] {\n    try {\n      const jsonMatch = content.match(/\\[[\\s\\S]*\\]/)\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0])\n        return parsed.map((item: any) => this.normalizeAnalyzedDependency(item))\n      }\n    } catch (error) {\n      console.warn('解析快速依赖检查响应失败:', error)\n    }\n\n    return []\n  }\n\n  /**\n   * 解析学习准备度响应\n   */\n  private parseReadinessResponse(content: string): {\n    readiness: number\n    missingConcepts: string[]\n    recommendations: string[]\n    estimatedPrepTime: number\n  } {\n    try {\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/)\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0])\n        return {\n          readiness: parseInt(parsed.readiness) || 50,\n          missingConcepts: Array.isArray(parsed.missingConcepts) ? parsed.missingConcepts : [],\n          recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations : [],\n          estimatedPrepTime: parseInt(parsed.estimatedPrepTime) || 60\n        }\n      }\n    } catch (error) {\n      console.warn('解析学习准备度响应失败:', error)\n    }\n\n    return {\n      readiness: 50,\n      missingConcepts: [],\n      recommendations: ['建议先掌握基础概念'],\n      estimatedPrepTime: 60\n    }\n  }\n\n  /**\n   * 标准化依赖分析响应\n   */\n  private normalizeDependencyResponse(obj: any): DependencyAnalysisResponse {\n    return {\n      dependencies: Array.isArray(obj.dependencies) ? \n        obj.dependencies.map((dep: any) => this.normalizeAnalyzedDependency(dep)) : [],\n      recommendedOrder: Array.isArray(obj.recommendedOrder) ? obj.recommendedOrder : [],\n      totalEstimatedTime: parseInt(obj.totalEstimatedTime) || 0,\n      explanation: obj.explanation || '依赖分析完成'\n    }\n  }\n\n  /**\n   * 标准化分析依赖对象\n   */\n  private normalizeAnalyzedDependency(obj: any): AnalyzedDependency {\n    return {\n      concept: obj.concept || obj.name || '未知概念',\n      importance: parseInt(obj.importance) || 5,\n      difficulty: parseInt(obj.difficulty) || 5,\n      description: obj.description || obj.desc || '',\n      estimatedTime: parseInt(obj.estimatedTime || obj.time) || 30,\n      learningPath: Array.isArray(obj.learningPath) ? obj.learningPath : [],\n      resources: Array.isArray(obj.resources) ? obj.resources : []\n    }\n  }\n}\n\n// 导出单例实例\nexport const dependencyAnalyzer = new DependencyAnalyzerService()\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAWO,MAAM;IAEX;;GAEC,GACD,MAAM,oBAAoB,OAAkC,EAAuC;QACjG,MAAM,SAAS,IAAI,CAAC,6BAA6B,CAAC;QAElD,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS,IAAI,CAAC,eAAe;oBAC/B;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,WAAW;YACb;YAEA,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,OAAO,EAAE;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,qBACJ,YAAoB,EACpB,cAAsB,EACS;QAC/B,MAAM,SAAS,CAAC,OAAO,EAAE,aAAa,OAAO,EAAE,eAAe;;MAE5D,EAAE,eAAe;;;;;;+FAMwE,CAAC;QAE5F,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,WAAW;YACb;YAEA,OAAO,IAAI,CAAC,4BAA4B,CAAC,SAAS,OAAO;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,wBACJ,WAAmB,EACnB,gBAA0B,EAC1B,YAAoB,UAAU,EAM7B;QACD,MAAM,SAAS,CAAC,OAAO,EAAE,YAAY;;;AAGzC,EAAE,iBAAiB,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM;;KAE5C,EAAE,UAAU;;;;;;;;2EAQ0D,CAAC;QAExE,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,WAAW;YACb;YAEA,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,OAAO;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,OAAO;gBACL,WAAW;gBACX,iBAAiB,EAAE;gBACnB,iBAAiB;oBAAC;iBAAY;gBAC9B,mBAAmB;YACrB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,8BAA8B,OAAkC,EAAU;QAChF,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG;QAErE,IAAI,SAAS,CAAC,OAAO,EAAE,aAAa;AACxC,EAAE,gBAAgB,GAAG,CAAC,CAAA,UAAW,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM;;AAE5D,EAAE,cAAc,CAAC,KAAK,EAAE,aAAa,GAAG,GAAG;AAC3C,EAAE,eAAe,CAAC,KAAK,EAAE,cAAc,GAAG,GAAG;;;;;;;;;;;;;;;;cAgB/B,CAAC;QAEX,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,kBAA0B;QAChC,OAAO,CAAC;;;;;;;;;;;;;8BAakB,CAAC;IAC7B;IAEA;;GAEC,GACD,AAAQ,wBACN,OAAe,EACf,OAAkC,EACN;QAC5B,IAAI;YACF,aAAa;YACb,MAAM,YAAY,QAAQ,KAAK,CAAC;YAChC,IAAI,WAAW;gBACb,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;gBACtC,OAAO,IAAI,CAAC,2BAA2B,CAAC;YAC1C;YAEA,SAAS;YACT,OAAO,IAAI,CAAC,2BAA2B,CAAC,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,sBAAsB;YACnC,OAAO,IAAI,CAAC,2BAA2B,CAAC,SAAS;QACnD;IACF;IAEA;;GAEC,GACD,AAAQ,4BACN,OAAe,EACf,OAAkC,EACN;QAC5B,MAAM,eAAqC,EAAE;QAE7C,iBAAiB;QACjB,QAAQ,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS;YACxC,aAAa,IAAI,CAAC;gBAChB;gBACA,YAAY,IAAI;gBAChB,YAAY;gBACZ,aAAa,CAAC,EAAE,EAAE,QAAQ,KAAK,CAAC;gBAChC,eAAe;gBACf,cAAc;oBAAC,CAAC,EAAE,EAAE,QAAQ,KAAK,CAAC;oBAAE,CAAC,EAAE,EAAE,QAAQ,GAAG,CAAC;iBAAC;gBACtD,WAAW;oBAAC,GAAG,QAAQ,IAAI,CAAC;oBAAE,GAAG,QAAQ,IAAI,CAAC;iBAAC;YACjD;QACF;QAEA,OAAO;YACL;YACA,kBAAkB,QAAQ,eAAe;YACzC,oBAAoB,aAAa,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,IAAI,aAAa,IAAI,CAAC,GAAG;YACtF,aAAa;QACf;IACF;IAEA;;GAEC,GACD,AAAQ,6BAA6B,OAAe,EAAwB;QAC1E,IAAI;YACF,MAAM,YAAY,QAAQ,KAAK,CAAC;YAChC,IAAI,WAAW;gBACb,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;gBACtC,OAAO,OAAO,GAAG,CAAC,CAAC,OAAc,IAAI,CAAC,2BAA2B,CAAC;YACpE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,iBAAiB;QAChC;QAEA,OAAO,EAAE;IACX;IAEA;;GAEC,GACD,AAAQ,uBAAuB,OAAe,EAK5C;QACA,IAAI;YACF,MAAM,YAAY,QAAQ,KAAK,CAAC;YAChC,IAAI,WAAW;gBACb,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;gBACtC,OAAO;oBACL,WAAW,SAAS,OAAO,SAAS,KAAK;oBACzC,iBAAiB,MAAM,OAAO,CAAC,OAAO,eAAe,IAAI,OAAO,eAAe,GAAG,EAAE;oBACpF,iBAAiB,MAAM,OAAO,CAAC,OAAO,eAAe,IAAI,OAAO,eAAe,GAAG,EAAE;oBACpF,mBAAmB,SAAS,OAAO,iBAAiB,KAAK;gBAC3D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,gBAAgB;QAC/B;QAEA,OAAO;YACL,WAAW;YACX,iBAAiB,EAAE;YACnB,iBAAiB;gBAAC;aAAY;YAC9B,mBAAmB;QACrB;IACF;IAEA;;GAEC,GACD,AAAQ,4BAA4B,GAAQ,EAA8B;QACxE,OAAO;YACL,cAAc,MAAM,OAAO,CAAC,IAAI,YAAY,IAC1C,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,MAAa,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE;YAChF,kBAAkB,MAAM,OAAO,CAAC,IAAI,gBAAgB,IAAI,IAAI,gBAAgB,GAAG,EAAE;YACjF,oBAAoB,SAAS,IAAI,kBAAkB,KAAK;YACxD,aAAa,IAAI,WAAW,IAAI;QAClC;IACF;IAEA;;GAEC,GACD,AAAQ,4BAA4B,GAAQ,EAAsB;QAChE,OAAO;YACL,SAAS,IAAI,OAAO,IAAI,IAAI,IAAI,IAAI;YACpC,YAAY,SAAS,IAAI,UAAU,KAAK;YACxC,YAAY,SAAS,IAAI,UAAU,KAAK;YACxC,aAAa,IAAI,WAAW,IAAI,IAAI,IAAI,IAAI;YAC5C,eAAe,SAAS,IAAI,aAAa,IAAI,IAAI,IAAI,KAAK;YAC1D,cAAc,MAAM,OAAO,CAAC,IAAI,YAAY,IAAI,IAAI,YAAY,GAAG,EAAE;YACrE,WAAW,MAAM,OAAO,CAAC,IAAI,SAAS,IAAI,IAAI,SAAS,GAAG,EAAE;QAC9D;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/llm/pathPlanner.ts"], "sourcesContent": ["/**\n * 学习路径规划服务\n * 基于知识依赖分析，智能规划最优的学习路径和顺序\n */\n\nimport { llmService } from './service'\nimport { dependencyAnalyzer } from './dependencyAnalyzer'\nimport { \n  LearningPathRequest,\n  LearningPath,\n  LearningStep,\n  DependencyAnalysisRequest\n} from './types'\n\n/**\n * 学习路径规划服务类\n */\nexport class PathPlannerService {\n  \n  /**\n   * 生成完整的学习路径\n   */\n  async generateLearningPath(request: LearningPathRequest): Promise<LearningPath> {\n    try {\n      // 1. 分析知识依赖\n      const dependencyAnalysis = await this.analyzeLearningDependencies(request)\n      \n      // 2. 生成学习步骤\n      const steps = await this.generateLearningSteps(request, dependencyAnalysis)\n      \n      // 3. 优化学习路径\n      const optimizedSteps = this.optimizeLearningPath(steps, request)\n      \n      // 4. 生成里程碑\n      const milestones = this.generateMilestones(optimizedSteps)\n      \n      return {\n        id: this.generatePathId(),\n        title: `${request.targetTopic} 学习路径`,\n        description: this.generatePathDescription(request),\n        totalTime: optimizedSteps.reduce((sum, step) => sum + step.estimatedTime, 0),\n        difficulty: this.calculateOverallDifficulty(optimizedSteps),\n        steps: optimizedSteps,\n        milestones\n      }\n    } catch (error) {\n      console.error('学习路径生成失败:', error)\n      throw new Error('无法生成学习路径，请稍后重试')\n    }\n  }\n\n  /**\n   * 生成自适应学习路径\n   * 根据用户的学习进度动态调整\n   */\n  async generateAdaptivePath(\n    targetTopic: string,\n    currentProgress: { [concept: string]: number }, // 概念 -> 掌握程度(0-100)\n    learningHistory: string[], // 已学习的概念\n    timeConstraints?: { totalTime?: number; sessionLength?: number }\n  ): Promise<LearningPath> {\n    const prompt = this.buildAdaptivePathPrompt(\n      targetTopic, \n      currentProgress, \n      learningHistory, \n      timeConstraints\n    )\n    \n    try {\n      const response = await llmService.chat({\n        messages: [\n          {\n            role: 'system',\n            content: this.getAdaptiveSystemPrompt()\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.4,\n        maxTokens: 4000\n      })\n\n      return this.parseAdaptivePathResponse(response.content, targetTopic)\n    } catch (error) {\n      console.error('自适应路径生成失败:', error)\n      throw new Error('无法生成自适应学习路径')\n    }\n  }\n\n  /**\n   * 优化现有学习路径\n   */\n  async optimizeExistingPath(\n    currentPath: LearningPath,\n    userFeedback: {\n      completedSteps: string[]\n      strugglingConcepts: string[]\n      timeSpent: { [stepId: string]: number }\n      preferences: any\n    }\n  ): Promise<LearningPath> {\n    const prompt = `基于用户反馈优化学习路径：\n\n当前路径：${currentPath.title}\n已完成步骤：${userFeedback.completedSteps.join(', ')}\n困难概念：${userFeedback.strugglingConcepts.join(', ')}\n\n请优化路径，调整：\n1. 困难概念的学习方法\n2. 步骤的顺序和时间分配\n3. 添加必要的复习和练习\n4. 提供替代学习方案\n\n请以JSON格式返回优化后的学习路径。`\n\n    try {\n      const response = await llmService.chat({\n        messages: [\n          {\n            role: 'system',\n            content: '你是一个专业的学习路径优化专家，能够根据用户反馈调整学习计划。'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.3,\n        maxTokens: 3000\n      })\n\n      return this.parseOptimizedPathResponse(response.content, currentPath)\n    } catch (error) {\n      console.error('路径优化失败:', error)\n      return currentPath // 返回原路径\n    }\n  }\n\n  /**\n   * 分析学习依赖关系\n   */\n  private async analyzeLearningDependencies(request: LearningPathRequest) {\n    const dependencyRequest: DependencyAnalysisRequest = {\n      currentTopic: request.targetTopic,\n      unknownConcepts: request.missingKnowledge,\n      userContext: `当前掌握：${request.currentKnowledge.join(', ')}`,\n      currentLevel: this.inferUserLevel(request)\n    }\n\n    return await dependencyAnalyzer.analyzeDependencies(dependencyRequest)\n  }\n\n  /**\n   * 生成学习步骤\n   */\n  private async generateLearningSteps(\n    request: LearningPathRequest,\n    dependencyAnalysis: any\n  ): Promise<LearningStep[]> {\n    const prompt = `基于依赖分析结果，为\"${request.targetTopic}\"生成详细的学习步骤：\n\n目标：${request.targetTopic}\n当前知识：${request.currentKnowledge.join(', ')}\n缺失知识：${request.missingKnowledge.join(', ')}\n依赖分析：${JSON.stringify(dependencyAnalysis, null, 2)}\n\n要求：\n1. 创建循序渐进的学习步骤\n2. 每个步骤包含明确的学习目标\n3. 估算合理的学习时间\n4. 标注步骤类型（概念/练习/项目/复习）\n5. 定义前置要求\n\n请以JSON数组格式返回学习步骤。`\n\n    try {\n      const response = await llmService.chat({\n        messages: [\n          {\n            role: 'system',\n            content: '你是一个专业的课程设计专家，擅长创建结构化的学习步骤。'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.5,\n        maxTokens: 3000\n      })\n\n      return this.parseLearningStepsResponse(response.content)\n    } catch (error) {\n      console.error('学习步骤生成失败:', error)\n      return this.generateDefaultSteps(request)\n    }\n  }\n\n  /**\n   * 优化学习路径\n   */\n  private optimizeLearningPath(\n    steps: LearningStep[], \n    request: LearningPathRequest\n  ): LearningStep[] {\n    // 根据时间约束调整\n    if (request.timeConstraints?.totalTime) {\n      steps = this.adjustForTimeConstraints(steps, request.timeConstraints.totalTime)\n    }\n\n    // 根据学习偏好调整\n    if (request.preferences?.difficulty === 'gradual') {\n      steps = this.sortByDifficultyGradual(steps)\n    }\n\n    // 添加项目实践\n    if (request.preferences?.includeProjects) {\n      steps = this.addProjectSteps(steps)\n    }\n\n    return steps\n  }\n\n  /**\n   * 生成学习里程碑\n   */\n  private generateMilestones(steps: LearningStep[]): string[] {\n    const milestones: string[] = []\n    const totalSteps = steps.length\n    \n    // 每25%进度设置一个里程碑\n    for (let i = 1; i <= 4; i++) {\n      const milestoneIndex = Math.floor((totalSteps * i) / 4) - 1\n      if (milestoneIndex >= 0 && milestoneIndex < totalSteps) {\n        milestones.push(`完成${steps[milestoneIndex].title}`)\n      }\n    }\n\n    return milestones\n  }\n\n  /**\n   * 构建自适应路径提示词\n   */\n  private buildAdaptivePathPrompt(\n    targetTopic: string,\n    currentProgress: { [concept: string]: number },\n    learningHistory: string[],\n    timeConstraints?: any\n  ): string {\n    return `为用户生成自适应学习路径：\n\n目标主题：${targetTopic}\n\n当前进度：\n${Object.entries(currentProgress).map(([concept, progress]) => \n  `- ${concept}: ${progress}%`\n).join('\\n')}\n\n学习历史：${learningHistory.join(', ')}\n\n${timeConstraints ? `时间约束：${JSON.stringify(timeConstraints)}` : ''}\n\n请生成一个自适应的学习路径，要求：\n1. 基于当前进度调整学习重点\n2. 避免重复已掌握的内容\n3. 强化薄弱环节\n4. 提供个性化的学习建议\n\n请以JSON格式返回完整的学习路径。`\n  }\n\n  /**\n   * 获取自适应系统提示词\n   */\n  private getAdaptiveSystemPrompt(): string {\n    return `你是一个智能学习路径规划专家，具有以下能力：\n\n1. 分析学习者的当前状态和进度\n2. 识别知识盲点和薄弱环节\n3. 设计个性化的学习路径\n4. 动态调整学习策略和重点\n5. 优化学习效率和效果\n\n你的规划应该：\n- 基于学习科学和认知心理学原理\n- 考虑个体差异和学习偏好\n- 提供可操作的具体步骤\n- 包含适当的复习和巩固机制\n- 设置合理的学习目标和里程碑`\n  }\n\n  /**\n   * 解析自适应路径响应\n   */\n  private parseAdaptivePathResponse(content: string, targetTopic: string): LearningPath {\n    try {\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/)\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0])\n        return this.normalizeLearningPath(parsed, targetTopic)\n      }\n    } catch (error) {\n      console.warn('解析自适应路径响应失败:', error)\n    }\n\n    // 返回默认路径\n    return this.generateDefaultPath(targetTopic)\n  }\n\n  /**\n   * 解析优化路径响应\n   */\n  private parseOptimizedPathResponse(content: string, originalPath: LearningPath): LearningPath {\n    try {\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/)\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0])\n        return this.normalizeLearningPath(parsed, originalPath.title)\n      }\n    } catch (error) {\n      console.warn('解析优化路径响应失败:', error)\n    }\n\n    return originalPath\n  }\n\n  /**\n   * 解析学习步骤响应\n   */\n  private parseLearningStepsResponse(content: string): LearningStep[] {\n    try {\n      const jsonMatch = content.match(/\\[[\\s\\S]*\\]/)\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0])\n        return parsed.map((step: any) => this.normalizeLearningStep(step))\n      }\n    } catch (error) {\n      console.warn('解析学习步骤响应失败:', error)\n    }\n\n    return []\n  }\n\n  /**\n   * 标准化学习路径对象\n   */\n  private normalizeLearningPath(obj: any, title: string): LearningPath {\n    return {\n      id: obj.id || this.generatePathId(),\n      title: obj.title || title,\n      description: obj.description || '',\n      totalTime: parseInt(obj.totalTime) || 0,\n      difficulty: parseInt(obj.difficulty) || 5,\n      steps: Array.isArray(obj.steps) ? \n        obj.steps.map((step: any) => this.normalizeLearningStep(step)) : [],\n      milestones: Array.isArray(obj.milestones) ? obj.milestones : []\n    }\n  }\n\n  /**\n   * 标准化学习步骤对象\n   */\n  private normalizeLearningStep(obj: any): LearningStep {\n    return {\n      id: obj.id || this.generateStepId(),\n      title: obj.title || obj.name || '学习步骤',\n      description: obj.description || obj.desc || '',\n      type: obj.type || 'concept',\n      estimatedTime: parseInt(obj.estimatedTime || obj.time) || 30,\n      difficulty: parseInt(obj.difficulty) || 5,\n      prerequisites: Array.isArray(obj.prerequisites) ? obj.prerequisites : [],\n      resources: Array.isArray(obj.resources) ? obj.resources : [],\n      objectives: Array.isArray(obj.objectives) ? obj.objectives : []\n    }\n  }\n\n  /**\n   * 生成默认学习步骤\n   */\n  private generateDefaultSteps(request: LearningPathRequest): LearningStep[] {\n    return [\n      {\n        id: this.generateStepId(),\n        title: `${request.targetTopic} 基础概念`,\n        description: `学习${request.targetTopic}的基本概念和原理`,\n        type: 'concept',\n        estimatedTime: 60,\n        difficulty: 3,\n        prerequisites: [],\n        objectives: [`理解${request.targetTopic}的基本概念`]\n      },\n      {\n        id: this.generateStepId(),\n        title: `${request.targetTopic} 实践练习`,\n        description: `通过练习巩固${request.targetTopic}的知识`,\n        type: 'practice',\n        estimatedTime: 90,\n        difficulty: 5,\n        prerequisites: [`${request.targetTopic} 基础概念`],\n        objectives: [`掌握${request.targetTopic}的实际应用`]\n      }\n    ]\n  }\n\n  /**\n   * 生成默认学习路径\n   */\n  private generateDefaultPath(targetTopic: string): LearningPath {\n    const steps = [\n      {\n        id: this.generateStepId(),\n        title: `${targetTopic} 入门`,\n        description: `${targetTopic}的基础学习`,\n        type: 'concept' as const,\n        estimatedTime: 60,\n        difficulty: 3,\n        prerequisites: [],\n        objectives: [`了解${targetTopic}基础`]\n      }\n    ]\n\n    return {\n      id: this.generatePathId(),\n      title: `${targetTopic} 学习路径`,\n      description: `系统学习${targetTopic}的完整路径`,\n      totalTime: 60,\n      difficulty: 3,\n      steps,\n      milestones: [`完成${targetTopic}入门`]\n    }\n  }\n\n  /**\n   * 辅助方法\n   */\n  private generatePathId(): string {\n    return `path_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  }\n\n  private generateStepId(): string {\n    return `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  }\n\n  private generatePathDescription(request: LearningPathRequest): string {\n    return `为学习${request.targetTopic}量身定制的学习路径，基于您当前的知识水平和学习目标。`\n  }\n\n  private calculateOverallDifficulty(steps: LearningStep[]): number {\n    if (steps.length === 0) return 5\n    const avgDifficulty = steps.reduce((sum, step) => sum + step.difficulty, 0) / steps.length\n    return Math.round(avgDifficulty)\n  }\n\n  private inferUserLevel(request: LearningPathRequest): string {\n    const knowledgeCount = request.currentKnowledge.length\n    if (knowledgeCount === 0) return 'beginner'\n    if (knowledgeCount < 5) return 'beginner'\n    if (knowledgeCount < 15) return 'intermediate'\n    return 'advanced'\n  }\n\n  private adjustForTimeConstraints(steps: LearningStep[], totalTime: number): LearningStep[] {\n    const currentTotal = steps.reduce((sum, step) => sum + step.estimatedTime, 0)\n    if (currentTotal <= totalTime) return steps\n\n    const ratio = totalTime / currentTotal\n    return steps.map(step => ({\n      ...step,\n      estimatedTime: Math.round(step.estimatedTime * ratio)\n    }))\n  }\n\n  private sortByDifficultyGradual(steps: LearningStep[]): LearningStep[] {\n    return [...steps].sort((a, b) => a.difficulty - b.difficulty)\n  }\n\n  private addProjectSteps(steps: LearningStep[]): LearningStep[] {\n    // 在适当位置插入项目步骤\n    const projectStep: LearningStep = {\n      id: this.generateStepId(),\n      title: '综合项目实践',\n      description: '通过实际项目巩固所学知识',\n      type: 'project',\n      estimatedTime: 120,\n      difficulty: 7,\n      prerequisites: steps.slice(0, Math.floor(steps.length * 0.7)).map(s => s.title),\n      objectives: ['应用所学知识解决实际问题']\n    }\n\n    const insertIndex = Math.floor(steps.length * 0.8)\n    const result = [...steps]\n    result.splice(insertIndex, 0, projectStep)\n    return result\n  }\n}\n\n// 导出单例实例\nexport const pathPlanner = new PathPlannerService()\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;;;AAWO,MAAM;IAEX;;GAEC,GACD,MAAM,qBAAqB,OAA4B,EAAyB;QAC9E,IAAI;YACF,YAAY;YACZ,MAAM,qBAAqB,MAAM,IAAI,CAAC,2BAA2B,CAAC;YAElE,YAAY;YACZ,MAAM,QAAQ,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS;YAExD,YAAY;YACZ,MAAM,iBAAiB,IAAI,CAAC,oBAAoB,CAAC,OAAO;YAExD,WAAW;YACX,MAAM,aAAa,IAAI,CAAC,kBAAkB,CAAC;YAE3C,OAAO;gBACL,IAAI,IAAI,CAAC,cAAc;gBACvB,OAAO,GAAG,QAAQ,WAAW,CAAC,KAAK,CAAC;gBACpC,aAAa,IAAI,CAAC,uBAAuB,CAAC;gBAC1C,WAAW,eAAe,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,aAAa,EAAE;gBAC1E,YAAY,IAAI,CAAC,0BAA0B,CAAC;gBAC5C,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;GAGC,GACD,MAAM,qBACJ,WAAmB,EACnB,eAA8C,EAC9C,eAAyB,EACzB,eAAgE,EACzC;QACvB,MAAM,SAAS,IAAI,CAAC,uBAAuB,CACzC,aACA,iBACA,iBACA;QAGF,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS,IAAI,CAAC,uBAAuB;oBACvC;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,WAAW;YACb;YAEA,OAAO,IAAI,CAAC,yBAAyB,CAAC,SAAS,OAAO,EAAE;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,qBACJ,WAAyB,EACzB,YAKC,EACsB;QACvB,MAAM,SAAS,CAAC;;KAEf,EAAE,YAAY,KAAK,CAAC;MACnB,EAAE,aAAa,cAAc,CAAC,IAAI,CAAC,MAAM;KAC1C,EAAE,aAAa,kBAAkB,CAAC,IAAI,CAAC,MAAM;;;;;;;;mBAQ/B,CAAC;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,WAAW;YACb;YAEA,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,OAAO,EAAE;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,OAAO,YAAY,QAAQ;;QAC7B;IACF;IAEA;;GAEC,GACD,MAAc,4BAA4B,OAA4B,EAAE;QACtE,MAAM,oBAA+C;YACnD,cAAc,QAAQ,WAAW;YACjC,iBAAiB,QAAQ,gBAAgB;YACzC,aAAa,CAAC,KAAK,EAAE,QAAQ,gBAAgB,CAAC,IAAI,CAAC,OAAO;YAC1D,cAAc,IAAI,CAAC,cAAc,CAAC;QACpC;QAEA,OAAO,MAAM,yIAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC;IACtD;IAEA;;GAEC,GACD,MAAc,sBACZ,OAA4B,EAC5B,kBAAuB,EACE;QACzB,MAAM,SAAS,CAAC,WAAW,EAAE,QAAQ,WAAW,CAAC;;GAElD,EAAE,QAAQ,WAAW,CAAC;KACpB,EAAE,QAAQ,gBAAgB,CAAC,IAAI,CAAC,MAAM;KACtC,EAAE,QAAQ,gBAAgB,CAAC,IAAI,CAAC,MAAM;KACtC,EAAE,KAAK,SAAS,CAAC,oBAAoB,MAAM,GAAG;;;;;;;;;iBASlC,CAAC;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,WAAW;YACb;YAEA,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,OAAO;QACzD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC;IACF;IAEA;;GAEC,GACD,AAAQ,qBACN,KAAqB,EACrB,OAA4B,EACZ;QAChB,WAAW;QACX,IAAI,QAAQ,eAAe,EAAE,WAAW;YACtC,QAAQ,IAAI,CAAC,wBAAwB,CAAC,OAAO,QAAQ,eAAe,CAAC,SAAS;QAChF;QAEA,WAAW;QACX,IAAI,QAAQ,WAAW,EAAE,eAAe,WAAW;YACjD,QAAQ,IAAI,CAAC,uBAAuB,CAAC;QACvC;QAEA,SAAS;QACT,IAAI,QAAQ,WAAW,EAAE,iBAAiB;YACxC,QAAQ,IAAI,CAAC,eAAe,CAAC;QAC/B;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAqB,EAAY;QAC1D,MAAM,aAAuB,EAAE;QAC/B,MAAM,aAAa,MAAM,MAAM;QAE/B,gBAAgB;QAChB,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YAC3B,MAAM,iBAAiB,KAAK,KAAK,CAAC,AAAC,aAAa,IAAK,KAAK;YAC1D,IAAI,kBAAkB,KAAK,iBAAiB,YAAY;gBACtD,WAAW,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE;YACpD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,wBACN,WAAmB,EACnB,eAA8C,EAC9C,eAAyB,EACzB,eAAqB,EACb;QACR,OAAO,CAAC;;KAEP,EAAE,YAAY;;;AAGnB,EAAE,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,SAAS,SAAS,GACxD,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,CAAC,EAC5B,IAAI,CAAC,MAAM;;KAER,EAAE,gBAAgB,IAAI,CAAC,MAAM;;AAElC,EAAE,kBAAkB,CAAC,KAAK,EAAE,KAAK,SAAS,CAAC,kBAAkB,GAAG,GAAG;;;;;;;;kBAQjD,CAAC;IACjB;IAEA;;GAEC,GACD,AAAQ,0BAAkC;QACxC,OAAO,CAAC;;;;;;;;;;;;;eAaG,CAAC;IACd;IAEA;;GAEC,GACD,AAAQ,0BAA0B,OAAe,EAAE,WAAmB,EAAgB;QACpF,IAAI;YACF,MAAM,YAAY,QAAQ,KAAK,CAAC;YAChC,IAAI,WAAW;gBACb,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;gBACtC,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,gBAAgB;QAC/B;QAEA,SAAS;QACT,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAe,EAAE,YAA0B,EAAgB;QAC5F,IAAI;YACF,MAAM,YAAY,QAAQ,KAAK,CAAC;YAChC,IAAI,WAAW;gBACb,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;gBACtC,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,aAAa,KAAK;YAC9D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,eAAe;QAC9B;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAe,EAAkB;QAClE,IAAI;YACF,MAAM,YAAY,QAAQ,KAAK,CAAC;YAChC,IAAI,WAAW;gBACb,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;gBACtC,OAAO,OAAO,GAAG,CAAC,CAAC,OAAc,IAAI,CAAC,qBAAqB,CAAC;YAC9D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,eAAe;QAC9B;QAEA,OAAO,EAAE;IACX;IAEA;;GAEC,GACD,AAAQ,sBAAsB,GAAQ,EAAE,KAAa,EAAgB;QACnE,OAAO;YACL,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,cAAc;YACjC,OAAO,IAAI,KAAK,IAAI;YACpB,aAAa,IAAI,WAAW,IAAI;YAChC,WAAW,SAAS,IAAI,SAAS,KAAK;YACtC,YAAY,SAAS,IAAI,UAAU,KAAK;YACxC,OAAO,MAAM,OAAO,CAAC,IAAI,KAAK,IAC5B,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE;YACrE,YAAY,MAAM,OAAO,CAAC,IAAI,UAAU,IAAI,IAAI,UAAU,GAAG,EAAE;QACjE;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,GAAQ,EAAgB;QACpD,OAAO;YACL,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,cAAc;YACjC,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI;YAChC,aAAa,IAAI,WAAW,IAAI,IAAI,IAAI,IAAI;YAC5C,MAAM,IAAI,IAAI,IAAI;YAClB,eAAe,SAAS,IAAI,aAAa,IAAI,IAAI,IAAI,KAAK;YAC1D,YAAY,SAAS,IAAI,UAAU,KAAK;YACxC,eAAe,MAAM,OAAO,CAAC,IAAI,aAAa,IAAI,IAAI,aAAa,GAAG,EAAE;YACxE,WAAW,MAAM,OAAO,CAAC,IAAI,SAAS,IAAI,IAAI,SAAS,GAAG,EAAE;YAC5D,YAAY,MAAM,OAAO,CAAC,IAAI,UAAU,IAAI,IAAI,UAAU,GAAG,EAAE;QACjE;IACF;IAEA;;GAEC,GACD,AAAQ,qBAAqB,OAA4B,EAAkB;QACzE,OAAO;YACL;gBACE,IAAI,IAAI,CAAC,cAAc;gBACvB,OAAO,GAAG,QAAQ,WAAW,CAAC,KAAK,CAAC;gBACpC,aAAa,CAAC,EAAE,EAAE,QAAQ,WAAW,CAAC,QAAQ,CAAC;gBAC/C,MAAM;gBACN,eAAe;gBACf,YAAY;gBACZ,eAAe,EAAE;gBACjB,YAAY;oBAAC,CAAC,EAAE,EAAE,QAAQ,WAAW,CAAC,KAAK,CAAC;iBAAC;YAC/C;YACA;gBACE,IAAI,IAAI,CAAC,cAAc;gBACvB,OAAO,GAAG,QAAQ,WAAW,CAAC,KAAK,CAAC;gBACpC,aAAa,CAAC,MAAM,EAAE,QAAQ,WAAW,CAAC,GAAG,CAAC;gBAC9C,MAAM;gBACN,eAAe;gBACf,YAAY;gBACZ,eAAe;oBAAC,GAAG,QAAQ,WAAW,CAAC,KAAK,CAAC;iBAAC;gBAC9C,YAAY;oBAAC,CAAC,EAAE,EAAE,QAAQ,WAAW,CAAC,KAAK,CAAC;iBAAC;YAC/C;SACD;IACH;IAEA;;GAEC,GACD,AAAQ,oBAAoB,WAAmB,EAAgB;QAC7D,MAAM,QAAQ;YACZ;gBACE,IAAI,IAAI,CAAC,cAAc;gBACvB,OAAO,GAAG,YAAY,GAAG,CAAC;gBAC1B,aAAa,GAAG,YAAY,KAAK,CAAC;gBAClC,MAAM;gBACN,eAAe;gBACf,YAAY;gBACZ,eAAe,EAAE;gBACjB,YAAY;oBAAC,CAAC,EAAE,EAAE,YAAY,EAAE,CAAC;iBAAC;YACpC;SACD;QAED,OAAO;YACL,IAAI,IAAI,CAAC,cAAc;YACvB,OAAO,GAAG,YAAY,KAAK,CAAC;YAC5B,aAAa,CAAC,IAAI,EAAE,YAAY,KAAK,CAAC;YACtC,WAAW;YACX,YAAY;YACZ;YACA,YAAY;gBAAC,CAAC,EAAE,EAAE,YAAY,EAAE,CAAC;aAAC;QACpC;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAyB;QAC/B,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE;IAEQ,iBAAyB;QAC/B,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE;IAEQ,wBAAwB,OAA4B,EAAU;QACpE,OAAO,CAAC,GAAG,EAAE,QAAQ,WAAW,CAAC,0BAA0B,CAAC;IAC9D;IAEQ,2BAA2B,KAAqB,EAAU;QAChE,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAC/B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE,KAAK,MAAM,MAAM;QAC1F,OAAO,KAAK,KAAK,CAAC;IACpB;IAEQ,eAAe,OAA4B,EAAU;QAC3D,MAAM,iBAAiB,QAAQ,gBAAgB,CAAC,MAAM;QACtD,IAAI,mBAAmB,GAAG,OAAO;QACjC,IAAI,iBAAiB,GAAG,OAAO;QAC/B,IAAI,iBAAiB,IAAI,OAAO;QAChC,OAAO;IACT;IAEQ,yBAAyB,KAAqB,EAAE,SAAiB,EAAkB;QACzF,MAAM,eAAe,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,aAAa,EAAE;QAC3E,IAAI,gBAAgB,WAAW,OAAO;QAEtC,MAAM,QAAQ,YAAY;QAC1B,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,eAAe,KAAK,KAAK,CAAC,KAAK,aAAa,GAAG;YACjD,CAAC;IACH;IAEQ,wBAAwB,KAAqB,EAAkB;QACrE,OAAO;eAAI;SAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;IAC9D;IAEQ,gBAAgB,KAAqB,EAAkB;QAC7D,cAAc;QACd,MAAM,cAA4B;YAChC,IAAI,IAAI,CAAC,cAAc;YACvB,OAAO;YACP,aAAa;YACb,MAAM;YACN,eAAe;YACf,YAAY;YACZ,eAAe,MAAM,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;YAC9E,YAAY;gBAAC;aAAe;QAC9B;QAEA,MAAM,cAAc,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG;QAC9C,MAAM,SAAS;eAAI;SAAM;QACzB,OAAO,MAAM,CAAC,aAAa,GAAG;QAC9B,OAAO;IACT;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/llm/adaptiveLearning.ts"], "sourcesContent": ["/**\n * 自适应学习流程核心服务\n * 实现完整的学习循环：学习→遇到问题→分析依赖→规划路径→学习前置知识→回到原知识\n */\n\nimport { knowledgeGenerator } from './knowledgeGenerator'\nimport { dependencyAnalyzer } from './dependencyAnalyzer'\nimport { pathPlanner } from './pathPlanner'\nimport { llmService } from './service'\nimport { \n  GeneratedKnowledge,\n  DependencyAnalysisResponse,\n  LearningPath,\n  LearningStep\n} from './types'\n\n/**\n * 学习会话状态\n */\nexport interface LearningSession {\n  id: string\n  userId: string\n  targetTopic: string\n  currentStep: string\n  learningHistory: LearningRecord[]\n  knowledgeGraph: { [concept: string]: number } // 概念 -> 掌握程度\n  currentPath: LearningPath | null\n  blockedConcepts: string[] // 当前遇到困难的概念\n  sessionStartTime: Date\n  lastActivityTime: Date\n  status: 'active' | 'paused' | 'completed' | 'abandoned'\n}\n\n/**\n * 学习记录\n */\nexport interface LearningRecord {\n  id: string\n  timestamp: Date\n  action: 'start_topic' | 'encounter_difficulty' | 'request_help' | 'complete_step' | 'path_generated'\n  concept: string\n  details: any\n  timeSpent?: number\n  masteryBefore?: number\n  masteryAfter?: number\n}\n\n/**\n * 学习困难报告\n */\nexport interface LearningDifficulty {\n  concept: string\n  description: string\n  userQuestion?: string\n  context: string\n  timestamp: Date\n}\n\n/**\n * 自适应学习响应\n */\nexport interface AdaptiveLearningResponse {\n  type: 'knowledge_generated' | 'dependency_analysis' | 'path_suggested' | 'help_provided'\n  content: any\n  nextSteps: string[]\n  estimatedTime: number\n  confidence: number\n}\n\n/**\n * 自适应学习服务类\n */\nexport class AdaptiveLearningService {\n  private activeSessions: Map<string, LearningSession> = new Map()\n\n  /**\n   * 开始新的学习会话\n   */\n  async startLearningSession(\n    userId: string, \n    targetTopic: string,\n    userContext?: {\n      currentKnowledge: string[]\n      learningGoals: string[]\n      timeAvailable: number\n      preferredStyle: string\n    }\n  ): Promise<LearningSession> {\n    const sessionId = this.generateSessionId()\n    \n    // 生成初始知识内容\n    const initialKnowledge = await knowledgeGenerator.generateKnowledge({\n      topic: targetTopic,\n      userLevel: this.inferUserLevel(userContext?.currentKnowledge || []),\n      context: userContext ? JSON.stringify(userContext) : undefined,\n      preferences: {\n        includeExamples: true,\n        includeExercises: true\n      }\n    })\n\n    // 创建学习会话\n    const session: LearningSession = {\n      id: sessionId,\n      userId,\n      targetTopic,\n      currentStep: 'initial_learning',\n      learningHistory: [{\n        id: this.generateRecordId(),\n        timestamp: new Date(),\n        action: 'start_topic',\n        concept: targetTopic,\n        details: { initialKnowledge }\n      }],\n      knowledgeGraph: this.initializeKnowledgeGraph(userContext?.currentKnowledge || []),\n      currentPath: null,\n      blockedConcepts: [],\n      sessionStartTime: new Date(),\n      lastActivityTime: new Date(),\n      status: 'active'\n    }\n\n    this.activeSessions.set(sessionId, session)\n    return session\n  }\n\n  /**\n   * 处理学习困难 - 核心自适应逻辑\n   */\n  async handleLearningDifficulty(\n    sessionId: string,\n    difficulty: LearningDifficulty\n  ): Promise<AdaptiveLearningResponse> {\n    const session = this.activeSessions.get(sessionId)\n    if (!session) {\n      throw new Error('学习会话不存在')\n    }\n\n    try {\n      // 1. 记录困难\n      this.recordLearningDifficulty(session, difficulty)\n\n      // 2. 分析依赖关系\n      const dependencyAnalysis = await dependencyAnalyzer.analyzeDependencies({\n        currentTopic: session.targetTopic,\n        unknownConcepts: [difficulty.concept],\n        userContext: difficulty.context,\n        currentLevel: this.getCurrentUserLevel(session)\n      })\n\n      // 3. 评估是否需要学习前置知识\n      const needsPrerequisites = await this.assessPrerequisiteNeed(\n        session, \n        difficulty.concept, \n        dependencyAnalysis\n      )\n\n      if (needsPrerequisites) {\n        // 4. 生成学习路径\n        const learningPath = await this.generateAdaptivePath(session, dependencyAnalysis)\n        \n        // 5. 更新会话状态\n        session.currentPath = learningPath\n        session.blockedConcepts.push(difficulty.concept)\n        session.currentStep = 'learning_prerequisites'\n\n        return {\n          type: 'path_suggested',\n          content: {\n            analysis: dependencyAnalysis,\n            path: learningPath,\n            message: `我发现您在\"${difficulty.concept}\"上遇到了困难。让我为您规划一个学习路径，先掌握必要的前置知识。`\n          },\n          nextSteps: learningPath.steps.slice(0, 3).map(step => step.title),\n          estimatedTime: learningPath.totalTime,\n          confidence: 0.85\n        }\n      } else {\n        // 6. 提供直接帮助\n        const helpContent = await this.generateDirectHelp(session, difficulty)\n        \n        return {\n          type: 'help_provided',\n          content: helpContent,\n          nextSteps: ['继续当前学习', '尝试相关练习'],\n          estimatedTime: 15,\n          confidence: 0.75\n        }\n      }\n    } catch (error) {\n      console.error('处理学习困难失败:', error)\n      throw new Error('无法处理学习困难，请稍后重试')\n    }\n  }\n\n  /**\n   * 完成学习步骤\n   */\n  async completeStep(\n    sessionId: string,\n    stepId: string,\n    masteryLevel: number,\n    timeSpent: number,\n    feedback?: string\n  ): Promise<AdaptiveLearningResponse> {\n    const session = this.activeSessions.get(sessionId)\n    if (!session) {\n      throw new Error('学习会话不存在')\n    }\n\n    // 更新掌握程度\n    const step = this.findStepInPath(session.currentPath, stepId)\n    if (step) {\n      session.knowledgeGraph[step.title] = masteryLevel\n      \n      // 记录学习记录\n      session.learningHistory.push({\n        id: this.generateRecordId(),\n        timestamp: new Date(),\n        action: 'complete_step',\n        concept: step.title,\n        details: { stepId, feedback },\n        timeSpent,\n        masteryAfter: masteryLevel\n      })\n    }\n\n    // 检查是否可以回到原始学习目标\n    const canReturnToOriginal = await this.checkReturnCondition(session)\n    \n    if (canReturnToOriginal) {\n      // 移除已解决的阻塞概念\n      session.blockedConcepts = session.blockedConcepts.filter(\n        concept => session.knowledgeGraph[concept] < 70\n      )\n      \n      if (session.blockedConcepts.length === 0) {\n        session.currentStep = 'returning_to_original'\n        \n        return {\n          type: 'knowledge_generated',\n          content: {\n            message: `太好了！您已经掌握了必要的前置知识。现在让我们回到原始学习目标：\"${session.targetTopic}\"`,\n            nextConcept: session.targetTopic,\n            readinessScore: this.calculateReadinessScore(session)\n          },\n          nextSteps: ['回到原始学习目标', '应用新掌握的知识'],\n          estimatedTime: 30,\n          confidence: 0.9\n        }\n      }\n    }\n\n    // 继续当前路径\n    const nextStep = this.getNextStep(session.currentPath, stepId)\n    return {\n      type: 'path_suggested',\n      content: {\n        currentProgress: this.calculatePathProgress(session.currentPath, stepId),\n        nextStep,\n        encouragement: this.generateEncouragement(masteryLevel)\n      },\n      nextSteps: nextStep ? [nextStep.title] : ['完成当前路径'],\n      estimatedTime: nextStep?.estimatedTime || 0,\n      confidence: 0.8\n    }\n  }\n\n  /**\n   * 获取学习建议\n   */\n  async getLearningRecommendation(sessionId: string): Promise<AdaptiveLearningResponse> {\n    const session = this.activeSessions.get(sessionId)\n    if (!session) {\n      throw new Error('学习会话不存在')\n    }\n\n    const prompt = `基于用户的学习会话，提供个性化的学习建议：\n\n学习目标：${session.targetTopic}\n当前步骤：${session.currentStep}\n学习历史：${session.learningHistory.length}个记录\n知识掌握情况：${JSON.stringify(session.knowledgeGraph)}\n遇到困难的概念：${session.blockedConcepts.join(', ')}\n\n请提供：\n1. 当前学习状态评估\n2. 下一步学习建议\n3. 潜在的学习风险和解决方案\n4. 学习效率优化建议\n\n请以JSON格式返回建议。`\n\n    try {\n      const response = await llmService.chat({\n        messages: [\n          {\n            role: 'system',\n            content: '你是一个专业的学习顾问，能够基于学习数据提供个性化建议。'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.6,\n        maxTokens: 2000\n      })\n\n      const recommendation = this.parseRecommendationResponse(response.content)\n\n      return {\n        type: 'help_provided',\n        content: recommendation,\n        nextSteps: recommendation.nextSteps || ['继续当前学习'],\n        estimatedTime: 0,\n        confidence: 0.8\n      }\n    } catch (error) {\n      console.error('获取学习建议失败:', error)\n      throw new Error('无法获取学习建议')\n    }\n  }\n\n  /**\n   * 暂停学习会话\n   */\n  pauseSession(sessionId: string): void {\n    const session = this.activeSessions.get(sessionId)\n    if (session) {\n      session.status = 'paused'\n      session.lastActivityTime = new Date()\n    }\n  }\n\n  /**\n   * 恢复学习会话\n   */\n  resumeSession(sessionId: string): LearningSession | null {\n    const session = this.activeSessions.get(sessionId)\n    if (session) {\n      session.status = 'active'\n      session.lastActivityTime = new Date()\n      return session\n    }\n    return null\n  }\n\n  /**\n   * 获取学习会话\n   */\n  getSession(sessionId: string): LearningSession | null {\n    return this.activeSessions.get(sessionId) || null\n  }\n\n  /**\n   * 私有辅助方法\n   */\n  private generateSessionId(): string {\n    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  }\n\n  private generateRecordId(): string {\n    return `record_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  }\n\n  private inferUserLevel(currentKnowledge: string[]): 'beginner' | 'intermediate' | 'advanced' {\n    const count = currentKnowledge.length\n    if (count < 3) return 'beginner'\n    if (count < 10) return 'intermediate'\n    return 'advanced'\n  }\n\n  private initializeKnowledgeGraph(currentKnowledge: string[]): { [concept: string]: number } {\n    const graph: { [concept: string]: number } = {}\n    currentKnowledge.forEach(concept => {\n      graph[concept] = 80 // 假设已掌握的知识为80%\n    })\n    return graph\n  }\n\n  private recordLearningDifficulty(session: LearningSession, difficulty: LearningDifficulty): void {\n    session.learningHistory.push({\n      id: this.generateRecordId(),\n      timestamp: new Date(),\n      action: 'encounter_difficulty',\n      concept: difficulty.concept,\n      details: difficulty\n    })\n    session.lastActivityTime = new Date()\n  }\n\n  private getCurrentUserLevel(session: LearningSession): string {\n    const masteredConcepts = Object.values(session.knowledgeGraph).filter(level => level >= 70).length\n    if (masteredConcepts < 3) return 'beginner'\n    if (masteredConcepts < 10) return 'intermediate'\n    return 'advanced'\n  }\n\n  private async assessPrerequisiteNeed(\n    session: LearningSession,\n    concept: string,\n    analysis: DependencyAnalysisResponse\n  ): Promise<boolean> {\n    // 检查用户是否已经掌握了必要的前置知识\n    const missingPrerequisites = analysis.dependencies.filter(dep => \n      !session.knowledgeGraph[dep.concept] || session.knowledgeGraph[dep.concept] < 60\n    )\n\n    return missingPrerequisites.length > 0\n  }\n\n  private async generateAdaptivePath(\n    session: LearningSession,\n    analysis: DependencyAnalysisResponse\n  ): Promise<LearningPath> {\n    const currentKnowledge = Object.keys(session.knowledgeGraph).filter(\n      concept => session.knowledgeGraph[concept] >= 70\n    )\n\n    const missingKnowledge = analysis.dependencies.map(dep => dep.concept)\n\n    return await pathPlanner.generateLearningPath({\n      targetTopic: session.targetTopic,\n      currentKnowledge,\n      missingKnowledge,\n      timeConstraints: {\n        sessionLength: 60 // 默认60分钟会话\n      }\n    })\n  }\n\n  private async generateDirectHelp(\n    session: LearningSession,\n    difficulty: LearningDifficulty\n  ): Promise<any> {\n    const prompt = `用户在学习\"${session.targetTopic}\"时，对\"${difficulty.concept}\"感到困惑。\n\n用户问题：${difficulty.userQuestion || '理解困难'}\n上下文：${difficulty.context}\n\n请提供：\n1. 概念的简化解释\n2. 具体的例子或类比\n3. 学习建议和技巧\n4. 相关的练习或思考题\n\n请以易懂的方式回答，帮助用户克服这个困难。`\n\n    const response = await llmService.chat({\n      messages: [\n        {\n          role: 'system',\n          content: '你是一个耐心的导师，擅长用简单易懂的方式解释复杂概念。'\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: 0.7,\n      maxTokens: 1500\n    })\n\n    return {\n      explanation: response.content,\n      concept: difficulty.concept,\n      helpType: 'direct_explanation'\n    }\n  }\n\n  private checkReturnCondition(session: LearningSession): boolean {\n    // 检查是否所有阻塞概念都已掌握\n    return session.blockedConcepts.every(concept => \n      session.knowledgeGraph[concept] && session.knowledgeGraph[concept] >= 70\n    )\n  }\n\n  private calculateReadinessScore(session: LearningSession): number {\n    const relevantConcepts = Object.keys(session.knowledgeGraph)\n    if (relevantConcepts.length === 0) return 50\n\n    const avgMastery = relevantConcepts.reduce((sum, concept) => \n      sum + (session.knowledgeGraph[concept] || 0), 0\n    ) / relevantConcepts.length\n\n    return Math.round(avgMastery)\n  }\n\n  private findStepInPath(path: LearningPath | null, stepId: string): LearningStep | null {\n    if (!path) return null\n    return path.steps.find(step => step.id === stepId) || null\n  }\n\n  private getNextStep(path: LearningPath | null, currentStepId: string): LearningStep | null {\n    if (!path) return null\n    const currentIndex = path.steps.findIndex(step => step.id === currentStepId)\n    return currentIndex >= 0 && currentIndex < path.steps.length - 1 ? \n      path.steps[currentIndex + 1] : null\n  }\n\n  private calculatePathProgress(path: LearningPath | null, currentStepId: string): number {\n    if (!path) return 0\n    const currentIndex = path.steps.findIndex(step => step.id === currentStepId)\n    return currentIndex >= 0 ? Math.round(((currentIndex + 1) / path.steps.length) * 100) : 0\n  }\n\n  private generateEncouragement(masteryLevel: number): string {\n    if (masteryLevel >= 80) return '太棒了！您掌握得很好！'\n    if (masteryLevel >= 60) return '不错的进步！继续保持！'\n    if (masteryLevel >= 40) return '您正在进步，继续努力！'\n    return '没关系，学习需要时间，让我们再试一次！'\n  }\n\n  private parseRecommendationResponse(content: string): any {\n    try {\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/)\n      if (jsonMatch) {\n        return JSON.parse(jsonMatch[0])\n      }\n    } catch (error) {\n      console.warn('解析建议响应失败:', error)\n    }\n\n    return {\n      assessment: '继续当前的学习进度',\n      nextSteps: ['继续当前学习'],\n      risks: [],\n      optimizations: []\n    }\n  }\n}\n\n// 导出单例实例\nexport const adaptiveLearning = new AdaptiveLearningService()\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AACA;AACA;;;;;AAgEO,MAAM;IACH,iBAA+C,IAAI,MAAK;IAEhE;;GAEC,GACD,MAAM,qBACJ,MAAc,EACd,WAAmB,EACnB,WAKC,EACyB;QAC1B,MAAM,YAAY,IAAI,CAAC,iBAAiB;QAExC,WAAW;QACX,MAAM,mBAAmB,MAAM,yIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC;YAClE,OAAO;YACP,WAAW,IAAI,CAAC,cAAc,CAAC,aAAa,oBAAoB,EAAE;YAClE,SAAS,cAAc,KAAK,SAAS,CAAC,eAAe;YACrD,aAAa;gBACX,iBAAiB;gBACjB,kBAAkB;YACpB;QACF;QAEA,SAAS;QACT,MAAM,UAA2B;YAC/B,IAAI;YACJ;YACA;YACA,aAAa;YACb,iBAAiB;gBAAC;oBAChB,IAAI,IAAI,CAAC,gBAAgB;oBACzB,WAAW,IAAI;oBACf,QAAQ;oBACR,SAAS;oBACT,SAAS;wBAAE;oBAAiB;gBAC9B;aAAE;YACF,gBAAgB,IAAI,CAAC,wBAAwB,CAAC,aAAa,oBAAoB,EAAE;YACjF,aAAa;YACb,iBAAiB,EAAE;YACnB,kBAAkB,IAAI;YACtB,kBAAkB,IAAI;YACtB,QAAQ;QACV;QAEA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW;QACnC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,yBACJ,SAAiB,EACjB,UAA8B,EACK;QACnC,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACxC,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,UAAU;YACV,IAAI,CAAC,wBAAwB,CAAC,SAAS;YAEvC,YAAY;YACZ,MAAM,qBAAqB,MAAM,yIAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC;gBACtE,cAAc,QAAQ,WAAW;gBACjC,iBAAiB;oBAAC,WAAW,OAAO;iBAAC;gBACrC,aAAa,WAAW,OAAO;gBAC/B,cAAc,IAAI,CAAC,mBAAmB,CAAC;YACzC;YAEA,kBAAkB;YAClB,MAAM,qBAAqB,MAAM,IAAI,CAAC,sBAAsB,CAC1D,SACA,WAAW,OAAO,EAClB;YAGF,IAAI,oBAAoB;gBACtB,YAAY;gBACZ,MAAM,eAAe,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS;gBAE9D,YAAY;gBACZ,QAAQ,WAAW,GAAG;gBACtB,QAAQ,eAAe,CAAC,IAAI,CAAC,WAAW,OAAO;gBAC/C,QAAQ,WAAW,GAAG;gBAEtB,OAAO;oBACL,MAAM;oBACN,SAAS;wBACP,UAAU;wBACV,MAAM;wBACN,SAAS,CAAC,MAAM,EAAE,WAAW,OAAO,CAAC,gCAAgC,CAAC;oBACxE;oBACA,WAAW,aAAa,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;oBAChE,eAAe,aAAa,SAAS;oBACrC,YAAY;gBACd;YACF,OAAO;gBACL,YAAY;gBACZ,MAAM,cAAc,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS;gBAE3D,OAAO;oBACL,MAAM;oBACN,SAAS;oBACT,WAAW;wBAAC;wBAAU;qBAAS;oBAC/B,eAAe;oBACf,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,aACJ,SAAiB,EACjB,MAAc,EACd,YAAoB,EACpB,SAAiB,EACjB,QAAiB,EACkB;QACnC,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACxC,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,SAAS;QACT,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,WAAW,EAAE;QACtD,IAAI,MAAM;YACR,QAAQ,cAAc,CAAC,KAAK,KAAK,CAAC,GAAG;YAErC,SAAS;YACT,QAAQ,eAAe,CAAC,IAAI,CAAC;gBAC3B,IAAI,IAAI,CAAC,gBAAgB;gBACzB,WAAW,IAAI;gBACf,QAAQ;gBACR,SAAS,KAAK,KAAK;gBACnB,SAAS;oBAAE;oBAAQ;gBAAS;gBAC5B;gBACA,cAAc;YAChB;QACF;QAEA,iBAAiB;QACjB,MAAM,sBAAsB,MAAM,IAAI,CAAC,oBAAoB,CAAC;QAE5D,IAAI,qBAAqB;YACvB,aAAa;YACb,QAAQ,eAAe,GAAG,QAAQ,eAAe,CAAC,MAAM,CACtD,CAAA,UAAW,QAAQ,cAAc,CAAC,QAAQ,GAAG;YAG/C,IAAI,QAAQ,eAAe,CAAC,MAAM,KAAK,GAAG;gBACxC,QAAQ,WAAW,GAAG;gBAEtB,OAAO;oBACL,MAAM;oBACN,SAAS;wBACP,SAAS,CAAC,iCAAiC,EAAE,QAAQ,WAAW,CAAC,CAAC,CAAC;wBACnE,aAAa,QAAQ,WAAW;wBAChC,gBAAgB,IAAI,CAAC,uBAAuB,CAAC;oBAC/C;oBACA,WAAW;wBAAC;wBAAY;qBAAW;oBACnC,eAAe;oBACf,YAAY;gBACd;YACF;QACF;QAEA,SAAS;QACT,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,QAAQ,WAAW,EAAE;QACvD,OAAO;YACL,MAAM;YACN,SAAS;gBACP,iBAAiB,IAAI,CAAC,qBAAqB,CAAC,QAAQ,WAAW,EAAE;gBACjE;gBACA,eAAe,IAAI,CAAC,qBAAqB,CAAC;YAC5C;YACA,WAAW,WAAW;gBAAC,SAAS,KAAK;aAAC,GAAG;gBAAC;aAAS;YACnD,eAAe,UAAU,iBAAiB;YAC1C,YAAY;QACd;IACF;IAEA;;GAEC,GACD,MAAM,0BAA0B,SAAiB,EAAqC;QACpF,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACxC,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,CAAC;;KAEf,EAAE,QAAQ,WAAW,CAAC;KACtB,EAAE,QAAQ,WAAW,CAAC;KACtB,EAAE,QAAQ,eAAe,CAAC,MAAM,CAAC;OAC/B,EAAE,KAAK,SAAS,CAAC,QAAQ,cAAc,EAAE;QACxC,EAAE,QAAQ,eAAe,CAAC,IAAI,CAAC,MAAM;;;;;;;;aAQhC,CAAC;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,WAAW;YACb;YAEA,MAAM,iBAAiB,IAAI,CAAC,2BAA2B,CAAC,SAAS,OAAO;YAExE,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT,WAAW,eAAe,SAAS,IAAI;oBAAC;iBAAS;gBACjD,eAAe;gBACf,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,SAAiB,EAAQ;QACpC,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,MAAM,GAAG;YACjB,QAAQ,gBAAgB,GAAG,IAAI;QACjC;IACF;IAEA;;GAEC,GACD,cAAc,SAAiB,EAA0B;QACvD,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,MAAM,GAAG;YACjB,QAAQ,gBAAgB,GAAG,IAAI;YAC/B,OAAO;QACT;QACA,OAAO;IACT;IAEA;;GAEC,GACD,WAAW,SAAiB,EAA0B;QACpD,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc;IAC/C;IAEA;;GAEC,GACD,AAAQ,oBAA4B;QAClC,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC3E;IAEQ,mBAA2B;QACjC,OAAO,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC1E;IAEQ,eAAe,gBAA0B,EAA4C;QAC3F,MAAM,QAAQ,iBAAiB,MAAM;QACrC,IAAI,QAAQ,GAAG,OAAO;QACtB,IAAI,QAAQ,IAAI,OAAO;QACvB,OAAO;IACT;IAEQ,yBAAyB,gBAA0B,EAAiC;QAC1F,MAAM,QAAuC,CAAC;QAC9C,iBAAiB,OAAO,CAAC,CAAA;YACvB,KAAK,CAAC,QAAQ,GAAG,IAAG,eAAe;QACrC;QACA,OAAO;IACT;IAEQ,yBAAyB,OAAwB,EAAE,UAA8B,EAAQ;QAC/F,QAAQ,eAAe,CAAC,IAAI,CAAC;YAC3B,IAAI,IAAI,CAAC,gBAAgB;YACzB,WAAW,IAAI;YACf,QAAQ;YACR,SAAS,WAAW,OAAO;YAC3B,SAAS;QACX;QACA,QAAQ,gBAAgB,GAAG,IAAI;IACjC;IAEQ,oBAAoB,OAAwB,EAAU;QAC5D,MAAM,mBAAmB,OAAO,MAAM,CAAC,QAAQ,cAAc,EAAE,MAAM,CAAC,CAAA,QAAS,SAAS,IAAI,MAAM;QAClG,IAAI,mBAAmB,GAAG,OAAO;QACjC,IAAI,mBAAmB,IAAI,OAAO;QAClC,OAAO;IACT;IAEA,MAAc,uBACZ,OAAwB,EACxB,OAAe,EACf,QAAoC,EAClB;QAClB,qBAAqB;QACrB,MAAM,uBAAuB,SAAS,YAAY,CAAC,MAAM,CAAC,CAAA,MACxD,CAAC,QAAQ,cAAc,CAAC,IAAI,OAAO,CAAC,IAAI,QAAQ,cAAc,CAAC,IAAI,OAAO,CAAC,GAAG;QAGhF,OAAO,qBAAqB,MAAM,GAAG;IACvC;IAEA,MAAc,qBACZ,OAAwB,EACxB,QAAoC,EACb;QACvB,MAAM,mBAAmB,OAAO,IAAI,CAAC,QAAQ,cAAc,EAAE,MAAM,CACjE,CAAA,UAAW,QAAQ,cAAc,CAAC,QAAQ,IAAI;QAGhD,MAAM,mBAAmB,SAAS,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,OAAO;QAErE,OAAO,MAAM,kIAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC;YAC5C,aAAa,QAAQ,WAAW;YAChC;YACA;YACA,iBAAiB;gBACf,eAAe,GAAG,WAAW;YAC/B;QACF;IACF;IAEA,MAAc,mBACZ,OAAwB,EACxB,UAA8B,EAChB;QACd,MAAM,SAAS,CAAC,MAAM,EAAE,QAAQ,WAAW,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC;;KAErE,EAAE,WAAW,YAAY,IAAI,OAAO;IACrC,EAAE,WAAW,OAAO,CAAC;;;;;;;;qBAQJ,CAAC;QAElB,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;YACrC,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,WAAW;QACb;QAEA,OAAO;YACL,aAAa,SAAS,OAAO;YAC7B,SAAS,WAAW,OAAO;YAC3B,UAAU;QACZ;IACF;IAEQ,qBAAqB,OAAwB,EAAW;QAC9D,iBAAiB;QACjB,OAAO,QAAQ,eAAe,CAAC,KAAK,CAAC,CAAA,UACnC,QAAQ,cAAc,CAAC,QAAQ,IAAI,QAAQ,cAAc,CAAC,QAAQ,IAAI;IAE1E;IAEQ,wBAAwB,OAAwB,EAAU;QAChE,MAAM,mBAAmB,OAAO,IAAI,CAAC,QAAQ,cAAc;QAC3D,IAAI,iBAAiB,MAAM,KAAK,GAAG,OAAO;QAE1C,MAAM,aAAa,iBAAiB,MAAM,CAAC,CAAC,KAAK,UAC/C,MAAM,CAAC,QAAQ,cAAc,CAAC,QAAQ,IAAI,CAAC,GAAG,KAC5C,iBAAiB,MAAM;QAE3B,OAAO,KAAK,KAAK,CAAC;IACpB;IAEQ,eAAe,IAAyB,EAAE,MAAc,EAAuB;QACrF,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,WAAW;IACxD;IAEQ,YAAY,IAAyB,EAAE,aAAqB,EAAuB;QACzF,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,eAAe,KAAK,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC9D,OAAO,gBAAgB,KAAK,eAAe,KAAK,KAAK,CAAC,MAAM,GAAG,IAC7D,KAAK,KAAK,CAAC,eAAe,EAAE,GAAG;IACnC;IAEQ,sBAAsB,IAAyB,EAAE,aAAqB,EAAU;QACtF,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,eAAe,KAAK,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC9D,OAAO,gBAAgB,IAAI,KAAK,KAAK,CAAC,AAAC,CAAC,eAAe,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,GAAI,OAAO;IAC1F;IAEQ,sBAAsB,YAAoB,EAAU;QAC1D,IAAI,gBAAgB,IAAI,OAAO;QAC/B,IAAI,gBAAgB,IAAI,OAAO;QAC/B,IAAI,gBAAgB,IAAI,OAAO;QAC/B,OAAO;IACT;IAEQ,4BAA4B,OAAe,EAAO;QACxD,IAAI;YACF,MAAM,YAAY,QAAQ,KAAK,CAAC;YAChC,IAAI,WAAW;gBACb,OAAO,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,aAAa;QAC5B;QAEA,OAAO;YACL,YAAY;YACZ,WAAW;gBAAC;aAAS;YACrB,OAAO,EAAE;YACT,eAAe,EAAE;QACnB;IACF;AACF;AAGO,MAAM,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/app/api/adaptive/start-session/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { adaptiveLearning } from '@/lib/llm/adaptiveLearning'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId, targetTopic, userContext } = await request.json()\n\n    if (!userId || !targetTopic) {\n      return NextResponse.json(\n        { error: '用户ID和学习主题不能为空' },\n        { status: 400 }\n      )\n    }\n\n    // 开始新的学习会话\n    const session = await adaptiveLearning.startLearningSession(\n      userId,\n      targetTopic,\n      userContext\n    )\n\n    return NextResponse.json({\n      success: true,\n      session,\n      message: '学习会话已成功启动',\n      timestamp: new Date().toISOString()\n    })\n\n  } catch (error) {\n    console.error('启动学习会话失败:', error)\n    \n    return NextResponse.json(\n      { \n        error: '启动学习会话失败',\n        details: error instanceof Error ? error.message : '未知错误'\n      },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE/D,IAAI,CAAC,UAAU,CAAC,aAAa;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgB,GACzB;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,UAAU,MAAM,uIAAA,CAAA,mBAAgB,CAAC,oBAAoB,CACzD,QACA,aACA;QAGF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;QACnC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAE3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}