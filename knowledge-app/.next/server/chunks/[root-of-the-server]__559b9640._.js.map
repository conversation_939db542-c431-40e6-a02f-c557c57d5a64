{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/llm/config.ts"], "sourcesContent": ["/**\n * LLM配置管理\n * 支持多种LLM提供商和模型的配置\n */\n\nexport interface LLMConfig {\n  provider: 'groq' | 'moonshot' | 'openai' | 'anthropic'\n  apiKey: string\n  apiBase?: string\n  model: string\n  maxTokens?: number\n  temperature?: number\n  timeout?: number\n}\n\nexport interface LLMProviderConfig {\n  name: string\n  apiBase: string\n  defaultModel: string\n  supportedModels: string[]\n  headers?: Record<string, string>\n}\n\n/**\n * 支持的LLM提供商配置\n */\nexport const LLM_PROVIDERS: Record<string, LLMProviderConfig> = {\n  groq: {\n    name: 'Groq',\n    apiBase: 'https://api.groq.com/openai/v1',\n    defaultModel: 'llama-3.1-70b-versatile',\n    supportedModels: [\n      'llama-3.1-70b-versatile',\n      'llama-3.1-8b-instant',\n      'mixtral-8x7b-32768',\n      'gemma2-9b-it'\n    ]\n  },\n  moonshot: {\n    name: 'Moonshot AI',\n    apiBase: 'https://api.moonshot.cn/v1',\n    defaultModel: 'moonshot-v1-8k',\n    supportedModels: [\n      'moonshot-v1-8k',\n      'moonshot-v1-32k',\n      'moonshot-v1-128k',\n      'moonshotai/kimi-k2-instruct'\n    ]\n  },\n  openai: {\n    name: 'OpenAI',\n    apiBase: 'https://api.openai.com/v1',\n    defaultModel: 'gpt-4o-mini',\n    supportedModels: [\n      'gpt-4o',\n      'gpt-4o-mini',\n      'gpt-4-turbo',\n      'gpt-3.5-turbo'\n    ]\n  }\n}\n\n/**\n * 默认LLM配置\n */\nexport const DEFAULT_LLM_CONFIG: LLMConfig = {\n  provider: 'moonshot',\n  apiKey: process.env.MOONSHOT_API_KEY || '',\n  apiBase: LLM_PROVIDERS.moonshot.apiBase,\n  model: 'moonshotai/kimi-k2-instruct',\n  maxTokens: 4000,\n  temperature: 0.7,\n  timeout: 30000\n}\n\n/**\n * 从环境变量获取LLM配置\n */\nexport function getLLMConfigFromEnv(): LLMConfig {\n  // 优先使用Groq配置（如果可用）\n  if (process.env.GROQ_API_KEY) {\n    return {\n      provider: 'groq',\n      apiKey: process.env.GROQ_API_KEY,\n      apiBase: process.env.GROQ_API_BASE || LLM_PROVIDERS.groq.apiBase,\n      model: process.env.GROQ_MODEL || LLM_PROVIDERS.groq.defaultModel,\n      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),\n      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),\n      timeout: parseInt(process.env.LLM_TIMEOUT || '30000')\n    }\n  }\n\n  // 使用Moonshot配置\n  if (process.env.MOONSHOT_API_KEY) {\n    return {\n      provider: 'moonshot',\n      apiKey: process.env.MOONSHOT_API_KEY,\n      apiBase: process.env.MOONSHOT_API_BASE || LLM_PROVIDERS.moonshot.apiBase,\n      model: process.env.MOONSHOT_MODEL || 'moonshotai/kimi-k2-instruct',\n      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),\n      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),\n      timeout: parseInt(process.env.LLM_TIMEOUT || '30000')\n    }\n  }\n\n  // 使用OpenAI配置\n  if (process.env.OPENAI_API_KEY) {\n    return {\n      provider: 'openai',\n      apiKey: process.env.OPENAI_API_KEY,\n      apiBase: process.env.OPENAI_API_BASE || LLM_PROVIDERS.openai.apiBase,\n      model: process.env.OPENAI_MODEL || LLM_PROVIDERS.openai.defaultModel,\n      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),\n      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),\n      timeout: parseInt(process.env.LLM_TIMEOUT || '30000')\n    }\n  }\n\n  // 返回默认配置（开发模式）\n  return {\n    ...DEFAULT_LLM_CONFIG,\n    apiKey: 'mock-api-key' // 开发模式下的模拟key\n  }\n}\n\n/**\n * 验证LLM配置\n */\nexport function validateLLMConfig(config: LLMConfig): boolean {\n  if (!config.apiKey || config.apiKey === 'mock-api-key') {\n    console.warn('LLM API Key未配置，将使用模拟模式')\n    return false\n  }\n\n  if (!config.model) {\n    console.error('LLM模型未指定')\n    return false\n  }\n\n  const provider = LLM_PROVIDERS[config.provider]\n  if (!provider) {\n    console.error(`不支持的LLM提供商: ${config.provider}`)\n    return false\n  }\n\n  if (!provider.supportedModels.includes(config.model)) {\n    console.warn(`模型 ${config.model} 可能不被 ${provider.name} 支持`)\n  }\n\n  return true\n}\n\n/**\n * 获取当前LLM配置\n */\nexport function getCurrentLLMConfig(): LLMConfig {\n  const config = getLLMConfigFromEnv()\n  \n  if (!validateLLMConfig(config)) {\n    console.log('使用模拟LLM配置进行开发')\n  }\n\n  return config\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAuBM,MAAM,gBAAmD;IAC9D,MAAM;QACJ,MAAM;QACN,SAAS;QACT,cAAc;QACd,iBAAiB;YACf;YACA;YACA;YACA;SACD;IACH;IACA,UAAU;QACR,MAAM;QACN,SAAS;QACT,cAAc;QACd,iBAAiB;YACf;YACA;YACA;YACA;SACD;IACH;IACA,QAAQ;QACN,MAAM;QACN,SAAS;QACT,cAAc;QACd,iBAAiB;YACf;YACA;YACA;YACA;SACD;IACH;AACF;AAKO,MAAM,qBAAgC;IAC3C,UAAU;IACV,QAAQ,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IACxC,SAAS,cAAc,QAAQ,CAAC,OAAO;IACvC,OAAO;IACP,WAAW;IACX,aAAa;IACb,SAAS;AACX;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO;YACL,UAAU;YACV,QAAQ,QAAQ,GAAG,CAAC,YAAY;YAChC,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI,cAAc,IAAI,CAAC,OAAO;YAChE,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI,cAAc,IAAI,CAAC,YAAY;YAChE,WAAW,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;YAClD,aAAa,WAAW,QAAQ,GAAG,CAAC,eAAe,IAAI;YACvD,SAAS,SAAS,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC/C;IACF;IAEA,eAAe;IACf,IAAI,QAAQ,GAAG,CAAC,gBAAgB,EAAE;QAChC,OAAO;YACL,UAAU;YACV,QAAQ,QAAQ,GAAG,CAAC,gBAAgB;YACpC,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI,cAAc,QAAQ,CAAC,OAAO;YACxE,OAAO,QAAQ,GAAG,CAAC,cAAc,IAAI;YACrC,WAAW,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;YAClD,aAAa,WAAW,QAAQ,GAAG,CAAC,eAAe,IAAI;YACvD,SAAS,SAAS,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC/C;IACF;IAEA,aAAa;IACb,IAAI,QAAQ,GAAG,CAAC,cAAc,EAAE;QAC9B,OAAO;YACL,UAAU;YACV,QAAQ,QAAQ,GAAG,CAAC,cAAc;YAClC,SAAS,QAAQ,GAAG,CAAC,eAAe,IAAI,cAAc,MAAM,CAAC,OAAO;YACpE,OAAO,QAAQ,GAAG,CAAC,YAAY,IAAI,cAAc,MAAM,CAAC,YAAY;YACpE,WAAW,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;YAClD,aAAa,WAAW,QAAQ,GAAG,CAAC,eAAe,IAAI;YACvD,SAAS,SAAS,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC/C;IACF;IAEA,eAAe;IACf,OAAO;QACL,GAAG,kBAAkB;QACrB,QAAQ,eAAe,cAAc;IACvC;AACF;AAKO,SAAS,kBAAkB,MAAiB;IACjD,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,gBAAgB;QACtD,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI,CAAC,OAAO,KAAK,EAAE;QACjB,QAAQ,KAAK,CAAC;QACd,OAAO;IACT;IAEA,MAAM,WAAW,aAAa,CAAC,OAAO,QAAQ,CAAC;IAC/C,IAAI,CAAC,UAAU;QACb,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,OAAO,QAAQ,EAAE;QAC9C,OAAO;IACT;IAEA,IAAI,CAAC,SAAS,eAAe,CAAC,QAAQ,CAAC,OAAO,KAAK,GAAG;QACpD,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,GAAG,CAAC;IAC5D;IAEA,OAAO;AACT;AAKO,SAAS;IACd,MAAM,SAAS;IAEf,IAAI,CAAC,kBAAkB,SAAS;QAC9B,QAAQ,GAAG,CAAC;IACd;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/llm/service.ts"], "sourcesContent": ["/**\n * LLM服务实现\n * 支持多种LLM提供商的统一接口\n */\n\nimport { LLMConfig, LLM_PROVIDERS, getCurrentLLMConfig } from './config'\nimport { \n  LLMService, \n  LLMMessage, \n  LLMResponse, \n  LLMStreamResponse, \n  LLMRequestOptions, \n  LLMError \n} from './types'\n\n/**\n * 基础LLM服务实现\n */\nexport class BaseLLMService implements LLMService {\n  protected config: LLMConfig\n\n  constructor(config?: LLMConfig) {\n    this.config = config || getCurrentLLMConfig()\n  }\n\n  /**\n   * 发送聊天请求\n   */\n  async chat(options: LLMRequestOptions): Promise<LLMResponse> {\n    // 如果是模拟模式，返回模拟响应\n    if (this.config.apiKey === 'mock-api-key') {\n      return this.getMockResponse(options)\n    }\n\n    try {\n      const response = await this.makeRequest(options)\n      return this.parseResponse(response)\n    } catch (error) {\n      throw this.handleError(error)\n    }\n  }\n\n  /**\n   * 发送流式聊天请求\n   */\n  async* chatStream(options: LLMRequestOptions): AsyncGenerator<LLMStreamResponse, void, unknown> {\n    // 如果是模拟模式，返回模拟流响应\n    if (this.config.apiKey === 'mock-api-key') {\n      yield* this.getMockStreamResponse(options)\n      return\n    }\n\n    try {\n      const response = await this.makeStreamRequest(options)\n      yield* this.parseStreamResponse(response)\n    } catch (error) {\n      throw this.handleError(error)\n    }\n  }\n\n  /**\n   * 检查服务是否可用\n   */\n  async isAvailable(): Promise<boolean> {\n    if (this.config.apiKey === 'mock-api-key') {\n      return true // 模拟模式总是可用\n    }\n\n    try {\n      const testResponse = await this.chat({\n        messages: [{ role: 'user', content: 'Hello' }],\n        maxTokens: 10\n      })\n      return !!testResponse.content\n    } catch (error) {\n      console.warn('LLM服务不可用:', error)\n      return false\n    }\n  }\n\n  /**\n   * 获取支持的模型列表\n   */\n  getSupportedModels(): string[] {\n    const provider = LLM_PROVIDERS[this.config.provider]\n    return provider ? provider.supportedModels : []\n  }\n\n  /**\n   * 获取服务提供商名称\n   */\n  getProviderName(): string {\n    const provider = LLM_PROVIDERS[this.config.provider]\n    return provider ? provider.name : this.config.provider\n  }\n\n  /**\n   * 发送HTTP请求到LLM API\n   */\n  protected async makeRequest(options: LLMRequestOptions): Promise<Response> {\n    const provider = LLM_PROVIDERS[this.config.provider]\n    const url = `${this.config.apiBase || provider.apiBase}/chat/completions`\n\n    const requestBody = {\n      model: this.config.model,\n      messages: options.messages,\n      max_tokens: options.maxTokens || this.config.maxTokens,\n      temperature: options.temperature ?? this.config.temperature,\n      stream: false,\n      ...(options.stop && { stop: options.stop }),\n      ...(options.topP && { top_p: options.topP }),\n      ...(options.frequencyPenalty && { frequency_penalty: options.frequencyPenalty }),\n      ...(options.presencePenalty && { presence_penalty: options.presencePenalty })\n    }\n\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${this.config.apiKey}`,\n        ...provider.headers\n      },\n      body: JSON.stringify(requestBody),\n      signal: AbortSignal.timeout(this.config.timeout || 30000)\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    return response\n  }\n\n  /**\n   * 发送流式HTTP请求到LLM API\n   */\n  protected async makeStreamRequest(options: LLMRequestOptions): Promise<Response> {\n    const provider = LLM_PROVIDERS[this.config.provider]\n    const url = `${this.config.apiBase || provider.apiBase}/chat/completions`\n\n    const requestBody = {\n      model: this.config.model,\n      messages: options.messages,\n      max_tokens: options.maxTokens || this.config.maxTokens,\n      temperature: options.temperature ?? this.config.temperature,\n      stream: true,\n      ...(options.stop && { stop: options.stop }),\n      ...(options.topP && { top_p: options.topP }),\n      ...(options.frequencyPenalty && { frequency_penalty: options.frequencyPenalty }),\n      ...(options.presencePenalty && { presence_penalty: options.presencePenalty })\n    }\n\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${this.config.apiKey}`,\n        ...provider.headers\n      },\n      body: JSON.stringify(requestBody),\n      signal: AbortSignal.timeout(this.config.timeout || 30000)\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    return response\n  }\n\n  /**\n   * 解析LLM响应\n   */\n  protected async parseResponse(response: Response): Promise<LLMResponse> {\n    const data = await response.json()\n    \n    return {\n      content: data.choices[0]?.message?.content || '',\n      usage: data.usage,\n      model: data.model,\n      finish_reason: data.choices[0]?.finish_reason\n    }\n  }\n\n  /**\n   * 解析流式LLM响应\n   */\n  protected async* parseStreamResponse(response: Response): AsyncGenerator<LLMStreamResponse, void, unknown> {\n    const reader = response.body?.getReader()\n    if (!reader) {\n      throw new Error('无法读取响应流')\n    }\n\n    const decoder = new TextDecoder()\n    let buffer = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        buffer += decoder.decode(value, { stream: true })\n        const lines = buffer.split('\\n')\n        buffer = lines.pop() || ''\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6)\n            if (data === '[DONE]') {\n              return\n            }\n\n            try {\n              const parsed = JSON.parse(data)\n              const content = parsed.choices[0]?.delta?.content || ''\n              \n              yield {\n                content,\n                done: false,\n                usage: parsed.usage\n              }\n            } catch (error) {\n              console.warn('解析流数据失败:', error)\n            }\n          }\n        }\n      }\n    } finally {\n      reader.releaseLock()\n    }\n\n    yield {\n      content: '',\n      done: true\n    }\n  }\n\n  /**\n   * 处理错误\n   */\n  protected handleError(error: any): LLMError {\n    if (error.name === 'AbortError') {\n      return {\n        code: 'timeout',\n        message: 'LLM请求超时',\n        type: 'network_error',\n        details: error\n      }\n    }\n\n    if (error.message?.includes('401')) {\n      return {\n        code: 'unauthorized',\n        message: 'API密钥无效或已过期',\n        type: 'authentication_error',\n        details: error\n      }\n    }\n\n    if (error.message?.includes('429')) {\n      return {\n        code: 'rate_limit',\n        message: 'API调用频率超限',\n        type: 'rate_limit',\n        details: error\n      }\n    }\n\n    return {\n      code: 'unknown',\n      message: error.message || 'LLM服务错误',\n      type: 'api_error',\n      details: error\n    }\n  }\n\n  /**\n   * 获取模拟响应（开发模式）\n   */\n  protected getMockResponse(options: LLMRequestOptions): LLMResponse {\n    const userMessage = options.messages.find(m => m.role === 'user')?.content || ''\n    \n    let mockContent = '这是一个模拟的LLM响应。'\n    \n    if (userMessage.includes('知识') || userMessage.includes('学习')) {\n      mockContent = '基于您的请求，我建议从基础概念开始学习，然后逐步深入到高级主题。'\n    } else if (userMessage.includes('依赖') || userMessage.includes('前置')) {\n      mockContent = '分析您提到的概念，需要先掌握以下前置知识：1. 基础概念 2. 相关理论 3. 实践技能'\n    }\n\n    return {\n      content: mockContent,\n      usage: {\n        prompt_tokens: userMessage.length,\n        completion_tokens: mockContent.length,\n        total_tokens: userMessage.length + mockContent.length\n      },\n      model: this.config.model,\n      finish_reason: 'stop'\n    }\n  }\n\n  /**\n   * 获取模拟流响应（开发模式）\n   */\n  protected async* getMockStreamResponse(options: LLMRequestOptions): AsyncGenerator<LLMStreamResponse, void, unknown> {\n    const response = this.getMockResponse(options)\n    const words = response.content.split('')\n    \n    for (let i = 0; i < words.length; i++) {\n      await new Promise(resolve => setTimeout(resolve, 50)) // 模拟流式延迟\n      \n      yield {\n        content: words[i],\n        done: false\n      }\n    }\n\n    yield {\n      content: '',\n      done: true,\n      usage: response.usage\n    }\n  }\n}\n\n/**\n * 全局LLM服务实例\n */\nexport const llmService = new BaseLLMService()\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAaO,MAAM;IACD,OAAiB;IAE3B,YAAY,MAAkB,CAAE;QAC9B,IAAI,CAAC,MAAM,GAAG,UAAU,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD;IAC5C;IAEA;;GAEC,GACD,MAAM,KAAK,OAA0B,EAAwB;QAC3D,iBAAiB;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,gBAAgB;YACzC,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;YACxC,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,CAAC,WAAW,CAAC;QACzB;IACF;IAEA;;GAEC,GACD,OAAO,WAAW,OAA0B,EAAoD;QAC9F,kBAAkB;QAClB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,gBAAgB;YACzC,OAAO,IAAI,CAAC,qBAAqB,CAAC;YAClC;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAC9C,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,CAAC,WAAW,CAAC;QACzB;IACF;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,gBAAgB;YACzC,OAAO,KAAK,WAAW;;QACzB;QAEA,IAAI;YACF,MAAM,eAAe,MAAM,IAAI,CAAC,IAAI,CAAC;gBACnC,UAAU;oBAAC;wBAAE,MAAM;wBAAQ,SAAS;oBAAQ;iBAAE;gBAC9C,WAAW;YACb;YACA,OAAO,CAAC,CAAC,aAAa,OAAO;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,aAAa;YAC1B,OAAO;QACT;IACF;IAEA;;GAEC,GACD,qBAA+B;QAC7B,MAAM,WAAW,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpD,OAAO,WAAW,SAAS,eAAe,GAAG,EAAE;IACjD;IAEA;;GAEC,GACD,kBAA0B;QACxB,MAAM,WAAW,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpD,OAAO,WAAW,SAAS,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ;IACxD;IAEA;;GAEC,GACD,MAAgB,YAAY,OAA0B,EAAqB;QACzE,MAAM,WAAW,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,SAAS,OAAO,CAAC,iBAAiB,CAAC;QAEzE,MAAM,cAAc;YAClB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,UAAU,QAAQ,QAAQ;YAC1B,YAAY,QAAQ,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;YACtD,aAAa,QAAQ,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;YAC3D,QAAQ;YACR,GAAI,QAAQ,IAAI,IAAI;gBAAE,MAAM,QAAQ,IAAI;YAAC,CAAC;YAC1C,GAAI,QAAQ,IAAI,IAAI;gBAAE,OAAO,QAAQ,IAAI;YAAC,CAAC;YAC3C,GAAI,QAAQ,gBAAgB,IAAI;gBAAE,mBAAmB,QAAQ,gBAAgB;YAAC,CAAC;YAC/E,GAAI,QAAQ,eAAe,IAAI;gBAAE,kBAAkB,QAAQ,eAAe;YAAC,CAAC;QAC9E;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC/C,GAAG,SAAS,OAAO;YACrB;YACA,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI;QACrD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAgB,kBAAkB,OAA0B,EAAqB;QAC/E,MAAM,WAAW,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,SAAS,OAAO,CAAC,iBAAiB,CAAC;QAEzE,MAAM,cAAc;YAClB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,UAAU,QAAQ,QAAQ;YAC1B,YAAY,QAAQ,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;YACtD,aAAa,QAAQ,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;YAC3D,QAAQ;YACR,GAAI,QAAQ,IAAI,IAAI;gBAAE,MAAM,QAAQ,IAAI;YAAC,CAAC;YAC1C,GAAI,QAAQ,IAAI,IAAI;gBAAE,OAAO,QAAQ,IAAI;YAAC,CAAC;YAC3C,GAAI,QAAQ,gBAAgB,IAAI;gBAAE,mBAAmB,QAAQ,gBAAgB;YAAC,CAAC;YAC/E,GAAI,QAAQ,eAAe,IAAI;gBAAE,kBAAkB,QAAQ,eAAe;YAAC,CAAC;QAC9E;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC/C,GAAG,SAAS,OAAO;YACrB;YACA,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI;QACrD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAgB,cAAc,QAAkB,EAAwB;QACtE,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL,SAAS,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;YAC9C,OAAO,KAAK,KAAK;YACjB,OAAO,KAAK,KAAK;YACjB,eAAe,KAAK,OAAO,CAAC,EAAE,EAAE;QAClC;IACF;IAEA;;GAEC,GACD,OAAiB,oBAAoB,QAAkB,EAAoD;QACzG,MAAM,SAAS,SAAS,IAAI,EAAE;QAC9B,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,UAAU,IAAI;QACpB,IAAI,SAAS;QAEb,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,UAAU,QAAQ,MAAM,CAAC,OAAO;oBAAE,QAAQ;gBAAK;gBAC/C,MAAM,QAAQ,OAAO,KAAK,CAAC;gBAC3B,SAAS,MAAM,GAAG,MAAM;gBAExB,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;wBACxB,IAAI,SAAS,UAAU;4BACrB;wBACF;wBAEA,IAAI;4BACF,MAAM,SAAS,KAAK,KAAK,CAAC;4BAC1B,MAAM,UAAU,OAAO,OAAO,CAAC,EAAE,EAAE,OAAO,WAAW;4BAErD,MAAM;gCACJ;gCACA,MAAM;gCACN,OAAO,OAAO,KAAK;4BACrB;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,IAAI,CAAC,YAAY;wBAC3B;oBACF;gBACF;YACF;QACF,SAAU;YACR,OAAO,WAAW;QACpB;QAEA,MAAM;YACJ,SAAS;YACT,MAAM;QACR;IACF;IAEA;;GAEC,GACD,AAAU,YAAY,KAAU,EAAY;QAC1C,IAAI,MAAM,IAAI,KAAK,cAAc;YAC/B,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;QACF;QAEA,IAAI,MAAM,OAAO,EAAE,SAAS,QAAQ;YAClC,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;QACF;QAEA,IAAI,MAAM,OAAO,EAAE,SAAS,QAAQ;YAClC,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS,MAAM,OAAO,IAAI;YAC1B,MAAM;YACN,SAAS;QACX;IACF;IAEA;;GAEC,GACD,AAAU,gBAAgB,OAA0B,EAAe;QACjE,MAAM,cAAc,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,WAAW;QAE9E,IAAI,cAAc;QAElB,IAAI,YAAY,QAAQ,CAAC,SAAS,YAAY,QAAQ,CAAC,OAAO;YAC5D,cAAc;QAChB,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS,YAAY,QAAQ,CAAC,OAAO;YACnE,cAAc;QAChB;QAEA,OAAO;YACL,SAAS;YACT,OAAO;gBACL,eAAe,YAAY,MAAM;gBACjC,mBAAmB,YAAY,MAAM;gBACrC,cAAc,YAAY,MAAM,GAAG,YAAY,MAAM;YACvD;YACA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,OAAiB,sBAAsB,OAA0B,EAAoD;QACnH,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QACtC,MAAM,QAAQ,SAAS,OAAO,CAAC,KAAK,CAAC;QAErC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,MAAK,SAAS;YAE/D,MAAM;gBACJ,SAAS,KAAK,CAAC,EAAE;gBACjB,MAAM;YACR;QACF;QAEA,MAAM;YACJ,SAAS;YACT,MAAM;YACN,OAAO,SAAS,KAAK;QACvB;IACF;AACF;AAKO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/app/api/llm/test/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { llmService } from '@/lib/llm/service'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { message } = await request.json()\n\n    if (!message) {\n      return NextResponse.json(\n        { error: '消息内容不能为空' },\n        { status: 400 }\n      )\n    }\n\n    // 测试LLM服务\n    const response = await llmService.chat({\n      messages: [\n        {\n          role: 'user',\n          content: message\n        }\n      ],\n      maxTokens: 100,\n      temperature: 0.7\n    })\n\n    // 检查服务可用性\n    const isAvailable = await llmService.isAvailable()\n    const supportedModels = llmService.getSupportedModels()\n    const providerName = llmService.getProviderName()\n\n    return NextResponse.json({\n      success: true,\n      response: response.content,\n      serviceInfo: {\n        isAvailable,\n        providerName,\n        supportedModels,\n        usage: response.usage\n      },\n      timestamp: new Date().toISOString()\n    })\n\n  } catch (error) {\n    console.error('LLM测试失败:', error)\n    \n    return NextResponse.json(\n      { \n        error: 'LLM服务测试失败',\n        details: error instanceof Error ? error.message : '未知错误'\n      },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEtC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAW,GACpB;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;YACrC,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,WAAW;YACX,aAAa;QACf;QAEA,UAAU;QACV,MAAM,cAAc,MAAM,8HAAA,CAAA,aAAU,CAAC,WAAW;QAChD,MAAM,kBAAkB,8HAAA,CAAA,aAAU,CAAC,kBAAkB;QACrD,MAAM,eAAe,8HAAA,CAAA,aAAU,CAAC,eAAe;QAE/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU,SAAS,OAAO;YAC1B,aAAa;gBACX;gBACA;gBACA;gBACA,OAAO,SAAS,KAAK;YACvB;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAE1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}