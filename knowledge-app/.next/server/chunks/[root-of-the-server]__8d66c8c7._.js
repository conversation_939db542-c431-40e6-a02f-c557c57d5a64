module.exports = {

"[project]/.next-internal/server/app/api/adaptive/start-session/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/llm/config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * LLM配置管理
 * 支持多种LLM提供商和模型的配置
 */ __turbopack_context__.s({
    "DEFAULT_LLM_CONFIG": ()=>DEFAULT_LLM_CONFIG,
    "LLM_PROVIDERS": ()=>LLM_PROVIDERS,
    "getCurrentLLMConfig": ()=>getCurrentLLMConfig,
    "getLLMConfigFromEnv": ()=>getLLMConfigFromEnv,
    "validateLLMConfig": ()=>validateLLMConfig
});
const LLM_PROVIDERS = {
    groq: {
        name: 'Groq',
        apiBase: 'https://api.groq.com/openai/v1',
        defaultModel: 'llama-3.1-70b-versatile',
        supportedModels: [
            'llama-3.1-70b-versatile',
            'llama-3.1-8b-instant',
            'mixtral-8x7b-32768',
            'gemma2-9b-it'
        ]
    },
    moonshot: {
        name: 'Moonshot AI',
        apiBase: 'https://api.moonshot.cn/v1',
        defaultModel: 'moonshot-v1-8k',
        supportedModels: [
            'moonshot-v1-8k',
            'moonshot-v1-32k',
            'moonshot-v1-128k',
            'moonshotai/kimi-k2-instruct'
        ]
    },
    openai: {
        name: 'OpenAI',
        apiBase: 'https://api.openai.com/v1',
        defaultModel: 'gpt-4o-mini',
        supportedModels: [
            'gpt-4o',
            'gpt-4o-mini',
            'gpt-4-turbo',
            'gpt-3.5-turbo'
        ]
    }
};
const DEFAULT_LLM_CONFIG = {
    provider: 'moonshot',
    apiKey: process.env.MOONSHOT_API_KEY || '',
    apiBase: LLM_PROVIDERS.moonshot.apiBase,
    model: 'moonshotai/kimi-k2-instruct',
    maxTokens: 4000,
    temperature: 0.7,
    timeout: 30000
};
function getLLMConfigFromEnv() {
    // 优先使用Groq配置（如果可用）
    if (process.env.GROQ_API_KEY) {
        return {
            provider: 'groq',
            apiKey: process.env.GROQ_API_KEY,
            apiBase: process.env.GROQ_API_BASE || LLM_PROVIDERS.groq.apiBase,
            model: process.env.GROQ_MODEL || LLM_PROVIDERS.groq.defaultModel,
            maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
            temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
            timeout: parseInt(process.env.LLM_TIMEOUT || '30000')
        };
    }
    // 使用Moonshot配置
    if (process.env.MOONSHOT_API_KEY) {
        return {
            provider: 'moonshot',
            apiKey: process.env.MOONSHOT_API_KEY,
            apiBase: process.env.MOONSHOT_API_BASE || LLM_PROVIDERS.moonshot.apiBase,
            model: process.env.MOONSHOT_MODEL || 'moonshotai/kimi-k2-instruct',
            maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
            temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
            timeout: parseInt(process.env.LLM_TIMEOUT || '30000')
        };
    }
    // 使用OpenAI配置
    if (process.env.OPENAI_API_KEY) {
        return {
            provider: 'openai',
            apiKey: process.env.OPENAI_API_KEY,
            apiBase: process.env.OPENAI_API_BASE || LLM_PROVIDERS.openai.apiBase,
            model: process.env.OPENAI_MODEL || LLM_PROVIDERS.openai.defaultModel,
            maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '4000'),
            temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
            timeout: parseInt(process.env.LLM_TIMEOUT || '30000')
        };
    }
    // 返回默认配置（开发模式）
    return {
        ...DEFAULT_LLM_CONFIG,
        apiKey: 'mock-api-key' // 开发模式下的模拟key
    };
}
function validateLLMConfig(config) {
    if (!config.apiKey || config.apiKey === 'mock-api-key') {
        console.warn('LLM API Key未配置，将使用模拟模式');
        return false;
    }
    if (!config.model) {
        console.error('LLM模型未指定');
        return false;
    }
    const provider = LLM_PROVIDERS[config.provider];
    if (!provider) {
        console.error(`不支持的LLM提供商: ${config.provider}`);
        return false;
    }
    if (!provider.supportedModels.includes(config.model)) {
        console.warn(`模型 ${config.model} 可能不被 ${provider.name} 支持`);
    }
    return true;
}
function getCurrentLLMConfig() {
    const config = getLLMConfigFromEnv();
    if (!validateLLMConfig(config)) {
        console.log('使用模拟LLM配置进行开发');
    }
    return config;
}
}),
"[project]/src/lib/llm/service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * LLM服务实现
 * 支持多种LLM提供商的统一接口
 */ __turbopack_context__.s({
    "BaseLLMService": ()=>BaseLLMService,
    "llmService": ()=>llmService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/config.ts [app-route] (ecmascript)");
;
class BaseLLMService {
    config;
    constructor(config){
        this.config = config || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCurrentLLMConfig"])();
    }
    /**
   * 发送聊天请求
   */ async chat(options) {
        // 如果是模拟模式，返回模拟响应
        if (this.config.apiKey === 'mock-api-key') {
            return this.getMockResponse(options);
        }
        try {
            const response = await this.makeRequest(options);
            return this.parseResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    }
    /**
   * 发送流式聊天请求
   */ async *chatStream(options) {
        // 如果是模拟模式，返回模拟流响应
        if (this.config.apiKey === 'mock-api-key') {
            yield* this.getMockStreamResponse(options);
            return;
        }
        try {
            const response = await this.makeStreamRequest(options);
            yield* this.parseStreamResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    }
    /**
   * 检查服务是否可用
   */ async isAvailable() {
        if (this.config.apiKey === 'mock-api-key') {
            return true // 模拟模式总是可用
            ;
        }
        try {
            const testResponse = await this.chat({
                messages: [
                    {
                        role: 'user',
                        content: 'Hello'
                    }
                ],
                maxTokens: 10
            });
            return !!testResponse.content;
        } catch (error) {
            console.warn('LLM服务不可用:', error);
            return false;
        }
    }
    /**
   * 获取支持的模型列表
   */ getSupportedModels() {
        const provider = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LLM_PROVIDERS"][this.config.provider];
        return provider ? provider.supportedModels : [];
    }
    /**
   * 获取服务提供商名称
   */ getProviderName() {
        const provider = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LLM_PROVIDERS"][this.config.provider];
        return provider ? provider.name : this.config.provider;
    }
    /**
   * 发送HTTP请求到LLM API
   */ async makeRequest(options) {
        const provider = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LLM_PROVIDERS"][this.config.provider];
        const url = `${this.config.apiBase || provider.apiBase}/chat/completions`;
        const requestBody = {
            model: this.config.model,
            messages: options.messages,
            max_tokens: options.maxTokens || this.config.maxTokens,
            temperature: options.temperature ?? this.config.temperature,
            stream: false,
            ...options.stop && {
                stop: options.stop
            },
            ...options.topP && {
                top_p: options.topP
            },
            ...options.frequencyPenalty && {
                frequency_penalty: options.frequencyPenalty
            },
            ...options.presencePenalty && {
                presence_penalty: options.presencePenalty
            }
        };
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.config.apiKey}`,
                ...provider.headers
            },
            body: JSON.stringify(requestBody),
            signal: AbortSignal.timeout(this.config.timeout || 30000)
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response;
    }
    /**
   * 发送流式HTTP请求到LLM API
   */ async makeStreamRequest(options) {
        const provider = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LLM_PROVIDERS"][this.config.provider];
        const url = `${this.config.apiBase || provider.apiBase}/chat/completions`;
        const requestBody = {
            model: this.config.model,
            messages: options.messages,
            max_tokens: options.maxTokens || this.config.maxTokens,
            temperature: options.temperature ?? this.config.temperature,
            stream: true,
            ...options.stop && {
                stop: options.stop
            },
            ...options.topP && {
                top_p: options.topP
            },
            ...options.frequencyPenalty && {
                frequency_penalty: options.frequencyPenalty
            },
            ...options.presencePenalty && {
                presence_penalty: options.presencePenalty
            }
        };
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.config.apiKey}`,
                ...provider.headers
            },
            body: JSON.stringify(requestBody),
            signal: AbortSignal.timeout(this.config.timeout || 30000)
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response;
    }
    /**
   * 解析LLM响应
   */ async parseResponse(response) {
        const data = await response.json();
        return {
            content: data.choices[0]?.message?.content || '',
            usage: data.usage,
            model: data.model,
            finish_reason: data.choices[0]?.finish_reason
        };
    }
    /**
   * 解析流式LLM响应
   */ async *parseStreamResponse(response) {
        const reader = response.body?.getReader();
        if (!reader) {
            throw new Error('无法读取响应流');
        }
        const decoder = new TextDecoder();
        let buffer = '';
        try {
            while(true){
                const { done, value } = await reader.read();
                if (done) break;
                buffer += decoder.decode(value, {
                    stream: true
                });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';
                for (const line of lines){
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') {
                            return;
                        }
                        try {
                            const parsed = JSON.parse(data);
                            const content = parsed.choices[0]?.delta?.content || '';
                            yield {
                                content,
                                done: false,
                                usage: parsed.usage
                            };
                        } catch (error) {
                            console.warn('解析流数据失败:', error);
                        }
                    }
                }
            }
        } finally{
            reader.releaseLock();
        }
        yield {
            content: '',
            done: true
        };
    }
    /**
   * 处理错误
   */ handleError(error) {
        if (error.name === 'AbortError') {
            return {
                code: 'timeout',
                message: 'LLM请求超时',
                type: 'network_error',
                details: error
            };
        }
        if (error.message?.includes('401')) {
            return {
                code: 'unauthorized',
                message: 'API密钥无效或已过期',
                type: 'authentication_error',
                details: error
            };
        }
        if (error.message?.includes('429')) {
            return {
                code: 'rate_limit',
                message: 'API调用频率超限',
                type: 'rate_limit',
                details: error
            };
        }
        return {
            code: 'unknown',
            message: error.message || 'LLM服务错误',
            type: 'api_error',
            details: error
        };
    }
    /**
   * 获取模拟响应（开发模式）
   */ getMockResponse(options) {
        const userMessage = options.messages.find((m)=>m.role === 'user')?.content || '';
        let mockContent = '这是一个模拟的LLM响应。';
        if (userMessage.includes('知识') || userMessage.includes('学习')) {
            mockContent = '基于您的请求，我建议从基础概念开始学习，然后逐步深入到高级主题。';
        } else if (userMessage.includes('依赖') || userMessage.includes('前置')) {
            mockContent = '分析您提到的概念，需要先掌握以下前置知识：1. 基础概念 2. 相关理论 3. 实践技能';
        }
        return {
            content: mockContent,
            usage: {
                prompt_tokens: userMessage.length,
                completion_tokens: mockContent.length,
                total_tokens: userMessage.length + mockContent.length
            },
            model: this.config.model,
            finish_reason: 'stop'
        };
    }
    /**
   * 获取模拟流响应（开发模式）
   */ async *getMockStreamResponse(options) {
        const response = this.getMockResponse(options);
        const words = response.content.split('');
        for(let i = 0; i < words.length; i++){
            await new Promise((resolve)=>setTimeout(resolve, 50)); // 模拟流式延迟
            yield {
                content: words[i],
                done: false
            };
        }
        yield {
            content: '',
            done: true,
            usage: response.usage
        };
    }
}
const llmService = new BaseLLMService();
}),
"[project]/src/lib/llm/knowledgeGenerator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 智能知识生成服务
 * 基于用户兴趣和需求，使用LLM生成个性化学习内容
 */ __turbopack_context__.s({
    "KnowledgeGeneratorService": ()=>KnowledgeGeneratorService,
    "knowledgeGenerator": ()=>knowledgeGenerator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/service.ts [app-route] (ecmascript)");
;
class KnowledgeGeneratorService {
    /**
   * 生成知识内容
   */ async generateKnowledge(request) {
        const prompt = this.buildKnowledgeGenerationPrompt(request);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
                messages: [
                    {
                        role: 'system',
                        content: this.getSystemPrompt()
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.7,
                maxTokens: 3000
            });
            return this.parseKnowledgeResponse(response.content, request);
        } catch (error) {
            console.error('知识生成失败:', error);
            throw new Error('无法生成知识内容，请稍后重试');
        }
    }
    /**
   * 批量生成相关知识点
   */ async generateRelatedKnowledge(baseTopic, count = 5) {
        const prompt = `请为主题"${baseTopic}"生成${count}个相关的知识点，每个知识点应该：
1. 与主题相关但各有侧重
2. 难度递进或并列
3. 包含实用的学习内容

请以JSON数组格式返回，每个知识点包含：title, description, difficulty_level, estimated_time, prerequisites, tags`;
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
                messages: [
                    {
                        role: 'system',
                        content: this.getSystemPrompt()
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.8,
                maxTokens: 4000
            });
            return this.parseMultipleKnowledgeResponse(response.content);
        } catch (error) {
            console.error('批量知识生成失败:', error);
            throw new Error('无法生成相关知识点，请稍后重试');
        }
    }
    /**
   * 根据用户兴趣推荐学习主题
   */ async recommendTopics(userInterests, currentLevel = 'beginner') {
        const prompt = `基于用户兴趣：${userInterests.join(', ')}，当前水平：${currentLevel}
请推荐10个适合的学习主题，要求：
1. 与用户兴趣相关
2. 适合当前水平
3. 有实际应用价值
4. 学习路径清晰

请以简洁的主题名称列表返回，每行一个主题。`;
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的学习顾问，擅长根据用户兴趣推荐合适的学习主题。'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.6,
                maxTokens: 1000
            });
            return this.parseTopicRecommendations(response.content);
        } catch (error) {
            console.error('主题推荐失败:', error);
            return [] // 返回空数组而不是抛出错误
            ;
        }
    }
    /**
   * 构建知识生成提示词
   */ buildKnowledgeGenerationPrompt(request) {
        const { topic, userLevel = 'beginner', context, preferences } = request;
        let prompt = `请为主题"${topic}"生成详细的学习内容。

用户水平：${userLevel}
${context ? `背景信息：${context}` : ''}

要求：
1. 内容要准确、实用、易懂
2. 适合${userLevel}水平的学习者
3. 包含清晰的概念解释和实例
4. 估算学习时间和难度等级（1-10）
5. 列出必要的前置知识
6. 添加相关标签

${preferences?.includeExamples ? '请包含具体的代码示例或实际案例。' : ''}
${preferences?.includeExercises ? '请包含练习题或实践任务。' : ''}
${preferences?.focusAreas?.length ? `重点关注：${preferences.focusAreas.join(', ')}` : ''}

请以结构化的格式返回，包含：
- 标题
- 描述
- 详细内容
- 难度等级（1-10）
- 估计学习时间（分钟）
- 前置知识列表
- 标签列表
${preferences?.includeExamples ? '- 示例列表' : ''}
${preferences?.includeExercises ? '- 练习列表' : ''}`;
        return prompt;
    }
    /**
   * 获取系统提示词
   */ getSystemPrompt() {
        return `你是一个专业的教育内容生成专家，擅长创建高质量的学习材料。你的任务是：

1. 生成准确、实用的学习内容
2. 根据用户水平调整内容难度
3. 提供清晰的学习路径和时间估算
4. 确保内容的逻辑性和连贯性
5. 包含实际应用和案例

请始终以学习者的角度思考，确保内容易于理解和实践。`;
    }
    /**
   * 解析知识生成响应
   */ parseKnowledgeResponse(content, request) {
        try {
            // 尝试解析JSON格式的响应
            if (content.includes('{') && content.includes('}')) {
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const parsed = JSON.parse(jsonMatch[0]);
                    return this.normalizeKnowledgeObject(parsed);
                }
            }
            // 如果不是JSON格式，则解析文本格式
            return this.parseTextKnowledgeResponse(content, request);
        } catch (error) {
            console.warn('解析知识响应失败，使用文本解析:', error);
            return this.parseTextKnowledgeResponse(content, request);
        }
    }
    /**
   * 解析文本格式的知识响应
   */ parseTextKnowledgeResponse(content, request) {
        const lines = content.split('\n').filter((line)=>line.trim());
        return {
            title: request.topic,
            description: `关于${request.topic}的学习内容`,
            content: content,
            difficulty_level: this.extractDifficultyLevel(content),
            estimated_time: this.extractEstimatedTime(content),
            prerequisites: this.extractPrerequisites(content),
            tags: this.extractTags(content, request.topic),
            examples: request.preferences?.includeExamples ? this.extractExamples(content) : undefined,
            exercises: request.preferences?.includeExercises ? this.extractExercises(content) : undefined
        };
    }
    /**
   * 解析多个知识点响应
   */ parseMultipleKnowledgeResponse(content) {
        try {
            // 尝试解析JSON数组
            const jsonMatch = content.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return parsed.map((item)=>this.normalizeKnowledgeObject(item));
            }
            // 如果不是JSON格式，返回空数组
            return [];
        } catch (error) {
            console.warn('解析多个知识点响应失败:', error);
            return [];
        }
    }
    /**
   * 解析主题推荐
   */ parseTopicRecommendations(content) {
        return content.split('\n').map((line)=>line.trim()).filter((line)=>line && !line.startsWith('#')).map((line)=>line.replace(/^\d+\.\s*/, '').replace(/^-\s*/, '')).slice(0, 10) // 限制最多10个主题
        ;
    }
    /**
   * 标准化知识对象
   */ normalizeKnowledgeObject(obj) {
        return {
            title: obj.title || obj.name || '未命名知识点',
            description: obj.description || obj.desc || '',
            content: obj.content || obj.details || obj.description || '',
            difficulty_level: parseInt(obj.difficulty_level || obj.difficulty || '5'),
            estimated_time: parseInt(obj.estimated_time || obj.time || '30'),
            prerequisites: Array.isArray(obj.prerequisites) ? obj.prerequisites : [],
            tags: Array.isArray(obj.tags) ? obj.tags : [],
            examples: Array.isArray(obj.examples) ? obj.examples : undefined,
            exercises: Array.isArray(obj.exercises) ? obj.exercises : undefined
        };
    }
    /**
   * 提取难度等级
   */ extractDifficultyLevel(content) {
        const match = content.match(/难度[：:]\s*(\d+)/i) || content.match(/difficulty[：:]\s*(\d+)/i);
        return match ? parseInt(match[1]) : 5;
    }
    /**
   * 提取估计时间
   */ extractEstimatedTime(content) {
        const match = content.match(/时间[：:]\s*(\d+)/i) || content.match(/time[：:]\s*(\d+)/i) || content.match(/(\d+)\s*分钟/i);
        return match ? parseInt(match[1]) : 30;
    }
    /**
   * 提取前置知识
   */ extractPrerequisites(content) {
        const section = this.extractSection(content, [
            '前置知识',
            'prerequisites',
            '先决条件'
        ]);
        if (!section) return [];
        return section.split(/[,，\n]/).map((item)=>item.trim()).filter((item)=>item && item.length > 1);
    }
    /**
   * 提取标签
   */ extractTags(content, topic) {
        const section = this.extractSection(content, [
            '标签',
            'tags',
            '关键词'
        ]);
        const tags = section ? section.split(/[,，\n]/).map((item)=>item.trim()).filter((item)=>item) : [
            topic
        ];
        return [
            ...new Set(tags)
        ] // 去重
        ;
    }
    /**
   * 提取示例
   */ extractExamples(content) {
        const section = this.extractSection(content, [
            '示例',
            'examples',
            '案例'
        ]);
        if (!section) return [];
        return section.split(/\n\n/).map((item)=>item.trim()).filter((item)=>item && item.length > 10);
    }
    /**
   * 提取练习
   */ extractExercises(content) {
        const section = this.extractSection(content, [
            '练习',
            'exercises',
            '任务'
        ]);
        if (!section) return [];
        return section.split(/\n\n/).map((item)=>item.trim()).filter((item)=>item && item.length > 5);
    }
    /**
   * 提取内容段落
   */ extractSection(content, keywords) {
        for (const keyword of keywords){
            const regex = new RegExp(`${keyword}[：:]([\\s\\S]*?)(?=\\n\\n|\\n[^\\s]|$)`, 'i');
            const match = content.match(regex);
            if (match) {
                return match[1].trim();
            }
        }
        return null;
    }
}
const knowledgeGenerator = new KnowledgeGeneratorService();
}),
"[project]/src/lib/llm/dependencyAnalyzer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 依赖知识分析服务
 * 当用户遇到不懂的概念时，分析并识别需要的前置知识
 */ __turbopack_context__.s({
    "DependencyAnalyzerService": ()=>DependencyAnalyzerService,
    "dependencyAnalyzer": ()=>dependencyAnalyzer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/service.ts [app-route] (ecmascript)");
;
class DependencyAnalyzerService {
    /**
   * 分析知识依赖
   */ async analyzeDependencies(request) {
        const prompt = this.buildDependencyAnalysisPrompt(request);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
                messages: [
                    {
                        role: 'system',
                        content: this.getSystemPrompt()
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                maxTokens: 3000
            });
            return this.parseDependencyResponse(response.content, request);
        } catch (error) {
            console.error('依赖分析失败:', error);
            throw new Error('无法分析知识依赖，请稍后重试');
        }
    }
    /**
   * 快速检查概念依赖
   */ async quickDependencyCheck(currentTopic, unknownConcept) {
        const prompt = `用户正在学习"${currentTopic}"，但不理解"${unknownConcept}"这个概念。

请分析学习"${unknownConcept}"需要的前置知识，要求：
1. 列出3-5个最重要的前置概念
2. 按重要性和学习顺序排序
3. 估算每个概念的学习时间和难度
4. 提供简短的概念描述

请以JSON数组格式返回，每个依赖包含：concept, importance(1-10), difficulty(1-10), description, estimatedTime(分钟)`;
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的学习顾问，擅长分析知识结构和学习依赖关系。'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.2,
                maxTokens: 2000
            });
            return this.parseQuickDependencyResponse(response.content);
        } catch (error) {
            console.error('快速依赖检查失败:', error);
            return [];
        }
    }
    /**
   * 评估学习准备度
   */ async assessLearningReadiness(targetTopic, currentKnowledge, userLevel = 'beginner') {
        const prompt = `评估用户学习"${targetTopic}"的准备度：

用户当前掌握的知识：
${currentKnowledge.map((k)=>`- ${k}`).join('\n')}

用户水平：${userLevel}

请分析：
1. 学习准备度（0-100分）
2. 缺失的关键概念
3. 学习建议
4. 预计准备时间（分钟）

请以JSON格式返回：{readiness, missingConcepts, recommendations, estimatedPrepTime}`;
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的学习评估专家，能够准确评估学习者的知识准备度。'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.2,
                maxTokens: 1500
            });
            return this.parseReadinessResponse(response.content);
        } catch (error) {
            console.error('学习准备度评估失败:', error);
            return {
                readiness: 50,
                missingConcepts: [],
                recommendations: [
                    '建议先掌握基础概念'
                ],
                estimatedPrepTime: 60
            };
        }
    }
    /**
   * 构建依赖分析提示词
   */ buildDependencyAnalysisPrompt(request) {
        const { currentTopic, unknownConcepts, userContext, currentLevel } = request;
        let prompt = `用户正在学习"${currentTopic}"，但遇到了以下不理解的概念：
${unknownConcepts.map((concept)=>`- ${concept}`).join('\n')}

${userContext ? `用户背景：${userContext}` : ''}
${currentLevel ? `当前水平：${currentLevel}` : ''}

请进行详细的依赖分析：

1. 对每个不理解的概念，分析其前置知识要求
2. 识别概念之间的依赖关系
3. 按学习优先级排序
4. 估算学习时间和难度
5. 提供学习建议和资源

要求：
- 分析要准确、实用
- 考虑概念的重要性和学习顺序
- 提供具体的学习路径
- 估算合理的时间投入

请以结构化格式返回分析结果。`;
        return prompt;
    }
    /**
   * 获取系统提示词
   */ getSystemPrompt() {
        return `你是一个专业的知识结构分析专家，具有以下能力：

1. 深度理解各学科的知识体系和依赖关系
2. 准确识别学习某个概念所需的前置知识
3. 合理评估学习难度和时间投入
4. 提供实用的学习路径和建议

你的分析应该：
- 基于教育学和认知科学原理
- 考虑学习者的认知负荷
- 提供循序渐进的学习路径
- 注重实际应用和理解深度

请始终以学习者的最佳利益为出发点，提供准确、实用的分析结果。`;
    }
    /**
   * 解析依赖分析响应
   */ parseDependencyResponse(content, request) {
        try {
            // 尝试解析JSON格式
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return this.normalizeDependencyResponse(parsed);
            }
            // 解析文本格式
            return this.parseTextDependencyResponse(content, request);
        } catch (error) {
            console.warn('解析依赖分析响应失败，使用文本解析:', error);
            return this.parseTextDependencyResponse(content, request);
        }
    }
    /**
   * 解析文本格式的依赖分析响应
   */ parseTextDependencyResponse(content, request) {
        const dependencies = [];
        // 为每个未知概念创建基础依赖项
        request.unknownConcepts.forEach((concept, index)=>{
            dependencies.push({
                concept,
                importance: 8 - index,
                difficulty: 5,
                description: `学习${concept}的相关知识`,
                estimatedTime: 30,
                learningPath: [
                    `理解${concept}的基本概念`,
                    `掌握${concept}的应用`
                ],
                resources: [
                    `${concept}相关教程`,
                    `${concept}实践案例`
                ]
            });
        });
        return {
            dependencies,
            recommendedOrder: request.unknownConcepts,
            totalEstimatedTime: dependencies.reduce((sum, dep)=>sum + (dep.estimatedTime || 0), 0),
            explanation: '基于文本分析生成的学习建议，建议按照概念的基础程度逐步学习。'
        };
    }
    /**
   * 解析快速依赖检查响应
   */ parseQuickDependencyResponse(content) {
        try {
            const jsonMatch = content.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return parsed.map((item)=>this.normalizeAnalyzedDependency(item));
            }
        } catch (error) {
            console.warn('解析快速依赖检查响应失败:', error);
        }
        return [];
    }
    /**
   * 解析学习准备度响应
   */ parseReadinessResponse(content) {
        try {
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return {
                    readiness: parseInt(parsed.readiness) || 50,
                    missingConcepts: Array.isArray(parsed.missingConcepts) ? parsed.missingConcepts : [],
                    recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations : [],
                    estimatedPrepTime: parseInt(parsed.estimatedPrepTime) || 60
                };
            }
        } catch (error) {
            console.warn('解析学习准备度响应失败:', error);
        }
        return {
            readiness: 50,
            missingConcepts: [],
            recommendations: [
                '建议先掌握基础概念'
            ],
            estimatedPrepTime: 60
        };
    }
    /**
   * 标准化依赖分析响应
   */ normalizeDependencyResponse(obj) {
        return {
            dependencies: Array.isArray(obj.dependencies) ? obj.dependencies.map((dep)=>this.normalizeAnalyzedDependency(dep)) : [],
            recommendedOrder: Array.isArray(obj.recommendedOrder) ? obj.recommendedOrder : [],
            totalEstimatedTime: parseInt(obj.totalEstimatedTime) || 0,
            explanation: obj.explanation || '依赖分析完成'
        };
    }
    /**
   * 标准化分析依赖对象
   */ normalizeAnalyzedDependency(obj) {
        return {
            concept: obj.concept || obj.name || '未知概念',
            importance: parseInt(obj.importance) || 5,
            difficulty: parseInt(obj.difficulty) || 5,
            description: obj.description || obj.desc || '',
            estimatedTime: parseInt(obj.estimatedTime || obj.time) || 30,
            learningPath: Array.isArray(obj.learningPath) ? obj.learningPath : [],
            resources: Array.isArray(obj.resources) ? obj.resources : []
        };
    }
}
const dependencyAnalyzer = new DependencyAnalyzerService();
}),
"[project]/src/lib/llm/pathPlanner.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 学习路径规划服务
 * 基于知识依赖分析，智能规划最优的学习路径和顺序
 */ __turbopack_context__.s({
    "PathPlannerService": ()=>PathPlannerService,
    "pathPlanner": ()=>pathPlanner
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/service.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$dependencyAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/dependencyAnalyzer.ts [app-route] (ecmascript)");
;
;
class PathPlannerService {
    /**
   * 生成完整的学习路径
   */ async generateLearningPath(request) {
        try {
            // 1. 分析知识依赖
            const dependencyAnalysis = await this.analyzeLearningDependencies(request);
            // 2. 生成学习步骤
            const steps = await this.generateLearningSteps(request, dependencyAnalysis);
            // 3. 优化学习路径
            const optimizedSteps = this.optimizeLearningPath(steps, request);
            // 4. 生成里程碑
            const milestones = this.generateMilestones(optimizedSteps);
            return {
                id: this.generatePathId(),
                title: `${request.targetTopic} 学习路径`,
                description: this.generatePathDescription(request),
                totalTime: optimizedSteps.reduce((sum, step)=>sum + step.estimatedTime, 0),
                difficulty: this.calculateOverallDifficulty(optimizedSteps),
                steps: optimizedSteps,
                milestones
            };
        } catch (error) {
            console.error('学习路径生成失败:', error);
            throw new Error('无法生成学习路径，请稍后重试');
        }
    }
    /**
   * 生成自适应学习路径
   * 根据用户的学习进度动态调整
   */ async generateAdaptivePath(targetTopic, currentProgress, learningHistory, timeConstraints) {
        const prompt = this.buildAdaptivePathPrompt(targetTopic, currentProgress, learningHistory, timeConstraints);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
                messages: [
                    {
                        role: 'system',
                        content: this.getAdaptiveSystemPrompt()
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.4,
                maxTokens: 4000
            });
            return this.parseAdaptivePathResponse(response.content, targetTopic);
        } catch (error) {
            console.error('自适应路径生成失败:', error);
            throw new Error('无法生成自适应学习路径');
        }
    }
    /**
   * 优化现有学习路径
   */ async optimizeExistingPath(currentPath, userFeedback) {
        const prompt = `基于用户反馈优化学习路径：

当前路径：${currentPath.title}
已完成步骤：${userFeedback.completedSteps.join(', ')}
困难概念：${userFeedback.strugglingConcepts.join(', ')}

请优化路径，调整：
1. 困难概念的学习方法
2. 步骤的顺序和时间分配
3. 添加必要的复习和练习
4. 提供替代学习方案

请以JSON格式返回优化后的学习路径。`;
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的学习路径优化专家，能够根据用户反馈调整学习计划。'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                maxTokens: 3000
            });
            return this.parseOptimizedPathResponse(response.content, currentPath);
        } catch (error) {
            console.error('路径优化失败:', error);
            return currentPath // 返回原路径
            ;
        }
    }
    /**
   * 分析学习依赖关系
   */ async analyzeLearningDependencies(request) {
        const dependencyRequest = {
            currentTopic: request.targetTopic,
            unknownConcepts: request.missingKnowledge,
            userContext: `当前掌握：${request.currentKnowledge.join(', ')}`,
            currentLevel: this.inferUserLevel(request)
        };
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$dependencyAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dependencyAnalyzer"].analyzeDependencies(dependencyRequest);
    }
    /**
   * 生成学习步骤
   */ async generateLearningSteps(request, dependencyAnalysis) {
        const prompt = `基于依赖分析结果，为"${request.targetTopic}"生成详细的学习步骤：

目标：${request.targetTopic}
当前知识：${request.currentKnowledge.join(', ')}
缺失知识：${request.missingKnowledge.join(', ')}
依赖分析：${JSON.stringify(dependencyAnalysis, null, 2)}

要求：
1. 创建循序渐进的学习步骤
2. 每个步骤包含明确的学习目标
3. 估算合理的学习时间
4. 标注步骤类型（概念/练习/项目/复习）
5. 定义前置要求

请以JSON数组格式返回学习步骤。`;
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的课程设计专家，擅长创建结构化的学习步骤。'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.5,
                maxTokens: 3000
            });
            return this.parseLearningStepsResponse(response.content);
        } catch (error) {
            console.error('学习步骤生成失败:', error);
            return this.generateDefaultSteps(request);
        }
    }
    /**
   * 优化学习路径
   */ optimizeLearningPath(steps, request) {
        // 根据时间约束调整
        if (request.timeConstraints?.totalTime) {
            steps = this.adjustForTimeConstraints(steps, request.timeConstraints.totalTime);
        }
        // 根据学习偏好调整
        if (request.preferences?.difficulty === 'gradual') {
            steps = this.sortByDifficultyGradual(steps);
        }
        // 添加项目实践
        if (request.preferences?.includeProjects) {
            steps = this.addProjectSteps(steps);
        }
        return steps;
    }
    /**
   * 生成学习里程碑
   */ generateMilestones(steps) {
        const milestones = [];
        const totalSteps = steps.length;
        // 每25%进度设置一个里程碑
        for(let i = 1; i <= 4; i++){
            const milestoneIndex = Math.floor(totalSteps * i / 4) - 1;
            if (milestoneIndex >= 0 && milestoneIndex < totalSteps) {
                milestones.push(`完成${steps[milestoneIndex].title}`);
            }
        }
        return milestones;
    }
    /**
   * 构建自适应路径提示词
   */ buildAdaptivePathPrompt(targetTopic, currentProgress, learningHistory, timeConstraints) {
        return `为用户生成自适应学习路径：

目标主题：${targetTopic}

当前进度：
${Object.entries(currentProgress).map(([concept, progress])=>`- ${concept}: ${progress}%`).join('\n')}

学习历史：${learningHistory.join(', ')}

${timeConstraints ? `时间约束：${JSON.stringify(timeConstraints)}` : ''}

请生成一个自适应的学习路径，要求：
1. 基于当前进度调整学习重点
2. 避免重复已掌握的内容
3. 强化薄弱环节
4. 提供个性化的学习建议

请以JSON格式返回完整的学习路径。`;
    }
    /**
   * 获取自适应系统提示词
   */ getAdaptiveSystemPrompt() {
        return `你是一个智能学习路径规划专家，具有以下能力：

1. 分析学习者的当前状态和进度
2. 识别知识盲点和薄弱环节
3. 设计个性化的学习路径
4. 动态调整学习策略和重点
5. 优化学习效率和效果

你的规划应该：
- 基于学习科学和认知心理学原理
- 考虑个体差异和学习偏好
- 提供可操作的具体步骤
- 包含适当的复习和巩固机制
- 设置合理的学习目标和里程碑`;
    }
    /**
   * 解析自适应路径响应
   */ parseAdaptivePathResponse(content, targetTopic) {
        try {
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return this.normalizeLearningPath(parsed, targetTopic);
            }
        } catch (error) {
            console.warn('解析自适应路径响应失败:', error);
        }
        // 返回默认路径
        return this.generateDefaultPath(targetTopic);
    }
    /**
   * 解析优化路径响应
   */ parseOptimizedPathResponse(content, originalPath) {
        try {
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return this.normalizeLearningPath(parsed, originalPath.title);
            }
        } catch (error) {
            console.warn('解析优化路径响应失败:', error);
        }
        return originalPath;
    }
    /**
   * 解析学习步骤响应
   */ parseLearningStepsResponse(content) {
        try {
            const jsonMatch = content.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return parsed.map((step)=>this.normalizeLearningStep(step));
            }
        } catch (error) {
            console.warn('解析学习步骤响应失败:', error);
        }
        return [];
    }
    /**
   * 标准化学习路径对象
   */ normalizeLearningPath(obj, title) {
        return {
            id: obj.id || this.generatePathId(),
            title: obj.title || title,
            description: obj.description || '',
            totalTime: parseInt(obj.totalTime) || 0,
            difficulty: parseInt(obj.difficulty) || 5,
            steps: Array.isArray(obj.steps) ? obj.steps.map((step)=>this.normalizeLearningStep(step)) : [],
            milestones: Array.isArray(obj.milestones) ? obj.milestones : []
        };
    }
    /**
   * 标准化学习步骤对象
   */ normalizeLearningStep(obj) {
        return {
            id: obj.id || this.generateStepId(),
            title: obj.title || obj.name || '学习步骤',
            description: obj.description || obj.desc || '',
            type: obj.type || 'concept',
            estimatedTime: parseInt(obj.estimatedTime || obj.time) || 30,
            difficulty: parseInt(obj.difficulty) || 5,
            prerequisites: Array.isArray(obj.prerequisites) ? obj.prerequisites : [],
            resources: Array.isArray(obj.resources) ? obj.resources : [],
            objectives: Array.isArray(obj.objectives) ? obj.objectives : []
        };
    }
    /**
   * 生成默认学习步骤
   */ generateDefaultSteps(request) {
        return [
            {
                id: this.generateStepId(),
                title: `${request.targetTopic} 基础概念`,
                description: `学习${request.targetTopic}的基本概念和原理`,
                type: 'concept',
                estimatedTime: 60,
                difficulty: 3,
                prerequisites: [],
                objectives: [
                    `理解${request.targetTopic}的基本概念`
                ]
            },
            {
                id: this.generateStepId(),
                title: `${request.targetTopic} 实践练习`,
                description: `通过练习巩固${request.targetTopic}的知识`,
                type: 'practice',
                estimatedTime: 90,
                difficulty: 5,
                prerequisites: [
                    `${request.targetTopic} 基础概念`
                ],
                objectives: [
                    `掌握${request.targetTopic}的实际应用`
                ]
            }
        ];
    }
    /**
   * 生成默认学习路径
   */ generateDefaultPath(targetTopic) {
        const steps = [
            {
                id: this.generateStepId(),
                title: `${targetTopic} 入门`,
                description: `${targetTopic}的基础学习`,
                type: 'concept',
                estimatedTime: 60,
                difficulty: 3,
                prerequisites: [],
                objectives: [
                    `了解${targetTopic}基础`
                ]
            }
        ];
        return {
            id: this.generatePathId(),
            title: `${targetTopic} 学习路径`,
            description: `系统学习${targetTopic}的完整路径`,
            totalTime: 60,
            difficulty: 3,
            steps,
            milestones: [
                `完成${targetTopic}入门`
            ]
        };
    }
    /**
   * 辅助方法
   */ generatePathId() {
        return `path_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generateStepId() {
        return `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generatePathDescription(request) {
        return `为学习${request.targetTopic}量身定制的学习路径，基于您当前的知识水平和学习目标。`;
    }
    calculateOverallDifficulty(steps) {
        if (steps.length === 0) return 5;
        const avgDifficulty = steps.reduce((sum, step)=>sum + step.difficulty, 0) / steps.length;
        return Math.round(avgDifficulty);
    }
    inferUserLevel(request) {
        const knowledgeCount = request.currentKnowledge.length;
        if (knowledgeCount === 0) return 'beginner';
        if (knowledgeCount < 5) return 'beginner';
        if (knowledgeCount < 15) return 'intermediate';
        return 'advanced';
    }
    adjustForTimeConstraints(steps, totalTime) {
        const currentTotal = steps.reduce((sum, step)=>sum + step.estimatedTime, 0);
        if (currentTotal <= totalTime) return steps;
        const ratio = totalTime / currentTotal;
        return steps.map((step)=>({
                ...step,
                estimatedTime: Math.round(step.estimatedTime * ratio)
            }));
    }
    sortByDifficultyGradual(steps) {
        return [
            ...steps
        ].sort((a, b)=>a.difficulty - b.difficulty);
    }
    addProjectSteps(steps) {
        // 在适当位置插入项目步骤
        const projectStep = {
            id: this.generateStepId(),
            title: '综合项目实践',
            description: '通过实际项目巩固所学知识',
            type: 'project',
            estimatedTime: 120,
            difficulty: 7,
            prerequisites: steps.slice(0, Math.floor(steps.length * 0.7)).map((s)=>s.title),
            objectives: [
                '应用所学知识解决实际问题'
            ]
        };
        const insertIndex = Math.floor(steps.length * 0.8);
        const result = [
            ...steps
        ];
        result.splice(insertIndex, 0, projectStep);
        return result;
    }
}
const pathPlanner = new PathPlannerService();
}),
"[project]/src/lib/llm/adaptiveLearning.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 自适应学习流程核心服务
 * 实现完整的学习循环：学习→遇到问题→分析依赖→规划路径→学习前置知识→回到原知识
 */ __turbopack_context__.s({
    "AdaptiveLearningService": ()=>AdaptiveLearningService,
    "adaptiveLearning": ()=>adaptiveLearning
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$knowledgeGenerator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/knowledgeGenerator.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$dependencyAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/dependencyAnalyzer.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$pathPlanner$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/pathPlanner.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/service.ts [app-route] (ecmascript)");
;
;
;
;
class AdaptiveLearningService {
    activeSessions = new Map();
    /**
   * 开始新的学习会话
   */ async startLearningSession(userId, targetTopic, userContext) {
        const sessionId = this.generateSessionId();
        // 生成初始知识内容
        const initialKnowledge = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$knowledgeGenerator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["knowledgeGenerator"].generateKnowledge({
            topic: targetTopic,
            userLevel: this.inferUserLevel(userContext?.currentKnowledge || []),
            context: userContext ? JSON.stringify(userContext) : undefined,
            preferences: {
                includeExamples: true,
                includeExercises: true
            }
        });
        // 创建学习会话
        const session = {
            id: sessionId,
            userId,
            targetTopic,
            currentStep: 'initial_learning',
            learningHistory: [
                {
                    id: this.generateRecordId(),
                    timestamp: new Date(),
                    action: 'start_topic',
                    concept: targetTopic,
                    details: {
                        initialKnowledge
                    }
                }
            ],
            knowledgeGraph: this.initializeKnowledgeGraph(userContext?.currentKnowledge || []),
            currentPath: null,
            blockedConcepts: [],
            sessionStartTime: new Date(),
            lastActivityTime: new Date(),
            status: 'active'
        };
        this.activeSessions.set(sessionId, session);
        return session;
    }
    /**
   * 处理学习困难 - 核心自适应逻辑
   */ async handleLearningDifficulty(sessionId, difficulty) {
        const session = this.activeSessions.get(sessionId);
        if (!session) {
            throw new Error('学习会话不存在');
        }
        try {
            // 1. 记录困难
            this.recordLearningDifficulty(session, difficulty);
            // 2. 分析依赖关系
            const dependencyAnalysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$dependencyAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dependencyAnalyzer"].analyzeDependencies({
                currentTopic: session.targetTopic,
                unknownConcepts: [
                    difficulty.concept
                ],
                userContext: difficulty.context,
                currentLevel: this.getCurrentUserLevel(session)
            });
            // 3. 评估是否需要学习前置知识
            const needsPrerequisites = await this.assessPrerequisiteNeed(session, difficulty.concept, dependencyAnalysis);
            if (needsPrerequisites) {
                // 4. 生成学习路径
                const learningPath = await this.generateAdaptivePath(session, dependencyAnalysis);
                // 5. 更新会话状态
                session.currentPath = learningPath;
                session.blockedConcepts.push(difficulty.concept);
                session.currentStep = 'learning_prerequisites';
                return {
                    type: 'path_suggested',
                    content: {
                        analysis: dependencyAnalysis,
                        path: learningPath,
                        message: `我发现您在"${difficulty.concept}"上遇到了困难。让我为您规划一个学习路径，先掌握必要的前置知识。`
                    },
                    nextSteps: learningPath.steps.slice(0, 3).map((step)=>step.title),
                    estimatedTime: learningPath.totalTime,
                    confidence: 0.85
                };
            } else {
                // 6. 提供直接帮助
                const helpContent = await this.generateDirectHelp(session, difficulty);
                return {
                    type: 'help_provided',
                    content: helpContent,
                    nextSteps: [
                        '继续当前学习',
                        '尝试相关练习'
                    ],
                    estimatedTime: 15,
                    confidence: 0.75
                };
            }
        } catch (error) {
            console.error('处理学习困难失败:', error);
            throw new Error('无法处理学习困难，请稍后重试');
        }
    }
    /**
   * 完成学习步骤
   */ async completeStep(sessionId, stepId, masteryLevel, timeSpent, feedback) {
        const session = this.activeSessions.get(sessionId);
        if (!session) {
            throw new Error('学习会话不存在');
        }
        // 更新掌握程度
        const step = this.findStepInPath(session.currentPath, stepId);
        if (step) {
            session.knowledgeGraph[step.title] = masteryLevel;
            // 记录学习记录
            session.learningHistory.push({
                id: this.generateRecordId(),
                timestamp: new Date(),
                action: 'complete_step',
                concept: step.title,
                details: {
                    stepId,
                    feedback
                },
                timeSpent,
                masteryAfter: masteryLevel
            });
        }
        // 检查是否可以回到原始学习目标
        const canReturnToOriginal = await this.checkReturnCondition(session);
        if (canReturnToOriginal) {
            // 移除已解决的阻塞概念
            session.blockedConcepts = session.blockedConcepts.filter((concept)=>session.knowledgeGraph[concept] < 70);
            if (session.blockedConcepts.length === 0) {
                session.currentStep = 'returning_to_original';
                return {
                    type: 'knowledge_generated',
                    content: {
                        message: `太好了！您已经掌握了必要的前置知识。现在让我们回到原始学习目标："${session.targetTopic}"`,
                        nextConcept: session.targetTopic,
                        readinessScore: this.calculateReadinessScore(session)
                    },
                    nextSteps: [
                        '回到原始学习目标',
                        '应用新掌握的知识'
                    ],
                    estimatedTime: 30,
                    confidence: 0.9
                };
            }
        }
        // 继续当前路径
        const nextStep = this.getNextStep(session.currentPath, stepId);
        return {
            type: 'path_suggested',
            content: {
                currentProgress: this.calculatePathProgress(session.currentPath, stepId),
                nextStep,
                encouragement: this.generateEncouragement(masteryLevel)
            },
            nextSteps: nextStep ? [
                nextStep.title
            ] : [
                '完成当前路径'
            ],
            estimatedTime: nextStep?.estimatedTime || 0,
            confidence: 0.8
        };
    }
    /**
   * 获取学习建议
   */ async getLearningRecommendation(sessionId) {
        const session = this.activeSessions.get(sessionId);
        if (!session) {
            throw new Error('学习会话不存在');
        }
        const prompt = `基于用户的学习会话，提供个性化的学习建议：

学习目标：${session.targetTopic}
当前步骤：${session.currentStep}
学习历史：${session.learningHistory.length}个记录
知识掌握情况：${JSON.stringify(session.knowledgeGraph)}
遇到困难的概念：${session.blockedConcepts.join(', ')}

请提供：
1. 当前学习状态评估
2. 下一步学习建议
3. 潜在的学习风险和解决方案
4. 学习效率优化建议

请以JSON格式返回建议。`;
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的学习顾问，能够基于学习数据提供个性化建议。'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.6,
                maxTokens: 2000
            });
            const recommendation = this.parseRecommendationResponse(response.content);
            return {
                type: 'help_provided',
                content: recommendation,
                nextSteps: recommendation.nextSteps || [
                    '继续当前学习'
                ],
                estimatedTime: 0,
                confidence: 0.8
            };
        } catch (error) {
            console.error('获取学习建议失败:', error);
            throw new Error('无法获取学习建议');
        }
    }
    /**
   * 暂停学习会话
   */ pauseSession(sessionId) {
        const session = this.activeSessions.get(sessionId);
        if (session) {
            session.status = 'paused';
            session.lastActivityTime = new Date();
        }
    }
    /**
   * 恢复学习会话
   */ resumeSession(sessionId) {
        const session = this.activeSessions.get(sessionId);
        if (session) {
            session.status = 'active';
            session.lastActivityTime = new Date();
            return session;
        }
        return null;
    }
    /**
   * 获取学习会话
   */ getSession(sessionId) {
        return this.activeSessions.get(sessionId) || null;
    }
    /**
   * 私有辅助方法
   */ generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generateRecordId() {
        return `record_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    inferUserLevel(currentKnowledge) {
        const count = currentKnowledge.length;
        if (count < 3) return 'beginner';
        if (count < 10) return 'intermediate';
        return 'advanced';
    }
    initializeKnowledgeGraph(currentKnowledge) {
        const graph = {};
        currentKnowledge.forEach((concept)=>{
            graph[concept] = 80; // 假设已掌握的知识为80%
        });
        return graph;
    }
    recordLearningDifficulty(session, difficulty) {
        session.learningHistory.push({
            id: this.generateRecordId(),
            timestamp: new Date(),
            action: 'encounter_difficulty',
            concept: difficulty.concept,
            details: difficulty
        });
        session.lastActivityTime = new Date();
    }
    getCurrentUserLevel(session) {
        const masteredConcepts = Object.values(session.knowledgeGraph).filter((level)=>level >= 70).length;
        if (masteredConcepts < 3) return 'beginner';
        if (masteredConcepts < 10) return 'intermediate';
        return 'advanced';
    }
    async assessPrerequisiteNeed(session, concept, analysis) {
        // 检查用户是否已经掌握了必要的前置知识
        const missingPrerequisites = analysis.dependencies.filter((dep)=>!session.knowledgeGraph[dep.concept] || session.knowledgeGraph[dep.concept] < 60);
        return missingPrerequisites.length > 0;
    }
    async generateAdaptivePath(session, analysis) {
        const currentKnowledge = Object.keys(session.knowledgeGraph).filter((concept)=>session.knowledgeGraph[concept] >= 70);
        const missingKnowledge = analysis.dependencies.map((dep)=>dep.concept);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$pathPlanner$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pathPlanner"].generateLearningPath({
            targetTopic: session.targetTopic,
            currentKnowledge,
            missingKnowledge,
            timeConstraints: {
                sessionLength: 60 // 默认60分钟会话
            }
        });
    }
    async generateDirectHelp(session, difficulty) {
        const prompt = `用户在学习"${session.targetTopic}"时，对"${difficulty.concept}"感到困惑。

用户问题：${difficulty.userQuestion || '理解困难'}
上下文：${difficulty.context}

请提供：
1. 概念的简化解释
2. 具体的例子或类比
3. 学习建议和技巧
4. 相关的练习或思考题

请以易懂的方式回答，帮助用户克服这个困难。`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["llmService"].chat({
            messages: [
                {
                    role: 'system',
                    content: '你是一个耐心的导师，擅长用简单易懂的方式解释复杂概念。'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.7,
            maxTokens: 1500
        });
        return {
            explanation: response.content,
            concept: difficulty.concept,
            helpType: 'direct_explanation'
        };
    }
    checkReturnCondition(session) {
        // 检查是否所有阻塞概念都已掌握
        return session.blockedConcepts.every((concept)=>session.knowledgeGraph[concept] && session.knowledgeGraph[concept] >= 70);
    }
    calculateReadinessScore(session) {
        const relevantConcepts = Object.keys(session.knowledgeGraph);
        if (relevantConcepts.length === 0) return 50;
        const avgMastery = relevantConcepts.reduce((sum, concept)=>sum + (session.knowledgeGraph[concept] || 0), 0) / relevantConcepts.length;
        return Math.round(avgMastery);
    }
    findStepInPath(path, stepId) {
        if (!path) return null;
        return path.steps.find((step)=>step.id === stepId) || null;
    }
    getNextStep(path, currentStepId) {
        if (!path) return null;
        const currentIndex = path.steps.findIndex((step)=>step.id === currentStepId);
        return currentIndex >= 0 && currentIndex < path.steps.length - 1 ? path.steps[currentIndex + 1] : null;
    }
    calculatePathProgress(path, currentStepId) {
        if (!path) return 0;
        const currentIndex = path.steps.findIndex((step)=>step.id === currentStepId);
        return currentIndex >= 0 ? Math.round((currentIndex + 1) / path.steps.length * 100) : 0;
    }
    generateEncouragement(masteryLevel) {
        if (masteryLevel >= 80) return '太棒了！您掌握得很好！';
        if (masteryLevel >= 60) return '不错的进步！继续保持！';
        if (masteryLevel >= 40) return '您正在进步，继续努力！';
        return '没关系，学习需要时间，让我们再试一次！';
    }
    parseRecommendationResponse(content) {
        try {
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
        } catch (error) {
            console.warn('解析建议响应失败:', error);
        }
        return {
            assessment: '继续当前的学习进度',
            nextSteps: [
                '继续当前学习'
            ],
            risks: [],
            optimizations: []
        };
    }
}
const adaptiveLearning = new AdaptiveLearningService();
}),
"[project]/src/app/api/adaptive/start-session/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$adaptiveLearning$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/llm/adaptiveLearning.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const { userId, targetTopic, userContext } = await request.json();
        if (!userId || !targetTopic) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '用户ID和学习主题不能为空'
            }, {
                status: 400
            });
        }
        // 开始新的学习会话
        const session = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$llm$2f$adaptiveLearning$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adaptiveLearning"].startLearningSession(userId, targetTopic, userContext);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            session,
            message: '学习会话已成功启动',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('启动学习会话失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '启动学习会话失败',
            details: error instanceof Error ? error.message : '未知错误'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8d66c8c7._.js.map