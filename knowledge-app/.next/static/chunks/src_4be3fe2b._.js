(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/Card.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": ()=>Card,
    "CardContent": ()=>CardContent,
    "CardFooter": ()=>CardFooter,
    "CardHeader": ()=>CardHeader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
;
;
;
const Card = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(_c = (param, ref)=>{
    let { className, children, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])('rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800', className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Card.tsx",
        lineNumber: 22,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
});
_c1 = Card;
const CardHeader = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(_c2 = (param, ref)=>{
    let { className, children, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])('flex flex-col space-y-1.5 p-6', className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Card.tsx",
        lineNumber: 37,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
});
_c3 = CardHeader;
const CardContent = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(_c4 = (param, ref)=>{
    let { className, children, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])('p-6 pt-0', className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Card.tsx",
        lineNumber: 49,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
});
_c5 = CardContent;
const CardFooter = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(_c6 = (param, ref)=>{
    let { className, children, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])('flex items-center p-6 pt-0', className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Card.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
});
_c7 = CardFooter;
Card.displayName = 'Card';
CardHeader.displayName = 'CardHeader';
CardContent.displayName = 'CardContent';
CardFooter.displayName = 'CardFooter';
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;
__turbopack_context__.k.register(_c, "Card$React.forwardRef");
__turbopack_context__.k.register(_c1, "Card");
__turbopack_context__.k.register(_c2, "CardHeader$React.forwardRef");
__turbopack_context__.k.register(_c3, "CardHeader");
__turbopack_context__.k.register(_c4, "CardContent$React.forwardRef");
__turbopack_context__.k.register(_c5, "CardContent");
__turbopack_context__.k.register(_c6, "CardFooter$React.forwardRef");
__turbopack_context__.k.register(_c7, "CardFooter");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/Button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": ()=>Button
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
;
;
;
const Button = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(_c = (param, ref)=>{
    let { className, variant = 'primary', size = 'md', isLoading = false, disabled, children, ...props } = param;
    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';
    const variantClasses = {
        primary: 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500',
        secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
        outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500',
        ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
        danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
    };
    const sizeClasses = {
        sm: 'px-3 py-1.5 text-sm',
        md: 'px-4 py-2 text-sm',
        lg: 'px-6 py-3 text-base'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(baseClasses, variantClasses[variant], sizeClasses[size], className),
        disabled: disabled || isLoading,
        ref: ref,
        ...props,
        children: [
            isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "animate-spin -ml-1 mr-2 h-4 w-4",
                xmlns: "http://www.w3.org/2000/svg",
                fill: "none",
                viewBox: "0 0 24 24",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                        className: "opacity-25",
                        cx: "12",
                        cy: "12",
                        r: "10",
                        stroke: "currentColor",
                        strokeWidth: "4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/Button.tsx",
                        lineNumber: 56,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        className: "opacity-75",
                        fill: "currentColor",
                        d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/Button.tsx",
                        lineNumber: 64,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/Button.tsx",
                lineNumber: 50,
                columnNumber: 11
            }, ("TURBOPACK compile-time value", void 0)),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/Button.tsx",
        lineNumber: 38,
        columnNumber: 7
    }, ("TURBOPACK compile-time value", void 0));
});
_c1 = Button;
Button.displayName = 'Button';
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Button$React.forwardRef");
__turbopack_context__.k.register(_c1, "Button");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/knowledge/KnowledgePointCard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "KnowledgePointCard": ()=>KnowledgePointCard
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/book-open.js [app-client] (ecmascript) <export default as BookOpen>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/square-pen.js [app-client] (ecmascript) <export default as Edit>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-client] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/target.js [app-client] (ecmascript) <export default as Target>");
'use client';
;
;
;
;
function KnowledgePointCard(param) {
    let { knowledgePoint, masteryLevel = 0, onEdit, onDelete, onSelect, isSelected = false, showActions = true } = param;
    const getDifficultyColor = (level)=>{
        if (level <= 3) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
        if (level <= 6) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    };
    const getMasteryColor = (level)=>{
        if (level >= 80) return 'bg-green-500';
        if (level >= 60) return 'bg-yellow-500';
        if (level >= 40) return 'bg-orange-500';
        return 'bg-red-500';
    };
    const getDifficultyLabel = (level)=>{
        if (level <= 3) return '简单';
        if (level <= 6) return '中等';
        return '困难';
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        className: "transition-all duration-200 hover:shadow-md cursor-pointer ".concat(isSelected ? 'ring-2 ring-indigo-500 border-indigo-500' : ''),
        onClick: ()=>onSelect === null || onSelect === void 0 ? void 0 : onSelect(knowledgePoint),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                className: "pb-3",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-start justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "font-semibold text-lg text-gray-900 dark:text-white mb-2",
                                    children: knowledgePoint.name
                                }, void 0, false, {
                                    fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                    lineNumber: 58,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2 mb-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(getDifficultyColor(knowledgePoint.difficulty_level)),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__["Target"], {
                                                    className: "w-3 h-3 mr-1"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                                    lineNumber: 63,
                                                    columnNumber: 17
                                                }, this),
                                                getDifficultyLabel(knowledgePoint.difficulty_level)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                            lineNumber: 62,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-xs text-gray-500 dark:text-gray-400",
                                            children: [
                                                "难度 ",
                                                knowledgePoint.difficulty_level,
                                                "/10"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                            lineNumber: 66,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                    lineNumber: 61,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                            lineNumber: 57,
                            columnNumber: 11
                        }, this),
                        showActions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-1 ml-2",
                            children: [
                                onEdit && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "ghost",
                                    size: "sm",
                                    onClick: (e)=>{
                                        e.stopPropagation();
                                        onEdit(knowledgePoint);
                                    },
                                    className: "h-8 w-8 p-0",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__["Edit"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                        lineNumber: 83,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                    lineNumber: 74,
                                    columnNumber: 17
                                }, this),
                                onDelete && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "ghost",
                                    size: "sm",
                                    onClick: (e)=>{
                                        e.stopPropagation();
                                        onDelete(knowledgePoint.id);
                                    },
                                    className: "h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                        lineNumber: 96,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                    lineNumber: 87,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                            lineNumber: 72,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                    lineNumber: 56,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                lineNumber: 55,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "pt-0",
                children: [
                    knowledgePoint.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2",
                        children: knowledgePoint.description
                    }, void 0, false, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                        lineNumber: 106,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between mb-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs font-medium text-gray-700 dark:text-gray-300",
                                        children: "掌握程度"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                        lineNumber: 114,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-gray-500 dark:text-gray-400",
                                        children: [
                                            masteryLevel,
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                        lineNumber: 117,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                lineNumber: 113,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-2 rounded-full transition-all duration-300 ".concat(getMasteryColor(masteryLevel)),
                                    style: {
                                        width: "".concat(masteryLevel, "%")
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                    lineNumber: 122,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                        lineNumber: 112,
                        columnNumber: 9
                    }, this),
                    knowledgePoint.tags && knowledgePoint.tags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap gap-1",
                        children: [
                            knowledgePoint.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "inline-flex items-center px-2 py-1 rounded-md text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300",
                                    children: tag
                                }, index, false, {
                                    fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                    lineNumber: 133,
                                    columnNumber: 15
                                }, this)),
                            knowledgePoint.tags.length > 3 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-xs text-gray-500 dark:text-gray-400",
                                children: [
                                    "+",
                                    knowledgePoint.tags.length - 3,
                                    " 更多"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                lineNumber: 141,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                        lineNumber: 131,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                lineNumber: 104,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardFooter"], {
                className: "pt-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between w-full text-xs text-gray-500 dark:text-gray-400",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__["BookOpen"], {
                                    className: "w-3 h-3 mr-1"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                                    lineNumber: 152,
                                    columnNumber: 13
                                }, this),
                                "创建于 ",
                                new Date(knowledgePoint.created_at).toLocaleDateString()
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                            lineNumber: 151,
                            columnNumber: 11
                        }, this),
                        masteryLevel >= 80 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                            children: "已掌握"
                        }, void 0, false, {
                            fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                            lineNumber: 156,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                    lineNumber: 150,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
                lineNumber: 149,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/knowledge/KnowledgePointCard.tsx",
        lineNumber: 49,
        columnNumber: 5
    }, this);
}
_c = KnowledgePointCard;
var _c;
__turbopack_context__.k.register(_c, "KnowledgePointCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/Input.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": ()=>Input
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
const Input = /*#__PURE__*/ _s(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(_c = _s((param, ref)=>{
    let { className, label, error, helperText, id, ...props } = param;
    _s();
    const generatedId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useId();
    const inputId = id || generatedId;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full",
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: inputId,
                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                children: label
            }, void 0, false, {
                fileName: "[project]/src/components/ui/Input.tsx",
                lineNumber: 18,
                columnNumber: 11
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                id: inputId,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])('block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm', 'placeholder:text-gray-400 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500', 'disabled:cursor-not-allowed disabled:opacity-50', 'dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-500', 'dark:focus:border-indigo-400 dark:focus:ring-indigo-400', error && 'border-red-500 focus:border-red-500 focus:ring-red-500', className),
                ref: ref,
                ...props
            }, void 0, false, {
                fileName: "[project]/src/components/ui/Input.tsx",
                lineNumber: 25,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                children: error
            }, void 0, false, {
                fileName: "[project]/src/components/ui/Input.tsx",
                lineNumber: 40,
                columnNumber: 11
            }, ("TURBOPACK compile-time value", void 0)),
            helperText && !error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-1 text-sm text-gray-500 dark:text-gray-400",
                children: helperText
            }, void 0, false, {
                fileName: "[project]/src/components/ui/Input.tsx",
                lineNumber: 45,
                columnNumber: 11
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/Input.tsx",
        lineNumber: 16,
        columnNumber: 7
    }, ("TURBOPACK compile-time value", void 0));
}, "P3bvVUypbBAHy0F8g4TFKgtieUM=")), "P3bvVUypbBAHy0F8g4TFKgtieUM=");
_c1 = Input;
Input.displayName = 'Input';
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Input$React.forwardRef");
__turbopack_context__.k.register(_c1, "Input");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "supabase": ()=>supabase
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-client] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://placeholder.supabase.co") || 'https://placeholder.supabase.co';
const supabaseAnonKey = ("TURBOPACK compile-time value", "placeholder_key") || 'placeholder_key';
// 检查是否为开发环境且使用占位符配置
const isDevMode = ("TURBOPACK compile-time value", "development") === 'development' && supabaseUrl.includes('placeholder');
const supabase = isDevMode ? null // 开发模式下返回null，API调用将被模拟
 : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/database.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "dependenciesAPI": ()=>dependenciesAPI,
    "knowledgePointsAPI": ()=>knowledgePointsAPI,
    "progressAPI": ()=>progressAPI,
    "questionsAPI": ()=>questionsAPI,
    "sessionsAPI": ()=>sessionsAPI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
// 获取当前用户ID的辅助函数
const getCurrentUserId = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        // 在客户端，尝试从localStorage获取用户信息
        try {
            const authStorage = localStorage.getItem('auth-storage');
            if (authStorage) {
                var _parsed_state_user, _parsed_state;
                const parsed = JSON.parse(authStorage);
                if ((_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : (_parsed_state_user = _parsed_state.user) === null || _parsed_state_user === void 0 ? void 0 : _parsed_state_user.id) {
                    return parsed.state.user.id;
                }
            }
        } catch (error) {
            console.warn('获取用户ID失败:', error);
        }
    }
    // 开发模式下返回模拟用户ID
    return 'mock-user';
};
// 开发模式下的模拟数据
const mockKnowledgePoints = [
    {
        id: '1',
        name: '基础算术',
        description: '加减乘除的基本运算',
        difficulty_level: 1,
        tags: [
            '数学',
            '基础'
        ],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 'mock-user'
    },
    {
        id: '2',
        name: 'JavaScript基础',
        description: 'JavaScript语法和基本概念',
        difficulty_level: 3,
        tags: [
            '编程',
            'JavaScript'
        ],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 'mock-user'
    }
];
const mockDependencies = [
    {
        id: 'dep1',
        prerequisite_id: '1',
        dependent_id: '2',
        created_at: new Date().toISOString()
    }
];
const mockProgress = [
    {
        id: 'progress1',
        user_id: 'mock-user',
        knowledge_point_id: '1',
        mastery_level: 85,
        notes: '基础算术已经掌握得很好',
        last_reviewed: new Date().toISOString(),
        test_scores: [
            80,
            85,
            90
        ],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    },
    {
        id: 'progress2',
        user_id: 'mock-user',
        knowledge_point_id: '2',
        mastery_level: 45,
        notes: 'JavaScript基础还需要更多练习',
        last_reviewed: new Date().toISOString(),
        test_scores: [
            40,
            45,
            50
        ],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    }
];
// 检查是否为开发模式
const isDevMode = !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"];
const knowledgePointsAPI = {
    // Get all knowledge points for current user
    async getAll () {
        if (isDevMode) {
            return Promise.resolve([
                ...mockKnowledgePoints
            ]);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_points').select('*').order('created_at', {
            ascending: true
        });
        if (error) throw error;
        return data || [];
    },
    // Get knowledge point by ID
    async getById (id) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_points').select('*').eq('id', id).single();
        if (error) throw error;
        return data;
    },
    // Create new knowledge point
    async create (knowledgePoint) {
        if (isDevMode) {
            const newPoint = {
                ...knowledgePoint,
                id: Date.now().toString(),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            mockKnowledgePoints.push(newPoint);
            return Promise.resolve(newPoint);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_points').insert(knowledgePoint).select().single();
        if (error) throw error;
        return data;
    },
    // Update knowledge point
    async update (id, updates) {
        if (isDevMode) {
            const index = mockKnowledgePoints.findIndex((point)=>point.id === id);
            if (index === -1) throw new Error('知识点不存在');
            const updatedPoint = {
                ...mockKnowledgePoints[index],
                ...updates,
                updated_at: new Date().toISOString()
            };
            mockKnowledgePoints[index] = updatedPoint;
            return Promise.resolve(updatedPoint);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_points').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // Delete knowledge point
    async delete (id) {
        if (isDevMode) {
            const index = mockKnowledgePoints.findIndex((point)=>point.id === id);
            if (index === -1) throw new Error('知识点不存在');
            mockKnowledgePoints.splice(index, 1);
            // 同时删除相关的依赖关系
            const dependencyIndexes = [];
            for(let i = mockDependencies.length - 1; i >= 0; i--){
                if (mockDependencies[i].prerequisite_id === id || mockDependencies[i].dependent_id === id) {
                    dependencyIndexes.push(i);
                }
            }
            dependencyIndexes.forEach((index)=>mockDependencies.splice(index, 1));
            return Promise.resolve();
        }
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_points').delete().eq('id', id);
        if (error) throw error;
    },
    // Search knowledge points by name or tags
    async search (query) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_points').select('*').or("name.ilike.%".concat(query, "%, description.ilike.%").concat(query, "%")).order('created_at', {
            ascending: true
        });
        if (error) throw error;
        return data || [];
    }
};
const dependenciesAPI = {
    // Get all dependencies
    async getAll () {
        if (isDevMode) {
            return Promise.resolve([
                ...mockDependencies
            ]);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_dependencies').select('*').order('created_at', {
            ascending: true
        });
        if (error) throw error;
        return data || [];
    },
    // Get prerequisites for a knowledge point
    async getPrerequisites (knowledgePointId) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_dependencies').select("\n        prerequisite_id,\n        knowledge_points!knowledge_dependencies_prerequisite_id_fkey(*)\n      ").eq('dependent_id', knowledgePointId);
        if (error) throw error;
        return (data === null || data === void 0 ? void 0 : data.map((item)=>item.knowledge_points).filter(Boolean)) || [];
    },
    // Get dependents for a knowledge point
    async getDependents (knowledgePointId) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_dependencies').select("\n        dependent_id,\n        knowledge_points!knowledge_dependencies_dependent_id_fkey(*)\n      ").eq('prerequisite_id', knowledgePointId);
        if (error) throw error;
        return (data === null || data === void 0 ? void 0 : data.map((item)=>item.knowledge_points).filter(Boolean)) || [];
    },
    // Create dependency
    async create (prerequisiteId, dependentId) {
        if (isDevMode) {
            const newDependency = {
                id: Date.now().toString(),
                prerequisite_id: prerequisiteId,
                dependent_id: dependentId,
                created_at: new Date().toISOString()
            };
            mockDependencies.push(newDependency);
            return Promise.resolve(newDependency);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_dependencies').insert({
            prerequisite_id: prerequisiteId,
            dependent_id: dependentId
        }).select().single();
        if (error) throw error;
        return data;
    },
    // Delete dependency
    async delete (prerequisiteId, dependentId) {
        if (isDevMode) {
            const index = mockDependencies.findIndex((dep)=>dep.prerequisite_id === prerequisiteId && dep.dependent_id === dependentId);
            if (index !== -1) {
                mockDependencies.splice(index, 1);
            }
            return Promise.resolve();
        }
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_dependencies').delete().eq('prerequisite_id', prerequisiteId).eq('dependent_id', dependentId);
        if (error) throw error;
    }
};
const progressAPI = {
    // Get all progress for current user
    async getAll () {
        if (isDevMode) {
            return Promise.resolve([
                ...mockProgress
            ]);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('user_progress').select('*').order('updated_at', {
            ascending: false
        });
        if (error) throw error;
        return data || [];
    },
    // Get progress for specific knowledge point
    async getByKnowledgePoint (knowledgePointId) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('user_progress').select('*').eq('knowledge_point_id', knowledgePointId).single();
        if (error && error.code !== 'PGRST116') throw error // PGRST116 is "not found"
        ;
        return data;
    },
    // Update or create progress
    async upsert (progress) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('user_progress').upsert(progress, {
            onConflict: 'user_id,knowledge_point_id'
        }).select().single();
        if (error) throw error;
        return data;
    },
    // Delete progress
    async delete (knowledgePointId) {
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('user_progress').delete().eq('knowledge_point_id', knowledgePointId);
        if (error) throw error;
    }
};
const sessionsAPI = {
    // Get all sessions for current user
    async getAll () {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('learning_sessions').select('*').order('start_time', {
            ascending: false
        });
        if (error) throw error;
        return data || [];
    },
    // Get sessions for specific knowledge point
    async getByKnowledgePoint (knowledgePointId) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('learning_sessions').select('*').eq('knowledge_point_id', knowledgePointId).order('start_time', {
            ascending: false
        });
        if (error) throw error;
        return data || [];
    },
    // Create new session
    async create (session) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('learning_sessions').insert(session).select().single();
        if (error) throw error;
        return data;
    },
    // Update session
    async update (id, updates) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('learning_sessions').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
};
const questionsAPI = {
    // Get questions for knowledge point
    async getByKnowledgePoint (knowledgePointId) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('test_questions').select('*').eq('knowledge_point_id', knowledgePointId).order('difficulty', {
            ascending: true
        });
        if (error) throw error;
        return data || [];
    },
    // Create new question
    async create (question) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('test_questions').insert(question).select().single();
        if (error) throw error;
        return data;
    },
    // Update question
    async update (id, updates) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('test_questions').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // Delete question
    async delete (id) {
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('test_questions').delete().eq('id', id);
        if (error) throw error;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/knowledgeGraphService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "KnowledgeGraphService": ()=>KnowledgeGraphService,
    "knowledgeGraphService": ()=>knowledgeGraphService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-client] (ecmascript)");
;
class KnowledgeGraphService {
    /**
   * 验证是否会产生循环依赖
   * 使用深度优先搜索检测环路
   */ async detectCycle(newPrerequisiteId, newDependentId, existingDependencies) {
        // 构建邻接表
        const graph = new Map();
        // 添加现有依赖
        existingDependencies.forEach((dep)=>{
            if (!graph.has(dep.prerequisite_id)) {
                graph.set(dep.prerequisite_id, []);
            }
            graph.get(dep.prerequisite_id).push(dep.dependent_id);
        });
        // 添加新的依赖
        if (!graph.has(newPrerequisiteId)) {
            graph.set(newPrerequisiteId, []);
        }
        graph.get(newPrerequisiteId).push(newDependentId);
        // DFS检测环路
        const visited = new Set();
        const recursionStack = new Set();
        const dfs = (node)=>{
            if (recursionStack.has(node)) return true // 发现环路
            ;
            if (visited.has(node)) return false;
            visited.add(node);
            recursionStack.add(node);
            const neighbors = graph.get(node) || [];
            for (const neighbor of neighbors){
                if (dfs(neighbor)) return true;
            }
            recursionStack.delete(node);
            return false;
        };
        // 检查所有节点
        for (const node of graph.keys()){
            if (!visited.has(node)) {
                if (dfs(node)) return true;
            }
        }
        return false;
    }
    /**
   * 安全地添加依赖关系
   * 确保不会产生循环依赖
   */ async addDependency(prerequisiteId, dependentId) {
        try {
            // 验证知识点存在
            const [prerequisite, dependent] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].getById(prerequisiteId),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].getById(dependentId)
            ]);
            if (!prerequisite) {
                return {
                    success: false,
                    error: '前置知识点不存在'
                };
            }
            if (!dependent) {
                return {
                    success: false,
                    error: '依赖知识点不存在'
                };
            }
            // 检查是否已存在该依赖
            const existingDependencies = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dependenciesAPI"].getAll();
            const exists = existingDependencies.some((dep)=>dep.prerequisite_id === prerequisiteId && dep.dependent_id === dependentId);
            if (exists) {
                return {
                    success: false,
                    error: '依赖关系已存在'
                };
            }
            // 检测循环依赖
            const hasCycle = await this.detectCycle(prerequisiteId, dependentId, existingDependencies);
            if (hasCycle) {
                return {
                    success: false,
                    error: '添加此依赖会产生循环依赖'
                };
            }
            // 创建依赖关系
            const dependency = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dependenciesAPI"].create(prerequisiteId, dependentId);
            return {
                success: true,
                dependency
            };
        } catch (error) {
            console.error('添加依赖关系失败:', error);
            return {
                success: false,
                error: '添加依赖关系失败'
            };
        }
    }
    /**
   * 获取知识图谱数据
   * 包含节点和边的信息
   */ async getKnowledgeGraph() {
        try {
            const [knowledgePoints, dependencies, userProgress] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].getAll(),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dependenciesAPI"].getAll(),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressAPI"].getAll()
            ]);
            // 构建节点
            const nodes = knowledgePoints.map((point)=>{
                const progress = userProgress.find((p)=>p.knowledge_point_id === point.id);
                return {
                    id: point.id,
                    name: point.name,
                    group: point.difficulty_level,
                    mastery_level: (progress === null || progress === void 0 ? void 0 : progress.mastery_level) || 0,
                    difficulty_level: point.difficulty_level
                };
            });
            // 构建边
            const links = dependencies.map((dep)=>({
                    source: dep.prerequisite_id,
                    target: dep.dependent_id
                }));
            return {
                nodes,
                links
            };
        } catch (error) {
            console.error('获取知识图谱失败:', error);
            return {
                nodes: [],
                links: []
            };
        }
    }
    /**
   * 计算拓扑排序
   * 用于确定学习顺序
   */ async getTopologicalOrder() {
        try {
            const dependencies = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dependenciesAPI"].getAll();
            const knowledgePoints = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].getAll();
            // 构建邻接表和入度表
            const graph = new Map();
            const inDegree = new Map();
            // 初始化
            knowledgePoints.forEach((point)=>{
                graph.set(point.id, []);
                inDegree.set(point.id, 0);
            });
            // 构建图
            dependencies.forEach((dep)=>{
                graph.get(dep.prerequisite_id).push(dep.dependent_id);
                inDegree.set(dep.dependent_id, (inDegree.get(dep.dependent_id) || 0) + 1);
            });
            // Kahn算法进行拓扑排序
            const queue = [];
            const result = [];
            // 找到所有入度为0的节点
            for (const [nodeId, degree] of inDegree.entries()){
                if (degree === 0) {
                    queue.push(nodeId);
                }
            }
            while(queue.length > 0){
                const current = queue.shift();
                result.push(current);
                // 处理当前节点的所有邻居
                const neighbors = graph.get(current) || [];
                for (const neighbor of neighbors){
                    const newDegree = inDegree.get(neighbor) - 1;
                    inDegree.set(neighbor, newDegree);
                    if (newDegree === 0) {
                        queue.push(neighbor);
                    }
                }
            }
            return result;
        } catch (error) {
            console.error('计算拓扑排序失败:', error);
            return [];
        }
    }
    /**
   * 获取学习推荐
   * 基于当前掌握情况推荐下一步学习内容
   */ async getLearningRecommendations() {
        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;
        try {
            const [knowledgePoints, dependencies, userProgress] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].getAll(),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dependenciesAPI"].getAll(),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressAPI"].getAll()
            ]);
            const recommendations = [];
            for (const point of knowledgePoints){
                const progress = userProgress.find((p)=>p.knowledge_point_id === point.id);
                const currentMastery = (progress === null || progress === void 0 ? void 0 : progress.mastery_level) || 0;
                // 跳过已掌握的知识点 (>= 80%)
                if (currentMastery >= 80) continue;
                // 检查前置条件是否满足
                const prerequisites = dependencies.filter((dep)=>dep.dependent_id === point.id).map((dep)=>dep.prerequisite_id);
                const allPrerequisitesMet = prerequisites.every((prereqId)=>{
                    const prereqProgress = userProgress.find((p)=>p.knowledge_point_id === prereqId);
                    return ((prereqProgress === null || prereqProgress === void 0 ? void 0 : prereqProgress.mastery_level) || 0) >= 80;
                });
                if (allPrerequisitesMet) {
                    // 计算优先级
                    let priority = 100 - currentMastery // 掌握程度越低，优先级越高
                    ;
                    priority += (10 - point.difficulty_level) * 5; // 难度越低，优先级越高
                    // 估算学习时间（基于难度）
                    const estimatedTime = point.difficulty_level * 30 // 每个难度级别30分钟
                    ;
                    recommendations.push({
                        knowledge_point: point,
                        reason: prerequisites.length > 0 ? "前置条件已满足，可以开始学习" : "基础知识点，建议优先学习",
                        priority,
                        estimated_time: estimatedTime
                    });
                }
            }
            // 按优先级排序并限制数量
            return recommendations.sort((a, b)=>b.priority - a.priority).slice(0, limit);
        } catch (error) {
            console.error('获取学习推荐失败:', error);
            return [];
        }
    }
    /**
   * 删除知识点及其相关依赖
   * 确保数据一致性
   */ async deleteKnowledgePoint(id) {
        try {
            // 检查是否有其他知识点依赖于此知识点
            const dependencies = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dependenciesAPI"].getAll();
            const hasDependents = dependencies.some((dep)=>dep.prerequisite_id === id);
            if (hasDependents) {
                return {
                    success: false,
                    error: '无法删除：其他知识点依赖于此知识点，请先移除相关依赖关系'
                };
            }
            // 删除知识点（数据库外键约束会自动删除相关的依赖关系和进度记录）
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].delete(id);
            return {
                success: true
            };
        } catch (error) {
            console.error('删除知识点失败:', error);
            return {
                success: false,
                error: '删除知识点失败'
            };
        }
    }
}
const knowledgeGraphService = new KnowledgeGraphService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/useKnowledgeStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useKnowledgeStore": ()=>useKnowledgeStore
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
;
const useKnowledgeStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        // Initial state
        knowledgePoints: [],
        dependencies: [],
        userProgress: [],
        selectedKnowledgePoint: null,
        isLoading: false,
        error: null,
        // Actions
        setKnowledgePoints: (points)=>set({
                knowledgePoints: points
            }),
        addKnowledgePoint: (point)=>set((state)=>({
                    knowledgePoints: [
                        ...state.knowledgePoints,
                        point
                    ]
                })),
        updateKnowledgePoint: (id, updates)=>set((state)=>({
                    knowledgePoints: state.knowledgePoints.map((point)=>point.id === id ? {
                            ...point,
                            ...updates
                        } : point)
                })),
        deleteKnowledgePoint: (id)=>set((state)=>({
                    knowledgePoints: state.knowledgePoints.filter((point)=>point.id !== id),
                    dependencies: state.dependencies.filter((dep)=>dep.prerequisite_id !== id && dep.dependent_id !== id)
                })),
        setDependencies: (deps)=>set({
                dependencies: deps
            }),
        addDependency: (dep)=>set((state)=>({
                    dependencies: [
                        ...state.dependencies,
                        dep
                    ]
                })),
        removeDependency: (prerequisiteId, dependentId)=>set((state)=>({
                    dependencies: state.dependencies.filter((dep)=>!(dep.prerequisite_id === prerequisiteId && dep.dependent_id === dependentId))
                })),
        setUserProgress: (progress)=>set({
                userProgress: progress
            }),
        updateUserProgress: (knowledgePointId, updates)=>set((state)=>({
                    userProgress: state.userProgress.map((progress)=>progress.knowledge_point_id === knowledgePointId ? {
                            ...progress,
                            ...updates
                        } : progress)
                })),
        setSelectedKnowledgePoint: (point)=>set({
                selectedKnowledgePoint: point
            }),
        setLoading: (loading)=>set({
                isLoading: loading
            }),
        setError: (error)=>set({
                error
            }),
        // Computed functions
        getKnowledgeGraph: ()=>{
            const { knowledgePoints, dependencies, userProgress } = get();
            const nodes = knowledgePoints.map((point)=>{
                const progress = userProgress.find((p)=>p.knowledge_point_id === point.id);
                return {
                    id: point.id,
                    name: point.name,
                    group: point.difficulty_level,
                    mastery_level: (progress === null || progress === void 0 ? void 0 : progress.mastery_level) || 0,
                    difficulty_level: point.difficulty_level
                };
            });
            const links = dependencies.map((dep)=>({
                    source: dep.prerequisite_id,
                    target: dep.dependent_id
                }));
            return {
                nodes,
                links
            };
        },
        getPrerequisites: (knowledgePointId)=>{
            const { knowledgePoints, dependencies } = get();
            const prerequisiteIds = dependencies.filter((dep)=>dep.dependent_id === knowledgePointId).map((dep)=>dep.prerequisite_id);
            return knowledgePoints.filter((point)=>prerequisiteIds.includes(point.id));
        },
        getDependents: (knowledgePointId)=>{
            const { knowledgePoints, dependencies } = get();
            const dependentIds = dependencies.filter((dep)=>dep.prerequisite_id === knowledgePointId).map((dep)=>dep.dependent_id);
            return knowledgePoints.filter((point)=>dependentIds.includes(point.id));
        },
        getUserMasteryLevel: (knowledgePointId)=>{
            const { userProgress } = get();
            const progress = userProgress.find((p)=>p.knowledge_point_id === knowledgePointId);
            return (progress === null || progress === void 0 ? void 0 : progress.mastery_level) || 0;
        },
        getRecommendations: ()=>{
            const { knowledgePoints, dependencies, userProgress } = get();
            // Simple recommendation algorithm: find knowledge points where all prerequisites are mastered
            return knowledgePoints.filter((point)=>{
                const progress = userProgress.find((p)=>p.knowledge_point_id === point.id);
                const currentMastery = (progress === null || progress === void 0 ? void 0 : progress.mastery_level) || 0;
                // Skip if already mastered (>= 80%)
                if (currentMastery >= 80) return false;
                // Check if all prerequisites are mastered
                const prerequisites = dependencies.filter((dep)=>dep.dependent_id === point.id).map((dep)=>dep.prerequisite_id);
                const allPrerequisitesMastered = prerequisites.every((prereqId)=>{
                    const prereqProgress = userProgress.find((p)=>p.knowledge_point_id === prereqId);
                    return ((prereqProgress === null || prereqProgress === void 0 ? void 0 : prereqProgress.mastery_level) || 0) >= 80;
                });
                return allPrerequisitesMastered;
            });
        }
    }));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useKnowledgeGraph.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useKnowledgeGraph": ()=>useKnowledgeGraph,
    "useKnowledgeGraphData": ()=>useKnowledgeGraphData
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$knowledgeGraphService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/knowledgeGraphService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useKnowledgeStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useKnowledgeStore.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
;
;
;
;
function useKnowledgeGraph() {
    _s();
    const { knowledgePoints, dependencies, selectedKnowledgePoint, isLoading, error, setKnowledgePoints, setDependencies, addKnowledgePoint, updateKnowledgePoint, deleteKnowledgePoint: deleteFromStore, addDependency, removeDependency, setSelectedKnowledgePoint, setLoading, setError, getKnowledgeGraph, getRecommendations } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useKnowledgeStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnowledgeStore"])();
    // 本地状态
    const [recommendations, setRecommendations] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [topologicalOrder, setTopologicalOrder] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    /**
   * 加载所有知识图谱数据
   */ const loadKnowledgeGraph = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useKnowledgeGraph.useCallback[loadKnowledgeGraph]": async ()=>{
            setLoading(true);
            setError(null);
            try {
                const { dependenciesAPI } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                const [points, dependencies] = await Promise.all([
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].getAll(),
                    dependenciesAPI.getAll()
                ]);
                setKnowledgePoints(points);
                setDependencies(dependencies);
            } catch (err) {
                console.error('加载知识图谱失败:', err);
                setError('加载知识图谱失败');
            } finally{
                setLoading(false);
            }
        }
    }["useKnowledgeGraph.useCallback[loadKnowledgeGraph]"], [
        setKnowledgePoints,
        setDependencies,
        setLoading,
        setError
    ]);
    /**
   * 创建新知识点
   */ const createKnowledgePoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useKnowledgeGraph.useCallback[createKnowledgePoint]": async (pointData)=>{
            setLoading(true);
            setError(null);
            try {
                const newPoint = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].create(pointData);
                addKnowledgePoint(newPoint);
                return {
                    success: true,
                    data: newPoint
                };
            } catch (err) {
                console.error('创建知识点失败:', err);
                setError('创建知识点失败');
                return {
                    success: false,
                    error: '创建知识点失败'
                };
            } finally{
                setLoading(false);
            }
        }
    }["useKnowledgeGraph.useCallback[createKnowledgePoint]"], [
        addKnowledgePoint,
        setLoading,
        setError
    ]);
    /**
   * 更新知识点
   */ const updateKnowledgePointData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useKnowledgeGraph.useCallback[updateKnowledgePointData]": async (id, updates)=>{
            setLoading(true);
            setError(null);
            try {
                const updatedPoint = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].update(id, updates);
                updateKnowledgePoint(id, updatedPoint);
                return {
                    success: true,
                    data: updatedPoint
                };
            } catch (err) {
                console.error('更新知识点失败:', err);
                setError('更新知识点失败');
                return {
                    success: false,
                    error: '更新知识点失败'
                };
            } finally{
                setLoading(false);
            }
        }
    }["useKnowledgeGraph.useCallback[updateKnowledgePointData]"], [
        updateKnowledgePoint,
        setLoading,
        setError
    ]);
    /**
   * 删除知识点
   */ const deleteKnowledgePoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useKnowledgeGraph.useCallback[deleteKnowledgePoint]": async (id)=>{
            setLoading(true);
            setError(null);
            try {
                const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$knowledgeGraphService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeGraphService"].deleteKnowledgePoint(id);
                if (result.success) {
                    deleteFromStore(id);
                    // 如果删除的是当前选中的知识点，清除选择
                    if ((selectedKnowledgePoint === null || selectedKnowledgePoint === void 0 ? void 0 : selectedKnowledgePoint.id) === id) {
                        setSelectedKnowledgePoint(null);
                    }
                    return {
                        success: true
                    };
                } else {
                    setError(result.error || '删除知识点失败');
                    return {
                        success: false,
                        error: result.error
                    };
                }
            } catch (err) {
                console.error('删除知识点失败:', err);
                setError('删除知识点失败');
                return {
                    success: false,
                    error: '删除知识点失败'
                };
            } finally{
                setLoading(false);
            }
        }
    }["useKnowledgeGraph.useCallback[deleteKnowledgePoint]"], [
        deleteFromStore,
        selectedKnowledgePoint,
        setSelectedKnowledgePoint,
        setLoading,
        setError
    ]);
    /**
   * 添加依赖关系
   */ const createDependency = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useKnowledgeGraph.useCallback[createDependency]": async (prerequisiteId, dependentId)=>{
            setLoading(true);
            setError(null);
            try {
                const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$knowledgeGraphService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeGraphService"].addDependency(prerequisiteId, dependentId);
                if (result.success && result.dependency) {
                    addDependency(result.dependency);
                    return {
                        success: true,
                        data: result.dependency
                    };
                } else {
                    setError(result.error || '添加依赖关系失败');
                    return {
                        success: false,
                        error: result.error
                    };
                }
            } catch (err) {
                console.error('添加依赖关系失败:', err);
                setError('添加依赖关系失败');
                return {
                    success: false,
                    error: '添加依赖关系失败'
                };
            } finally{
                setLoading(false);
            }
        }
    }["useKnowledgeGraph.useCallback[createDependency]"], [
        addDependency,
        setLoading,
        setError
    ]);
    /**
   * 删除依赖关系
   */ const deleteDependency = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useKnowledgeGraph.useCallback[deleteDependency]": async (prerequisiteId, dependentId)=>{
            setLoading(true);
            setError(null);
            try {
                const { dependenciesAPI } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                await dependenciesAPI.delete(prerequisiteId, dependentId);
                removeDependency(prerequisiteId, dependentId);
                return {
                    success: true
                };
            } catch (err) {
                console.error('删除依赖关系失败:', err);
                setError('删除依赖关系失败');
                return {
                    success: false,
                    error: '删除依赖关系失败'
                };
            } finally{
                setLoading(false);
            }
        }
    }["useKnowledgeGraph.useCallback[deleteDependency]"], [
        removeDependency,
        setLoading,
        setError
    ]);
    /**
   * 获取学习推荐
   */ const loadRecommendations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useKnowledgeGraph.useCallback[loadRecommendations]": async function() {
            let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;
            try {
                const recs = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$knowledgeGraphService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeGraphService"].getLearningRecommendations(limit);
                setRecommendations(recs);
                return recs;
            } catch (err) {
                console.error('获取学习推荐失败:', err);
                return [];
            }
        }
    }["useKnowledgeGraph.useCallback[loadRecommendations]"], []);
    /**
   * 获取拓扑排序
   */ const loadTopologicalOrder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useKnowledgeGraph.useCallback[loadTopologicalOrder]": async ()=>{
            try {
                const order = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$knowledgeGraphService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeGraphService"].getTopologicalOrder();
                setTopologicalOrder(order);
                return order;
            } catch (err) {
                console.error('获取拓扑排序失败:', err);
                return [];
            }
        }
    }["useKnowledgeGraph.useCallback[loadTopologicalOrder]"], []);
    /**
   * 搜索知识点
   */ const searchKnowledgePoints = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useKnowledgeGraph.useCallback[searchKnowledgePoints]": async (query)=>{
            if (!query.trim()) return knowledgePoints;
            try {
                const results = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].search(query);
                return results;
            } catch (err) {
                console.error('搜索知识点失败:', err);
                return [];
            }
        }
    }["useKnowledgeGraph.useCallback[searchKnowledgePoints]"], [
        knowledgePoints
    ]);
    // 初始化时加载数据
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useKnowledgeGraph.useEffect": ()=>{
            loadKnowledgeGraph();
        }
    }["useKnowledgeGraph.useEffect"], [
        loadKnowledgeGraph
    ]);
    // 返回所有需要的状态和方法
    return {
        // 状态
        knowledgePoints,
        dependencies,
        selectedKnowledgePoint,
        recommendations,
        topologicalOrder,
        isLoading,
        error,
        // 计算属性
        knowledgeGraph: getKnowledgeGraph(),
        storeRecommendations: getRecommendations(),
        // 方法
        loadKnowledgeGraph,
        createKnowledgePoint,
        updateKnowledgePoint: updateKnowledgePointData,
        deleteKnowledgePoint,
        createDependency,
        deleteDependency,
        loadRecommendations,
        loadTopologicalOrder,
        searchKnowledgePoints,
        setSelectedKnowledgePoint,
        // 工具方法
        clearError: ()=>setError(null),
        refreshData: loadKnowledgeGraph
    };
}
_s(useKnowledgeGraph, "54Omrp8xkQYKeaFLktxfRDJicAM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useKnowledgeStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnowledgeStore"]
    ];
});
function useKnowledgeGraphData() {
    _s1();
    const { knowledgePoints, dependencies, isLoading, error, getKnowledgeGraph, getRecommendations, setKnowledgePoints, setDependencies, setLoading, setError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useKnowledgeStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnowledgeStore"])();
    // 加载数据
    const loadData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useKnowledgeGraphData.useCallback[loadData]": async ()=>{
            setLoading(true);
            setError(null);
            try {
                const { dependenciesAPI } = await __turbopack_context__.r("[project]/src/lib/database.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                const [points, deps] = await Promise.all([
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgePointsAPI"].getAll(),
                    dependenciesAPI.getAll()
                ]);
                setKnowledgePoints(points);
                setDependencies(deps);
            } catch (err) {
                console.error('加载知识图谱数据失败:', err);
                setError('加载知识图谱数据失败');
            } finally{
                setLoading(false);
            }
        }
    }["useKnowledgeGraphData.useCallback[loadData]"], [
        setKnowledgePoints,
        setDependencies,
        setLoading,
        setError
    ]);
    // 初始化时加载数据
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useKnowledgeGraphData.useEffect": ()=>{
            loadData();
        }
    }["useKnowledgeGraphData.useEffect"], [
        loadData
    ]);
    return {
        knowledgePoints,
        dependencies,
        knowledgeGraph: getKnowledgeGraph(),
        recommendations: getRecommendations(),
        isLoading,
        error
    };
}
_s1(useKnowledgeGraphData, "8R7fLAhXOQws56D/HYsEAj4qrrQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useKnowledgeStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnowledgeStore"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/knowledge/KnowledgePointList.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "KnowledgePointList": ()=>KnowledgePointList
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$knowledge$2f$KnowledgePointCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/knowledge/KnowledgePointCard.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useKnowledgeGraph$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useKnowledgeGraph.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-client] (ecmascript) <export default as Filter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$narrow$2d$wide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SortAsc$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js [app-client] (ecmascript) <export default as SortAsc>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$down$2d$wide$2d$narrow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SortDesc$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.js [app-client] (ecmascript) <export default as SortDesc>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
function KnowledgePointList(param) {
    let { onCreateNew, onEditPoint, onDeletePoint, showActions = true } = param;
    _s();
    const { knowledgePoints, selectedKnowledgePoint, setSelectedKnowledgePoint, isLoading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useKnowledgeGraph$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnowledgeGraph"])();
    // 本地状态
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedTags, setSelectedTags] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [difficultyFilter, setDifficultyFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [sortField, setSortField] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('created_at');
    const [sortOrder, setSortOrder] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('desc');
    const [showFilters, setShowFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // 获取所有标签
    const allTags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "KnowledgePointList.useMemo[allTags]": ()=>{
            const tags = new Set();
            knowledgePoints.forEach({
                "KnowledgePointList.useMemo[allTags]": (point)=>{
                    var _point_tags;
                    (_point_tags = point.tags) === null || _point_tags === void 0 ? void 0 : _point_tags.forEach({
                        "KnowledgePointList.useMemo[allTags]": (tag)=>tags.add(tag)
                    }["KnowledgePointList.useMemo[allTags]"]);
                }
            }["KnowledgePointList.useMemo[allTags]"]);
            return Array.from(tags).sort();
        }
    }["KnowledgePointList.useMemo[allTags]"], [
        knowledgePoints
    ]);
    // 过滤和排序逻辑
    const filteredAndSortedPoints = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "KnowledgePointList.useMemo[filteredAndSortedPoints]": ()=>{
            let filtered = knowledgePoints;
            // 搜索过滤
            if (searchQuery.trim()) {
                const query = searchQuery.toLowerCase();
                filtered = filtered.filter({
                    "KnowledgePointList.useMemo[filteredAndSortedPoints]": (point)=>{
                        var _point_description, _point_tags;
                        return point.name.toLowerCase().includes(query) || ((_point_description = point.description) === null || _point_description === void 0 ? void 0 : _point_description.toLowerCase().includes(query)) || ((_point_tags = point.tags) === null || _point_tags === void 0 ? void 0 : _point_tags.some({
                            "KnowledgePointList.useMemo[filteredAndSortedPoints]": (tag)=>tag.toLowerCase().includes(query)
                        }["KnowledgePointList.useMemo[filteredAndSortedPoints]"]));
                    }
                }["KnowledgePointList.useMemo[filteredAndSortedPoints]"]);
            }
            // 标签过滤
            if (selectedTags.length > 0) {
                filtered = filtered.filter({
                    "KnowledgePointList.useMemo[filteredAndSortedPoints]": (point)=>selectedTags.every({
                            "KnowledgePointList.useMemo[filteredAndSortedPoints]": (tag)=>{
                                var _point_tags;
                                return (_point_tags = point.tags) === null || _point_tags === void 0 ? void 0 : _point_tags.includes(tag);
                            }
                        }["KnowledgePointList.useMemo[filteredAndSortedPoints]"])
                }["KnowledgePointList.useMemo[filteredAndSortedPoints]"]);
            }
            // 难度过滤
            if (difficultyFilter !== null) {
                filtered = filtered.filter({
                    "KnowledgePointList.useMemo[filteredAndSortedPoints]": (point)=>point.difficulty_level === difficultyFilter
                }["KnowledgePointList.useMemo[filteredAndSortedPoints]"]);
            }
            // 排序
            filtered.sort({
                "KnowledgePointList.useMemo[filteredAndSortedPoints]": (a, b)=>{
                    let aValue;
                    let bValue;
                    switch(sortField){
                        case 'name':
                            aValue = a.name.toLowerCase();
                            bValue = b.name.toLowerCase();
                            break;
                        case 'difficulty':
                            aValue = a.difficulty_level;
                            bValue = b.difficulty_level;
                            break;
                        case 'mastery':
                            // 这里需要从store获取掌握程度，暂时使用0
                            aValue = 0;
                            bValue = 0;
                            break;
                        case 'created_at':
                            aValue = new Date(a.created_at).getTime();
                            bValue = new Date(b.created_at).getTime();
                            break;
                        default:
                            return 0;
                    }
                    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
                    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
                    return 0;
                }
            }["KnowledgePointList.useMemo[filteredAndSortedPoints]"]);
            return filtered;
        }
    }["KnowledgePointList.useMemo[filteredAndSortedPoints]"], [
        knowledgePoints,
        searchQuery,
        selectedTags,
        difficultyFilter,
        sortField,
        sortOrder
    ]);
    const handleSort = (field)=>{
        if (sortField === field) {
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortOrder('asc');
        }
    };
    const toggleTag = (tag)=>{
        setSelectedTags((prev)=>prev.includes(tag) ? prev.filter((t)=>t !== tag) : [
                ...prev,
                tag
            ]);
    };
    const clearFilters = ()=>{
        setSearchQuery('');
        setSelectedTags([]);
        setDifficultyFilter(null);
    };
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center py-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-red-600 dark:text-red-400",
                    children: error
                }, void 0, false, {
                    fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                    lineNumber: 139,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    variant: "outline",
                    onClick: ()=>window.location.reload(),
                    className: "mt-4",
                    children: "重试"
                }, void 0, false, {
                    fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                    lineNumber: 140,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
            lineNumber: 138,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 max-w-md",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                placeholder: "搜索知识点...",
                                value: searchQuery,
                                onChange: (e)=>setSearchQuery(e.target.value),
                                className: "pl-10"
                            }, void 0, false, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                lineNumber: 156,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                lineNumber: 162,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                        lineNumber: 155,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "sm",
                                onClick: ()=>setShowFilters(!showFilters),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                                        className: "h-4 w-4 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                        lineNumber: 171,
                                        columnNumber: 13
                                    }, this),
                                    "筛选"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                lineNumber: 166,
                                columnNumber: 11
                            }, this),
                            onCreateNew && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                onClick: onCreateNew,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                        className: "h-4 w-4 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                        lineNumber: 177,
                                        columnNumber: 15
                                    }, this),
                                    "新建知识点"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                lineNumber: 176,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                        lineNumber: 165,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                lineNumber: 154,
                columnNumber: 7
            }, this),
            showFilters && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap items-center gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                        children: "排序:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                        lineNumber: 190,
                                        columnNumber: 15
                                    }, this),
                                    [
                                        'name',
                                        'difficulty',
                                        'created_at'
                                    ].map((field)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: sortField === field ? 'primary' : 'ghost',
                                            size: "sm",
                                            onClick: ()=>handleSort(field),
                                            className: "flex items-center gap-1",
                                            children: [
                                                field === 'name' && '名称',
                                                field === 'difficulty' && '难度',
                                                field === 'created_at' && '创建时间',
                                                sortField === field && (sortOrder === 'asc' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$narrow$2d$wide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SortAsc$3e$__["SortAsc"], {
                                                    className: "h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                                    lineNumber: 203,
                                                    columnNumber: 43
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$down$2d$wide$2d$narrow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SortDesc$3e$__["SortDesc"], {
                                                    className: "h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                                    lineNumber: 203,
                                                    columnNumber: 77
                                                }, this))
                                            ]
                                        }, field, true, {
                                            fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                            lineNumber: 192,
                                            columnNumber: 17
                                        }, this))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                lineNumber: 189,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                        children: "难度:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                        lineNumber: 211,
                                        columnNumber: 15
                                    }, this),
                                    [
                                        1,
                                        2,
                                        3,
                                        4,
                                        5,
                                        6,
                                        7,
                                        8,
                                        9,
                                        10
                                    ].map((level)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: difficultyFilter === level ? 'primary' : 'ghost',
                                            size: "sm",
                                            onClick: ()=>setDifficultyFilter(difficultyFilter === level ? null : level),
                                            children: level
                                        }, level, false, {
                                            fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                            lineNumber: 213,
                                            columnNumber: 17
                                        }, this))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                lineNumber: 210,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                        lineNumber: 187,
                        columnNumber: 11
                    }, this),
                    allTags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block",
                                children: "标签:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                lineNumber: 228,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-2",
                                children: allTags.map((tag)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: selectedTags.includes(tag) ? 'primary' : 'outline',
                                        size: "sm",
                                        onClick: ()=>toggleTag(tag),
                                        children: tag
                                    }, tag, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                        lineNumber: 231,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                lineNumber: 229,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                        lineNumber: 227,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-end",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            variant: "ghost",
                            size: "sm",
                            onClick: clearFilters,
                            children: "清除筛选"
                        }, void 0, false, {
                            fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                            lineNumber: 246,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                        lineNumber: 245,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                lineNumber: 186,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-sm text-gray-600 dark:text-gray-400",
                children: [
                    "显示 ",
                    filteredAndSortedPoints.length,
                    " / ",
                    knowledgePoints.length,
                    " 个知识点"
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                lineNumber: 254,
                columnNumber: 7
            }, this),
            isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
                children: [
                    ...Array(6)
                ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-pulse",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gray-200 dark:bg-gray-700 rounded-lg h-48"
                        }, void 0, false, {
                            fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                            lineNumber: 263,
                            columnNumber: 15
                        }, this)
                    }, i, false, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                        lineNumber: 262,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                lineNumber: 260,
                columnNumber: 9
            }, this) : filteredAndSortedPoints.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center py-12",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-500 dark:text-gray-400 mb-4",
                        children: knowledgePoints.length === 0 ? '还没有知识点' : '没有找到匹配的知识点'
                    }, void 0, false, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                        lineNumber: 269,
                        columnNumber: 11
                    }, this),
                    onCreateNew && knowledgePoints.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        onClick: onCreateNew,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                className: "h-4 w-4 mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                                lineNumber: 274,
                                columnNumber: 15
                            }, this),
                            "创建第一个知识点"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                        lineNumber: 273,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                lineNumber: 268,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
                children: filteredAndSortedPoints.map((point)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$knowledge$2f$KnowledgePointCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KnowledgePointCard"], {
                        knowledgePoint: point,
                        masteryLevel: 0,
                        onEdit: onEditPoint,
                        onDelete: onDeletePoint,
                        onSelect: setSelectedKnowledgePoint,
                        isSelected: (selectedKnowledgePoint === null || selectedKnowledgePoint === void 0 ? void 0 : selectedKnowledgePoint.id) === point.id,
                        showActions: showActions
                    }, point.id, false, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                        lineNumber: 282,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
                lineNumber: 280,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/knowledge/KnowledgePointList.tsx",
        lineNumber: 152,
        columnNumber: 5
    }, this);
}
_s(KnowledgePointList, "mtnY55+JtOMvnbbh5JSK6ytuoCs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useKnowledgeGraph$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnowledgeGraph"]
    ];
});
_c = KnowledgePointList;
var _c;
__turbopack_context__.k.register(_c, "KnowledgePointList");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/knowledge/KnowledgePointForm.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "KnowledgePointForm": ()=>KnowledgePointForm
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function KnowledgePointForm(param) {
    let { knowledgePoint, onSubmit, onCancel, isLoading = false } = param;
    _s();
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        name: '',
        description: '',
        difficulty_level: 1,
        tags: [],
        user_id: '' // 这个会在提交时设置
    });
    const [newTag, setNewTag] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    // 初始化表单数据
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "KnowledgePointForm.useEffect": ()=>{
            if (knowledgePoint) {
                setFormData({
                    name: knowledgePoint.name,
                    description: knowledgePoint.description || '',
                    difficulty_level: knowledgePoint.difficulty_level,
                    tags: knowledgePoint.tags || [],
                    user_id: knowledgePoint.user_id
                });
            } else {
                setFormData({
                    name: '',
                    description: '',
                    difficulty_level: 1,
                    tags: [],
                    user_id: ''
                });
            }
            setErrors({});
        }
    }["KnowledgePointForm.useEffect"], [
        knowledgePoint
    ]);
    const validateForm = ()=>{
        const newErrors = {};
        if (!formData.name.trim()) {
            newErrors.name = '知识点名称不能为空';
        }
        if (formData.difficulty_level < 1 || formData.difficulty_level > 10) {
            newErrors.difficulty_level = '难度级别必须在1-10之间';
        }
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!validateForm()) return;
        try {
            await onSubmit(formData);
        } catch (error) {
            console.error('提交表单失败:', error);
        }
    };
    const handleInputChange = (field, value)=>{
        setFormData((prev)=>({
                ...prev,
                [field]: value
            }));
        // 清除对应字段的错误
        if (errors[field]) {
            setErrors((prev)=>({
                    ...prev,
                    [field]: ''
                }));
        }
    };
    const addTag = ()=>{
        const tag = newTag.trim();
        if (tag && !formData.tags.includes(tag)) {
            handleInputChange('tags', [
                ...formData.tags,
                tag
            ]);
            setNewTag('');
        }
    };
    const removeTag = (tagToRemove)=>{
        handleInputChange('tags', formData.tags.filter((tag)=>tag !== tagToRemove));
    };
    const handleKeyPress = (e)=>{
        if (e.key === 'Enter') {
            e.preventDefault();
            addTag();
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        className: "w-full max-w-2xl mx-auto",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold text-gray-900 dark:text-white",
                            children: knowledgePoint ? '编辑知识点' : '创建新知识点'
                        }, void 0, false, {
                            fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                            lineNumber: 114,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            variant: "ghost",
                            size: "sm",
                            onClick: onCancel,
                            className: "h-8 w-8 p-0",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                lineNumber: 123,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                            lineNumber: 117,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                    lineNumber: 113,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                lineNumber: 112,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                onSubmit: handleSubmit,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                label: "知识点名称 *",
                                value: formData.name,
                                onChange: (e)=>handleInputChange('name', e.target.value),
                                error: errors.name,
                                placeholder: "输入知识点名称",
                                disabled: isLoading
                            }, void 0, false, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                lineNumber: 131,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                        children: "描述"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                        lineNumber: 142,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                        value: formData.description,
                                        onChange: (e)=>handleInputChange('description', e.target.value),
                                        placeholder: "描述这个知识点的内容和要点",
                                        rows: 3,
                                        disabled: isLoading,
                                        className: "block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder:text-gray-400 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-500 dark:focus:border-indigo-400 dark:focus:ring-indigo-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                        lineNumber: 145,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                lineNumber: 141,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                        children: "难度级别 *"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                        lineNumber: 157,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center space-x-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "range",
                                                min: "1",
                                                max: "10",
                                                value: formData.difficulty_level,
                                                onChange: (e)=>handleInputChange('difficulty_level', parseInt(e.target.value)),
                                                disabled: isLoading,
                                                className: "flex-1"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                lineNumber: 161,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-sm font-medium text-gray-700 dark:text-gray-300 min-w-[3rem]",
                                                children: [
                                                    formData.difficulty_level,
                                                    "/10"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                lineNumber: 170,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                        lineNumber: 160,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "简单"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                lineNumber: 175,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "中等"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                lineNumber: 176,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "困难"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                lineNumber: 177,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                        lineNumber: 174,
                                        columnNumber: 13
                                    }, this),
                                    errors.difficulty_level && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                        children: errors.difficulty_level
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                        lineNumber: 180,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                lineNumber: 156,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                        children: "标签"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                        lineNumber: 188,
                                        columnNumber: 13
                                    }, this),
                                    formData.tags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-wrap gap-2 mb-2",
                                        children: formData.tags.map((tag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "inline-flex items-center px-2 py-1 rounded-md text-xs bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
                                                children: [
                                                    tag,
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        type: "button",
                                                        onClick: ()=>removeTag(tag),
                                                        disabled: isLoading,
                                                        className: "ml-1 text-indigo-600 hover:text-indigo-800 dark:text-indigo-300 dark:hover:text-indigo-100",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                                            className: "h-3 w-3"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                            lineNumber: 207,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                        lineNumber: 201,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, index, true, {
                                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                lineNumber: 196,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                        lineNumber: 194,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                value: newTag,
                                                onChange: (e)=>setNewTag(e.target.value),
                                                onKeyPress: handleKeyPress,
                                                placeholder: "输入标签名称",
                                                disabled: isLoading,
                                                className: "flex-1"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                lineNumber: 216,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                type: "button",
                                                variant: "outline",
                                                size: "sm",
                                                onClick: addTag,
                                                disabled: !newTag.trim() || isLoading,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                    className: "h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                    lineNumber: 231,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                                lineNumber: 224,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                        lineNumber: 215,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-1 text-xs text-gray-500 dark:text-gray-400",
                                        children: "按回车键或点击加号添加标签"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                        lineNumber: 234,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                lineNumber: 187,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                        lineNumber: 129,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardFooter"], {
                        className: "flex justify-end space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                type: "button",
                                variant: "outline",
                                onClick: onCancel,
                                disabled: isLoading,
                                children: "取消"
                            }, void 0, false, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                lineNumber: 241,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                type: "submit",
                                isLoading: isLoading,
                                disabled: isLoading,
                                children: knowledgePoint ? '更新' : '创建'
                            }, void 0, false, {
                                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                                lineNumber: 249,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                        lineNumber: 240,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
                lineNumber: 128,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/knowledge/KnowledgePointForm.tsx",
        lineNumber: 111,
        columnNumber: 5
    }, this);
}
_s(KnowledgePointForm, "EBmkSD5tHW9/mhP0NofBH303Fd0=");
_c = KnowledgePointForm;
var _c;
__turbopack_context__.k.register(_c, "KnowledgePointForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/auth.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthService": ()=>AuthService,
    "authService": ()=>authService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
class AuthService {
    /**
   * 用户注册
   */ async signUp(email, password, metadata) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            // 开发模式下的模拟注册 - 直接创建会话
            const mockUser = {
                id: 'mock-user-' + Date.now(),
                email,
                user_metadata: metadata || {},
                created_at: new Date().toISOString()
            };
            return {
                data: {
                    user: mockUser,
                    session: {
                        access_token: 'mock-token',
                        refresh_token: 'mock-refresh',
                        expires_in: 3600,
                        expires_at: Math.floor(Date.now() / 1000) + 3600,
                        token_type: 'bearer',
                        user: mockUser
                    }
                },
                error: null
            };
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signUp({
            email,
            password,
            options: {
                data: metadata || {}
            }
        });
        return {
            data,
            error
        };
    }
    /**
   * 用户登录
   */ async signIn(email, password) {
        console.log('AuthService.signIn 被调用:', email);
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            console.log('开发模式：使用模拟登录');
            // 开发模式下的模拟登录
            const mockUser = {
                id: 'mock-user',
                email,
                user_metadata: {
                    name: '测试用户'
                },
                created_at: new Date().toISOString()
            };
            const result = {
                data: {
                    user: mockUser,
                    session: {
                        access_token: 'mock-token',
                        refresh_token: 'mock-refresh',
                        expires_in: 3600,
                        expires_at: Math.floor(Date.now() / 1000) + 3600,
                        token_type: 'bearer',
                        user: mockUser
                    }
                },
                error: null
            };
            console.log('模拟登录结果:', result);
            return result;
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signInWithPassword({
            email,
            password
        });
        return {
            data,
            error
        };
    }
    /**
   * 第三方登录 (Google)
   */ async signInWithGoogle() {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            // 开发模式下不支持第三方登录
            return {
                data: null,
                error: {
                    message: '开发模式下不支持第三方登录'
                }
            };
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signInWithOAuth({
            provider: 'google',
            options: {
                redirectTo: "".concat(window.location.origin, "/auth/callback")
            }
        });
        return {
            data,
            error
        };
    }
    /**
   * 用户登出
   */ async signOut() {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            // 开发模式下的模拟登出
            return {
                error: null
            };
        }
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signOut();
        return {
            error
        };
    }
    /**
   * 获取当前用户
   */ async getCurrentUser() {
        var _user_user_metadata, _user_email, _user_user_metadata1;
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            // 开发模式下检查localStorage中的用户信息
            try {
                const authStorage = localStorage.getItem('auth-storage');
                if (authStorage) {
                    var _parsed_state;
                    const parsed = JSON.parse(authStorage);
                    if ((_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.user) {
                        return parsed.state.user;
                    }
                }
            } catch (error) {
                console.warn('获取本地用户信息失败:', error);
            }
            return null;
        }
        const { data: { user }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.getUser();
        if (error || !user) {
            return null;
        }
        return {
            id: user.id,
            email: user.email || '',
            name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.name) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]),
            avatar_url: (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.avatar_url,
            created_at: user.created_at || new Date().toISOString()
        };
    }
    /**
   * 获取当前会话
   */ async getCurrentSession() {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            // 开发模式下检查是否有有效的用户会话
            try {
                const authStorage = localStorage.getItem('auth-storage');
                if (authStorage) {
                    var _parsed_state;
                    const parsed = JSON.parse(authStorage);
                    if ((_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.user) {
                        // 返回模拟会话
                        return {
                            access_token: 'mock-token',
                            refresh_token: 'mock-refresh',
                            expires_in: 3600,
                            expires_at: Math.floor(Date.now() / 1000) + 3600,
                            token_type: 'bearer',
                            user: parsed.state.user
                        };
                    }
                }
            } catch (error) {
                console.warn('获取本地会话信息失败:', error);
            }
            return null;
        }
        const { data: { session }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
        if (error) {
            console.error('获取会话失败:', error);
            return null;
        }
        return session;
    }
    /**
   * 刷新会话
   */ async refreshSession() {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            return {
                data: null,
                error: null
            };
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.refreshSession();
        return {
            data,
            error
        };
    }
    /**
   * 更新用户信息
   */ async updateUser(updates) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            // 开发模式下的模拟更新
            return {
                data: {
                    user: {
                        id: 'mock-user',
                        email: updates.email || '<EMAIL>',
                        user_metadata: updates.data || {
                            name: '测试用户'
                        }
                    }
                },
                error: null
            };
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.updateUser(updates);
        return {
            data,
            error
        };
    }
    /**
   * 重置密码
   */ async resetPassword(email) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            // 开发模式下的模拟重置
            return {
                data: null,
                error: null
            };
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.resetPasswordForEmail(email, {
            redirectTo: "".concat(window.location.origin, "/auth/reset-password")
        });
        return {
            data,
            error
        };
    }
    /**
   * 监听认证状态变化
   */ onAuthStateChange(callback) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            // 开发模式下不监听状态变化
            return {
                data: {
                    subscription: {
                        unsubscribe: ()=>{}
                    }
                }
            };
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.onAuthStateChange(callback);
    }
}
const authService = new AuthService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/useAuthStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAuthStore": ()=>useAuthStore
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
const useAuthStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        user: null,
        session: null,
        loading: true,
        // Actions
        setUser: (user)=>set({
                user
            }),
        setSession: (session)=>set({
                session
            }),
        setLoading: (loading)=>set({
                loading
            }),
        clearAuth: ()=>set({
                user: null,
                session: null,
                loading: false
            })
    }), {
    name: 'auth-storage',
    // 只持久化用户信息，不持久化session（安全考虑）
    partialize: (state)=>({
            user: state.user,
            loading: false // 重新加载时不保持loading状态
        })
}));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useAuth.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAuth": ()=>useAuth
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useAuthStore.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
;
;
function useAuth() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user, session, loading, setUser, setSession, setLoading, clearAuth } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])();
    // 计算认证状态
    const isAuthenticated = !!user;
    // 调试信息 (仅在开发模式下)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAuth.useEffect": ()=>{
            if ("TURBOPACK compile-time truthy", 1) {
                console.log('useAuth 状态变化:', {
                    user: !!user,
                    session: !!session,
                    isAuthenticated,
                    loading
                });
            }
        }
    }["useAuth.useEffect"], [
        user,
        session,
        isAuthenticated,
        loading
    ]);
    /**
   * 初始化认证状态
   */ const initializeAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuth.useCallback[initializeAuth]": async ()=>{
            setLoading(true);
            try {
                const [currentUser, currentSession] = await Promise.all([
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getCurrentUser(),
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getCurrentSession()
                ]);
                setUser(currentUser);
                setSession(currentSession);
            } catch (error) {
                console.error('初始化认证状态失败:', error);
                clearAuth();
            } finally{
                setLoading(false);
            }
        }
    }["useAuth.useCallback[initializeAuth]"], [
        setUser,
        setSession,
        setLoading,
        clearAuth
    ]);
    /**
   * 用户登录
   */ const signIn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuth.useCallback[signIn]": async (email, password)=>{
            setLoading(true);
            try {
                const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].signIn(email, password);
                if (error) {
                    throw new Error(error.message);
                }
                if (data.user && data.session) {
                    var _data_user_user_metadata, _data_user_email, _data_user_user_metadata1;
                    const authUser = {
                        id: data.user.id,
                        email: data.user.email || '',
                        name: ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.name) || ((_data_user_email = data.user.email) === null || _data_user_email === void 0 ? void 0 : _data_user_email.split('@')[0]),
                        avatar_url: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.avatar_url,
                        created_at: data.user.created_at || new Date().toISOString()
                    };
                    console.log('设置用户状态:', authUser);
                    setUser(authUser);
                    setSession(data.session);
                    console.log('用户状态已设置');
                    return {
                        success: true
                    };
                }
                throw new Error('登录失败');
            } catch (error) {
                console.error('登录失败:', error);
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '登录失败'
                };
            } finally{
                setLoading(false);
            }
        }
    }["useAuth.useCallback[signIn]"], [
        setUser,
        setSession,
        setLoading
    ]);
    /**
   * 用户注册
   */ const signUp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuth.useCallback[signUp]": async (email, password, name)=>{
            setLoading(true);
            try {
                const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].signUp(email, password, {
                    name
                });
                if (error) {
                    throw new Error(error.message);
                }
                // 注册成功，如果有session则直接登录
                if (data.session && data.user) {
                    var _data_user_user_metadata, _data_user_email, _data_user_user_metadata1;
                    const authUser = {
                        id: data.user.id,
                        email: data.user.email || '',
                        name: ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.name) || ((_data_user_email = data.user.email) === null || _data_user_email === void 0 ? void 0 : _data_user_email.split('@')[0]),
                        avatar_url: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.avatar_url,
                        created_at: data.user.created_at || new Date().toISOString()
                    };
                    setUser(authUser);
                    setSession(data.session);
                    return {
                        success: true,
                        needsVerification: false,
                        message: '注册成功'
                    };
                }
                // 需要邮箱验证
                return {
                    success: true,
                    needsVerification: true,
                    message: '请检查邮箱并点击验证链接'
                };
            } catch (error) {
                console.error('注册失败:', error);
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '注册失败'
                };
            } finally{
                setLoading(false);
            }
        }
    }["useAuth.useCallback[signUp]"], [
        setLoading
    ]);
    /**
   * 第三方登录
   */ const signInWithGoogle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuth.useCallback[signInWithGoogle]": async ()=>{
            setLoading(true);
            try {
                const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].signInWithGoogle();
                if (error) {
                    throw new Error(error.message);
                }
                // OAuth登录会重定向，这里不需要处理结果
                return {
                    success: true
                };
            } catch (error) {
                console.error('Google登录失败:', error);
                setLoading(false);
                return {
                    success: false,
                    error: error instanceof Error ? error.message : 'Google登录失败'
                };
            }
        }
    }["useAuth.useCallback[signInWithGoogle]"], [
        setLoading
    ]);
    /**
   * 用户登出
   */ const signOut = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuth.useCallback[signOut]": async ()=>{
            setLoading(true);
            try {
                const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].signOut();
                if (error) {
                    throw new Error(error.message);
                }
                clearAuth();
                router.push('/auth/signin');
                return {
                    success: true
                };
            } catch (error) {
                console.error('登出失败:', error);
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '登出失败'
                };
            } finally{
                setLoading(false);
            }
        }
    }["useAuth.useCallback[signOut]"], [
        clearAuth,
        router,
        setLoading
    ]);
    /**
   * 更新用户信息
   */ const updateProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuth.useCallback[updateProfile]": async (updates)=>{
            if (!user) return {
                success: false,
                error: '用户未登录'
            };
            setLoading(true);
            try {
                const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].updateUser({
                    email: updates.email,
                    data: {
                        name: updates.name,
                        avatar_url: updates.avatar_url
                    }
                });
                if (error) {
                    throw new Error(error.message);
                }
                if (data.user) {
                    var _data_user_user_metadata, _data_user_user_metadata1;
                    const updatedUser = {
                        ...user,
                        email: data.user.email || user.email,
                        name: ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.name) || user.name,
                        avatar_url: ((_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.avatar_url) || user.avatar_url
                    };
                    setUser(updatedUser);
                }
                return {
                    success: true
                };
            } catch (error) {
                console.error('更新用户信息失败:', error);
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '更新失败'
                };
            } finally{
                setLoading(false);
            }
        }
    }["useAuth.useCallback[updateProfile]"], [
        user,
        setUser,
        setLoading
    ]);
    /**
   * 重置密码
   */ const resetPassword = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuth.useCallback[resetPassword]": async (email)=>{
            setLoading(true);
            try {
                const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].resetPassword(email);
                if (error) {
                    throw new Error(error.message);
                }
                return {
                    success: true,
                    message: '密码重置邮件已发送，请检查邮箱'
                };
            } catch (error) {
                console.error('重置密码失败:', error);
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '重置密码失败'
                };
            } finally{
                setLoading(false);
            }
        }
    }["useAuth.useCallback[resetPassword]"], [
        setLoading
    ]);
    // 监听认证状态变化
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAuth.useEffect": ()=>{
            const { data: { subscription } } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].onAuthStateChange({
                "useAuth.useEffect": async (event, session)=>{
                    console.log('认证状态变化:', event, session);
                    if (event === 'SIGNED_IN' && session) {
                        var _session_user_user_metadata, _session_user_email, _session_user_user_metadata1;
                        const authUser = {
                            id: session.user.id,
                            email: session.user.email || '',
                            name: ((_session_user_user_metadata = session.user.user_metadata) === null || _session_user_user_metadata === void 0 ? void 0 : _session_user_user_metadata.name) || ((_session_user_email = session.user.email) === null || _session_user_email === void 0 ? void 0 : _session_user_email.split('@')[0]),
                            avatar_url: (_session_user_user_metadata1 = session.user.user_metadata) === null || _session_user_user_metadata1 === void 0 ? void 0 : _session_user_user_metadata1.avatar_url,
                            created_at: session.user.created_at || new Date().toISOString()
                        };
                        setUser(authUser);
                        setSession(session);
                    } else if (event === 'SIGNED_OUT') {
                        clearAuth();
                    }
                    setLoading(false);
                }
            }["useAuth.useEffect"]);
            // 初始化认证状态
            initializeAuth();
            return ({
                "useAuth.useEffect": ()=>{
                    subscription.unsubscribe();
                }
            })["useAuth.useEffect"];
        }
    }["useAuth.useEffect"], [
        initializeAuth,
        setUser,
        setSession,
        clearAuth,
        setLoading
    ]);
    return {
        // State
        user,
        session,
        loading,
        isAuthenticated,
        // Actions
        signIn,
        signUp,
        signInWithGoogle,
        signOut,
        updateProfile,
        resetPassword,
        // Utils
        initializeAuth
    };
}
_s(useAuth, "tS3K3sdgDwPHXHaSZLK+J56dx80=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/auth/AuthGuard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthGuard": ()=>AuthGuard,
    "GuestGuard": ()=>GuestGuard
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useAuth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brain$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brain$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brain.js [app-client] (ecmascript) <export default as Brain>");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function AuthGuard(param) {
    let { children, fallback, redirectTo = '/auth/signin' } = param;
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { isAuthenticated, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthGuard.useEffect": ()=>{
            // 如果加载完成且用户未认证，重定向到登录页
            if (!loading && !isAuthenticated) {
                router.push(redirectTo);
            }
        }
    }["AuthGuard.useEffect"], [
        loading,
        isAuthenticated,
        router,
        redirectTo
    ]);
    // 显示加载状态
    if (loading) {
        return fallback || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brain$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brain$3e$__["Brain"], {
                        className: "w-12 h-12 text-indigo-600 mx-auto mb-4 animate-pulse"
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/AuthGuard.tsx",
                        lineNumber: 38,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold text-gray-900 dark:text-white mb-2",
                        children: "Learn Everything"
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/AuthGuard.tsx",
                        lineNumber: 39,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 dark:text-gray-400",
                        children: "正在验证身份..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/AuthGuard.tsx",
                        lineNumber: 42,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"
                        }, void 0, false, {
                            fileName: "[project]/src/components/auth/AuthGuard.tsx",
                            lineNumber: 46,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/AuthGuard.tsx",
                        lineNumber: 45,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/auth/AuthGuard.tsx",
                lineNumber: 37,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/auth/AuthGuard.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, this);
    }
    // 如果用户未认证，显示空内容（即将重定向）
    if (!isAuthenticated) {
        return null;
    }
    // 用户已认证，显示受保护的内容
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(AuthGuard, "gWJ89pc545Cl+2Ljpu5b9kbtsR0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = AuthGuard;
function GuestGuard(param) {
    let { children, redirectTo = '/knowledge' } = param;
    _s1();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { isAuthenticated, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GuestGuard.useEffect": ()=>{
            console.log('GuestGuard状态:', {
                loading,
                isAuthenticated
            });
            // 如果加载完成且用户已认证，重定向到指定页面
            if (!loading && isAuthenticated) {
                console.log('GuestGuard: 用户已认证，重定向到', redirectTo);
                router.push(redirectTo);
            }
        }
    }["GuestGuard.useEffect"], [
        loading,
        isAuthenticated,
        router,
        redirectTo
    ]);
    // 显示加载状态
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brain$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brain$3e$__["Brain"], {
                        className: "w-12 h-12 text-indigo-600 mx-auto mb-4 animate-pulse"
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/AuthGuard.tsx",
                        lineNumber: 90,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 dark:text-gray-400",
                        children: "正在加载..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/AuthGuard.tsx",
                        lineNumber: 91,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/auth/AuthGuard.tsx",
                lineNumber: 89,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/auth/AuthGuard.tsx",
            lineNumber: 88,
            columnNumber: 7
        }, this);
    }
    // 如果用户已认证，显示空内容（即将重定向）
    if (isAuthenticated) {
        return null;
    }
    // 用户未认证，显示内容
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s1(GuestGuard, "gWJ89pc545Cl+2Ljpu5b9kbtsR0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c1 = GuestGuard;
var _c, _c1;
__turbopack_context__.k.register(_c, "AuthGuard");
__turbopack_context__.k.register(_c1, "GuestGuard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/knowledge/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>KnowledgePage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$knowledge$2f$KnowledgePointList$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/knowledge/KnowledgePointList.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$knowledge$2f$KnowledgePointForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/knowledge/KnowledgePointForm.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$AuthGuard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/auth/AuthGuard.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useKnowledgeGraph$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useKnowledgeGraph.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useAuth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brain$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brain$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brain.js [app-client] (ecmascript) <export default as Brain>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/log-out.js [app-client] (ecmascript) <export default as LogOut>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-client] (ecmascript) <export default as Zap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
function KnowledgePage() {
    _s();
    const { createKnowledgePoint, updateKnowledgePoint, deleteKnowledgePoint, isLoading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useKnowledgeGraph$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnowledgeGraph"])();
    const { user, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const [viewMode, setViewMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('list');
    const [editingPoint, setEditingPoint] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const handleCreateNew = ()=>{
        setEditingPoint(null);
        setViewMode('create');
    };
    const handleEdit = (point)=>{
        setEditingPoint(point);
        setViewMode('edit');
    };
    const handleDelete = async (id)=>{
        if (window.confirm('确定要删除这个知识点吗？这将同时删除相关的依赖关系和学习记录。')) {
            const result = await deleteKnowledgePoint(id);
            if (!result.success && result.error) {
                alert(result.error);
            }
        }
    };
    const handleFormSubmit = async (data)=>{
        try {
            // 使用当前登录用户的ID
            const formDataWithUser = {
                ...data,
                user_id: (user === null || user === void 0 ? void 0 : user.id) || 'mock-user'
            };
            let result;
            if (viewMode === 'edit' && editingPoint) {
                result = await updateKnowledgePoint(editingPoint.id, formDataWithUser);
            } else {
                result = await createKnowledgePoint(formDataWithUser);
            }
            if (result.success) {
                setViewMode('list');
                setEditingPoint(null);
            } else {
                alert(result.error || '操作失败');
            }
        } catch (error) {
            console.error('表单提交失败:', error);
            alert('操作失败，请重试');
        }
    };
    const handleCancel = ()=>{
        setViewMode('list');
        setEditingPoint(null);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$AuthGuard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthGuard"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 dark:bg-gray-900",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "container mx-auto px-4 py-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        viewMode !== 'list' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "ghost",
                                            size: "sm",
                                            onClick: handleCancel,
                                            className: "mr-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                                    className: "h-4 w-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                                    lineNumber: 95,
                                                    columnNumber: 19
                                                }, this),
                                                "返回"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/knowledge/page.tsx",
                                            lineNumber: 89,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brain$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brain$3e$__["Brain"], {
                                                    className: "h-8 w-8 text-indigo-600 mr-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                                    lineNumber: 100,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                            className: "text-3xl font-bold text-gray-900 dark:text-white",
                                                            children: [
                                                                viewMode === 'list' && '知识管理',
                                                                viewMode === 'create' && '创建知识点',
                                                                viewMode === 'edit' && '编辑知识点'
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/knowledge/page.tsx",
                                                            lineNumber: 102,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-gray-600 dark:text-gray-400 mt-1",
                                                            children: [
                                                                viewMode === 'list' && '管理你的知识点和学习进度',
                                                                viewMode === 'create' && '添加新的知识点到你的学习体系',
                                                                viewMode === 'edit' && '修改知识点信息'
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/knowledge/page.tsx",
                                                            lineNumber: 107,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                                    lineNumber: 101,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/knowledge/page.tsx",
                                            lineNumber: 99,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                    lineNumber: 87,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/adaptive",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                variant: "outline",
                                                size: "sm",
                                                className: "text-indigo-600 border-indigo-600 hover:bg-indigo-50",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"], {
                                                        className: "h-4 w-4 mr-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/knowledge/page.tsx",
                                                        lineNumber: 124,
                                                        columnNumber: 19
                                                    }, this),
                                                    "AI自适应学习"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/knowledge/page.tsx",
                                                lineNumber: 119,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/knowledge/page.tsx",
                                            lineNumber: 118,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                                    className: "h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                                    lineNumber: 129,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: [
                                                        "欢迎，",
                                                        (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.email)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                                    lineNumber: 130,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/knowledge/page.tsx",
                                            lineNumber: 128,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "ghost",
                                            size: "sm",
                                            onClick: signOut,
                                            className: "text-gray-600 hover:text-gray-800",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__["LogOut"], {
                                                    className: "h-4 w-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                                    lineNumber: 138,
                                                    columnNumber: 17
                                                }, this),
                                                "登出"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/knowledge/page.tsx",
                                            lineNumber: 132,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                    lineNumber: 117,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/knowledge/page.tsx",
                            lineNumber: 86,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/knowledge/page.tsx",
                        lineNumber: 85,
                        columnNumber: 9
                    }, this),
                    error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-red-800",
                            children: error
                        }, void 0, false, {
                            fileName: "[project]/src/app/knowledge/page.tsx",
                            lineNumber: 148,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/knowledge/page.tsx",
                        lineNumber: 147,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-w-7xl mx-auto",
                        children: [
                            viewMode === 'list' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$knowledge$2f$KnowledgePointList$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KnowledgePointList"], {
                                onCreateNew: handleCreateNew,
                                onEditPoint: handleEdit,
                                onDeletePoint: handleDelete,
                                showActions: true
                            }, void 0, false, {
                                fileName: "[project]/src/app/knowledge/page.tsx",
                                lineNumber: 155,
                                columnNumber: 13
                            }, this),
                            (viewMode === 'create' || viewMode === 'edit') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$knowledge$2f$KnowledgePointForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KnowledgePointForm"], {
                                    knowledgePoint: editingPoint,
                                    onSubmit: handleFormSubmit,
                                    onCancel: handleCancel,
                                    isLoading: isLoading
                                }, void 0, false, {
                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                    lineNumber: 165,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/knowledge/page.tsx",
                                lineNumber: 164,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/knowledge/page.tsx",
                        lineNumber: 153,
                        columnNumber: 9
                    }, this),
                    viewMode === 'list' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-12 text-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brain$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brain$3e$__["Brain"], {
                                    className: "h-4 w-4 text-indigo-600 mr-2"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                    lineNumber: 179,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                    children: "知识图谱核心功能已就绪 - 可以开始创建和管理知识点"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/knowledge/page.tsx",
                                    lineNumber: 180,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/knowledge/page.tsx",
                            lineNumber: 178,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/knowledge/page.tsx",
                        lineNumber: 177,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/knowledge/page.tsx",
                lineNumber: 83,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/knowledge/page.tsx",
            lineNumber: 82,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/knowledge/page.tsx",
        lineNumber: 81,
        columnNumber: 5
    }, this);
}
_s(KnowledgePage, "ctwxHqsmXJiAEOYptVxW9/1ndeI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useKnowledgeGraph$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnowledgeGraph"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = KnowledgePage;
var _c;
__turbopack_context__.k.register(_c, "KnowledgePage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_4be3fe2b._.js.map