{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { clsx } from 'clsx'\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={clsx(\n        'rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={clsx('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={clsx('p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={clsx('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\n\nCard.displayName = 'Card'\nCardHeader.displayName = 'CardHeader'\nCardContent.displayName = 'CardContent'\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardContent, CardFooter }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAkBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,8FACA;QAED,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,iCAAiC;QAChD,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAC3B,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,8BAA8B;QAC7C,GAAG,KAAK;kBAER;;;;;;;;AAKP,KAAK,WAAW,GAAG;AACnB,WAAW,WAAW,GAAG;AACzB,YAAY,WAAW,GAAG;AAC1B,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { clsx } from 'clsx'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    isLoading = false,\n    disabled,\n    children, \n    ...props \n  }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variantClasses = {\n      primary: 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'\n    }\n    \n    const sizeClasses = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base'\n    }\n    \n    return (\n      <button\n        className={clsx(\n          baseClasses,\n          variantClasses[variant],\n          sizeClasses[size],\n          className\n        )}\n        disabled={disabled || isLoading}\n        ref={ref}\n        {...props}\n      >\n        {isLoading && (\n          <svg \n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\" \n            xmlns=\"http://www.w3.org/2000/svg\" \n            fill=\"none\" \n            viewBox=\"0 0 24 24\"\n          >\n            <circle \n              className=\"opacity-25\" \n              cx=\"12\" \n              cy=\"12\" \n              r=\"10\" \n              stroke=\"currentColor\" \n              strokeWidth=\"4\"\n            />\n            <path \n              className=\"opacity-75\" \n              fill=\"currentColor\" \n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAQG;QARF,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,2BACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/components/knowledge/KnowledgePointCard.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, CardFooter } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { KnowledgePoint } from '@/types'\nimport { BookOpen, Edit, Trash2, Target } from 'lucide-react'\n\ninterface KnowledgePointCardProps {\n  knowledgePoint: KnowledgePoint\n  masteryLevel?: number\n  onEdit?: (knowledgePoint: KnowledgePoint) => void\n  onDelete?: (id: string) => void\n  onSelect?: (knowledgePoint: KnowledgePoint) => void\n  isSelected?: boolean\n  showActions?: boolean\n}\n\nexport function KnowledgePointCard({\n  knowledgePoint,\n  masteryLevel = 0,\n  onEdit,\n  onDelete,\n  onSelect,\n  isSelected = false,\n  showActions = true\n}: KnowledgePointCardProps) {\n  \n  const getDifficultyColor = (level: number) => {\n    if (level <= 3) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    if (level <= 6) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n  }\n  \n  const getMasteryColor = (level: number) => {\n    if (level >= 80) return 'bg-green-500'\n    if (level >= 60) return 'bg-yellow-500'\n    if (level >= 40) return 'bg-orange-500'\n    return 'bg-red-500'\n  }\n  \n  const getDifficultyLabel = (level: number) => {\n    if (level <= 3) return '简单'\n    if (level <= 6) return '中等'\n    return '困难'\n  }\n\n  return (\n    <Card \n      className={`transition-all duration-200 hover:shadow-md cursor-pointer ${\n        isSelected ? 'ring-2 ring-indigo-500 border-indigo-500' : ''\n      }`}\n      onClick={() => onSelect?.(knowledgePoint)}\n    >\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <h3 className=\"font-semibold text-lg text-gray-900 dark:text-white mb-2\">\n              {knowledgePoint.name}\n            </h3>\n            <div className=\"flex items-center gap-2 mb-2\">\n              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(knowledgePoint.difficulty_level)}`}>\n                <Target className=\"w-3 h-3 mr-1\" />\n                {getDifficultyLabel(knowledgePoint.difficulty_level)}\n              </span>\n              <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                难度 {knowledgePoint.difficulty_level}/10\n              </span>\n            </div>\n          </div>\n          {showActions && (\n            <div className=\"flex items-center gap-1 ml-2\">\n              {onEdit && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={(e) => {\n                    e.stopPropagation()\n                    onEdit(knowledgePoint)\n                  }}\n                  className=\"h-8 w-8 p-0\"\n                >\n                  <Edit className=\"h-4 w-4\" />\n                </Button>\n              )}\n              {onDelete && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={(e) => {\n                    e.stopPropagation()\n                    onDelete(knowledgePoint.id)\n                  }}\n                  className=\"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50\"\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </Button>\n              )}\n            </div>\n          )}\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"pt-0\">\n        {knowledgePoint.description && (\n          <p className=\"text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2\">\n            {knowledgePoint.description}\n          </p>\n        )}\n        \n        {/* 掌握程度进度条 */}\n        <div className=\"mb-3\">\n          <div className=\"flex items-center justify-between mb-1\">\n            <span className=\"text-xs font-medium text-gray-700 dark:text-gray-300\">\n              掌握程度\n            </span>\n            <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n              {masteryLevel}%\n            </span>\n          </div>\n          <div className=\"w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700\">\n            <div \n              className={`h-2 rounded-full transition-all duration-300 ${getMasteryColor(masteryLevel)}`}\n              style={{ width: `${masteryLevel}%` }}\n            />\n          </div>\n        </div>\n        \n        {/* 标签 */}\n        {knowledgePoint.tags && knowledgePoint.tags.length > 0 && (\n          <div className=\"flex flex-wrap gap-1\">\n            {knowledgePoint.tags.slice(0, 3).map((tag, index) => (\n              <span \n                key={index}\n                className=\"inline-flex items-center px-2 py-1 rounded-md text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300\"\n              >\n                {tag}\n              </span>\n            ))}\n            {knowledgePoint.tags.length > 3 && (\n              <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                +{knowledgePoint.tags.length - 3} 更多\n              </span>\n            )}\n          </div>\n        )}\n      </CardContent>\n      \n      <CardFooter className=\"pt-0\">\n        <div className=\"flex items-center justify-between w-full text-xs text-gray-500 dark:text-gray-400\">\n          <div className=\"flex items-center\">\n            <BookOpen className=\"w-3 h-3 mr-1\" />\n            创建于 {new Date(knowledgePoint.created_at).toLocaleDateString()}\n          </div>\n          {masteryLevel >= 80 && (\n            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\n              已掌握\n            </span>\n          )}\n        </div>\n      </CardFooter>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AAAA;AAAA;AAAA;AANA;;;;;AAkBO,SAAS,mBAAmB,KAQT;QARS,EACjC,cAAc,EACd,eAAe,CAAC,EAChB,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,aAAa,KAAK,EAClB,cAAc,IAAI,EACM,GARS;IAUjC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,WAAW,AAAC,8DAEX,OADC,aAAa,6CAA6C;QAE5D,SAAS,IAAM,qBAAA,+BAAA,SAAW;;0BAE1B,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,eAAe,IAAI;;;;;;8CAEtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAW,AAAC,uEAA0H,OAApD,mBAAmB,eAAe,gBAAgB;;8DACxI,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDACjB,mBAAmB,eAAe,gBAAgB;;;;;;;sDAErD,6LAAC;4CAAK,WAAU;;gDAA2C;gDACrD,eAAe,gBAAgB;gDAAC;;;;;;;;;;;;;;;;;;;wBAIzC,6BACC,6LAAC;4BAAI,WAAU;;gCACZ,wBACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,OAAO;oCACT;oCACA,WAAU;8CAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;gCAGnB,0BACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,SAAS,eAAe,EAAE;oCAC5B;oCACA,WAAU;8CAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB,eAAe,WAAW,kBACzB,6LAAC;wBAAE,WAAU;kCACV,eAAe,WAAW;;;;;;kCAK/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,6LAAC;wCAAK,WAAU;;4CACb;4CAAa;;;;;;;;;;;;;0CAGlB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAW,AAAC,gDAA6E,OAA9B,gBAAgB;oCAC3E,OAAO;wCAAE,OAAO,AAAC,GAAe,OAAb,cAAa;oCAAG;;;;;;;;;;;;;;;;;oBAMxC,eAAe,IAAI,IAAI,eAAe,IAAI,CAAC,MAAM,GAAG,mBACnD,6LAAC;wBAAI,WAAU;;4BACZ,eAAe,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACzC,6LAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;4BAMR,eAAe,IAAI,CAAC,MAAM,GAAG,mBAC5B,6LAAC;gCAAK,WAAU;;oCAA2C;oCACvD,eAAe,IAAI,CAAC,MAAM,GAAG;oCAAE;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;gCAChC,IAAI,KAAK,eAAe,UAAU,EAAE,kBAAkB;;;;;;;wBAE5D,gBAAgB,oBACf,6LAAC;4BAAK,WAAU;sCAA4H;;;;;;;;;;;;;;;;;;;;;;;AAQxJ;KAjJgB", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react'\nimport { clsx } from 'clsx'\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  helperText?: string\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, label, error, helperText, id, ...props }, ref) => {\n    const generatedId = React.useId()\n    const inputId = id || generatedId\n    \n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label \n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <input\n          id={inputId}\n          className={clsx(\n            'block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm',\n            'placeholder:text-gray-400 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500',\n            'disabled:cursor-not-allowed disabled:opacity-50',\n            'dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-500',\n            'dark:focus:border-indigo-400 dark:focus:ring-indigo-400',\n            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n            {error}\n          </p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAQA,MAAM,sBAAQ,GAAA,6JAAA,CAAA,UAAK,CAAC,UAAU,SAC5B,QAAwD;QAAvD,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,OAAO;;IACpD,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,KAAK;IAC/B,MAAM,UAAU,MAAM;IAEtB,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBACC,IAAI;gBACJ,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,oEACA,2GACA,mDACA,wFACA,2DACA,SAAS,0DACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAGJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder_key'\n\n// 检查是否为开发环境且使用占位符配置\nconst isDevMode = process.env.NODE_ENV === 'development' && supabaseUrl.includes('placeholder')\n\n// 创建Supabase客户端，开发模式下使用模拟配置\nexport const supabase = isDevMode\n  ? null // 开发模式下返回null，API调用将被模拟\n  : createClient(supabaseUrl, supabaseAnonKey)\n\nexport type Database = {\n  public: {\n    Tables: {\n      knowledge_points: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          difficulty_level: number\n          tags: string[]\n          created_at: string\n          updated_at: string\n          user_id: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          description?: string | null\n          difficulty_level?: number\n          tags?: string[]\n          created_at?: string\n          updated_at?: string\n          user_id: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          description?: string | null\n          difficulty_level?: number\n          tags?: string[]\n          created_at?: string\n          updated_at?: string\n          user_id?: string\n        }\n      }\n      knowledge_dependencies: {\n        Row: {\n          id: string\n          prerequisite_id: string\n          dependent_id: string\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          prerequisite_id: string\n          dependent_id: string\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          prerequisite_id?: string\n          dependent_id?: string\n          created_at?: string\n        }\n      }\n      user_progress: {\n        Row: {\n          id: string\n          user_id: string\n          knowledge_point_id: string\n          mastery_level: number\n          notes: string | null\n          last_reviewed: string | null\n          test_scores: number[]\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          knowledge_point_id: string\n          mastery_level?: number\n          notes?: string | null\n          last_reviewed?: string | null\n          test_scores?: number[]\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          knowledge_point_id?: string\n          mastery_level?: number\n          notes?: string | null\n          last_reviewed?: string | null\n          test_scores?: number[]\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM,cAAc,uEAAwC;AAC5D,MAAM,kBAAkB,uDAA6C;AAErE,oBAAoB;AACpB,MAAM,YAAY,oDAAyB,iBAAiB,YAAY,QAAQ,CAAC;AAG1E,MAAM,WAAW,YACpB,KAAK,wBAAwB;GAC7B,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/database.ts"], "sourcesContent": ["import { supabase } from './supabase'\nimport { KnowledgePoint, KnowledgeDependency, UserProgress, LearningSession, TestQuestion } from '@/types'\n\n// 开发模式下的模拟数据\nconst mockKnowledgePoints: KnowledgePoint[] = [\n  {\n    id: '1',\n    name: '基础算术',\n    description: '加减乘除的基本运算',\n    difficulty_level: 1,\n    tags: ['数学', '基础'],\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString(),\n    user_id: 'mock-user'\n  },\n  {\n    id: '2',\n    name: 'JavaScript基础',\n    description: 'JavaScript语法和基本概念',\n    difficulty_level: 3,\n    tags: ['编程', 'JavaScript'],\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString(),\n    user_id: 'mock-user'\n  }\n]\n\nconst mockDependencies: KnowledgeDependency[] = []\nconst mockProgress: UserProgress[] = []\n\n// 检查是否为开发模式\nconst isDevMode = !supabase\n\n// Knowledge Points API\nexport const knowledgePointsAPI = {\n  // Get all knowledge points for current user\n  async getAll(): Promise<KnowledgePoint[]> {\n    if (isDevMode) {\n      return Promise.resolve([...mockKnowledgePoints])\n    }\n\n    const { data, error } = await supabase!\n      .from('knowledge_points')\n      .select('*')\n      .order('created_at', { ascending: true })\n\n    if (error) throw error\n    return data || []\n  },\n\n  // Get knowledge point by ID\n  async getById(id: string): Promise<KnowledgePoint | null> {\n    const { data, error } = await supabase\n      .from('knowledge_points')\n      .select('*')\n      .eq('id', id)\n      .single()\n    \n    if (error) throw error\n    return data\n  },\n\n  // Create new knowledge point\n  async create(knowledgePoint: Omit<KnowledgePoint, 'id' | 'created_at' | 'updated_at'>): Promise<KnowledgePoint> {\n    if (isDevMode) {\n      const newPoint: KnowledgePoint = {\n        ...knowledgePoint,\n        id: Date.now().toString(),\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n      mockKnowledgePoints.push(newPoint)\n      return Promise.resolve(newPoint)\n    }\n\n    const { data, error } = await supabase!\n      .from('knowledge_points')\n      .insert(knowledgePoint)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data\n  },\n\n  // Update knowledge point\n  async update(id: string, updates: Partial<KnowledgePoint>): Promise<KnowledgePoint> {\n    if (isDevMode) {\n      const index = mockKnowledgePoints.findIndex(point => point.id === id)\n      if (index === -1) throw new Error('知识点不存在')\n\n      const updatedPoint = {\n        ...mockKnowledgePoints[index],\n        ...updates,\n        updated_at: new Date().toISOString()\n      }\n      mockKnowledgePoints[index] = updatedPoint\n      return Promise.resolve(updatedPoint)\n    }\n\n    const { data, error } = await supabase\n      .from('knowledge_points')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data\n  },\n\n  // Delete knowledge point\n  async delete(id: string): Promise<void> {\n    if (isDevMode) {\n      const index = mockKnowledgePoints.findIndex(point => point.id === id)\n      if (index === -1) throw new Error('知识点不存在')\n\n      mockKnowledgePoints.splice(index, 1)\n      // 同时删除相关的依赖关系\n      const dependencyIndexes = []\n      for (let i = mockDependencies.length - 1; i >= 0; i--) {\n        if (mockDependencies[i].prerequisite_id === id || mockDependencies[i].dependent_id === id) {\n          dependencyIndexes.push(i)\n        }\n      }\n      dependencyIndexes.forEach(index => mockDependencies.splice(index, 1))\n      return Promise.resolve()\n    }\n\n    const { error } = await supabase\n      .from('knowledge_points')\n      .delete()\n      .eq('id', id)\n\n    if (error) throw error\n  },\n\n  // Search knowledge points by name or tags\n  async search(query: string): Promise<KnowledgePoint[]> {\n    const { data, error } = await supabase\n      .from('knowledge_points')\n      .select('*')\n      .or(`name.ilike.%${query}%, description.ilike.%${query}%`)\n      .order('created_at', { ascending: true })\n    \n    if (error) throw error\n    return data || []\n  }\n}\n\n// Knowledge Dependencies API\nexport const dependenciesAPI = {\n  // Get all dependencies\n  async getAll(): Promise<KnowledgeDependency[]> {\n    if (isDevMode) {\n      return Promise.resolve([...mockDependencies])\n    }\n\n    const { data, error } = await supabase!\n      .from('knowledge_dependencies')\n      .select('*')\n      .order('created_at', { ascending: true })\n\n    if (error) throw error\n    return data || []\n  },\n\n  // Get prerequisites for a knowledge point\n  async getPrerequisites(knowledgePointId: string): Promise<KnowledgePoint[]> {\n    const { data, error } = await supabase\n      .from('knowledge_dependencies')\n      .select(`\n        prerequisite_id,\n        knowledge_points!knowledge_dependencies_prerequisite_id_fkey(*)\n      `)\n      .eq('dependent_id', knowledgePointId)\n    \n    if (error) throw error\n    return data?.map(item => item.knowledge_points).filter(Boolean) || []\n  },\n\n  // Get dependents for a knowledge point\n  async getDependents(knowledgePointId: string): Promise<KnowledgePoint[]> {\n    const { data, error } = await supabase\n      .from('knowledge_dependencies')\n      .select(`\n        dependent_id,\n        knowledge_points!knowledge_dependencies_dependent_id_fkey(*)\n      `)\n      .eq('prerequisite_id', knowledgePointId)\n    \n    if (error) throw error\n    return data?.map(item => item.knowledge_points).filter(Boolean) || []\n  },\n\n  // Create dependency\n  async create(prerequisiteId: string, dependentId: string): Promise<KnowledgeDependency> {\n    if (isDevMode) {\n      const newDependency: KnowledgeDependency = {\n        id: Date.now().toString(),\n        prerequisite_id: prerequisiteId,\n        dependent_id: dependentId,\n        created_at: new Date().toISOString()\n      }\n      mockDependencies.push(newDependency)\n      return Promise.resolve(newDependency)\n    }\n\n    const { data, error } = await supabase!\n      .from('knowledge_dependencies')\n      .insert({\n        prerequisite_id: prerequisiteId,\n        dependent_id: dependentId\n      })\n      .select()\n      .single()\n\n    if (error) throw error\n    return data\n  },\n\n  // Delete dependency\n  async delete(prerequisiteId: string, dependentId: string): Promise<void> {\n    if (isDevMode) {\n      const index = mockDependencies.findIndex(dep =>\n        dep.prerequisite_id === prerequisiteId && dep.dependent_id === dependentId\n      )\n      if (index !== -1) {\n        mockDependencies.splice(index, 1)\n      }\n      return Promise.resolve()\n    }\n\n    const { error } = await supabase!\n      .from('knowledge_dependencies')\n      .delete()\n      .eq('prerequisite_id', prerequisiteId)\n      .eq('dependent_id', dependentId)\n\n    if (error) throw error\n  }\n}\n\n// User Progress API\nexport const progressAPI = {\n  // Get all progress for current user\n  async getAll(): Promise<UserProgress[]> {\n    if (isDevMode) {\n      return Promise.resolve([...mockProgress])\n    }\n\n    const { data, error } = await supabase!\n      .from('user_progress')\n      .select('*')\n      .order('updated_at', { ascending: false })\n\n    if (error) throw error\n    return data || []\n  },\n\n  // Get progress for specific knowledge point\n  async getByKnowledgePoint(knowledgePointId: string): Promise<UserProgress | null> {\n    const { data, error } = await supabase\n      .from('user_progress')\n      .select('*')\n      .eq('knowledge_point_id', knowledgePointId)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error // PGRST116 is \"not found\"\n    return data\n  },\n\n  // Update or create progress\n  async upsert(progress: Omit<UserProgress, 'id' | 'created_at' | 'updated_at'>): Promise<UserProgress> {\n    const { data, error } = await supabase\n      .from('user_progress')\n      .upsert(progress, {\n        onConflict: 'user_id,knowledge_point_id'\n      })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  },\n\n  // Delete progress\n  async delete(knowledgePointId: string): Promise<void> {\n    const { error } = await supabase\n      .from('user_progress')\n      .delete()\n      .eq('knowledge_point_id', knowledgePointId)\n    \n    if (error) throw error\n  }\n}\n\n// Learning Sessions API\nexport const sessionsAPI = {\n  // Get all sessions for current user\n  async getAll(): Promise<LearningSession[]> {\n    const { data, error } = await supabase\n      .from('learning_sessions')\n      .select('*')\n      .order('start_time', { ascending: false })\n    \n    if (error) throw error\n    return data || []\n  },\n\n  // Get sessions for specific knowledge point\n  async getByKnowledgePoint(knowledgePointId: string): Promise<LearningSession[]> {\n    const { data, error } = await supabase\n      .from('learning_sessions')\n      .select('*')\n      .eq('knowledge_point_id', knowledgePointId)\n      .order('start_time', { ascending: false })\n    \n    if (error) throw error\n    return data || []\n  },\n\n  // Create new session\n  async create(session: Omit<LearningSession, 'id' | 'created_at'>): Promise<LearningSession> {\n    const { data, error } = await supabase\n      .from('learning_sessions')\n      .insert(session)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  },\n\n  // Update session\n  async update(id: string, updates: Partial<LearningSession>): Promise<LearningSession> {\n    const { data, error } = await supabase\n      .from('learning_sessions')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n}\n\n// Test Questions API\nexport const questionsAPI = {\n  // Get questions for knowledge point\n  async getByKnowledgePoint(knowledgePointId: string): Promise<TestQuestion[]> {\n    const { data, error } = await supabase\n      .from('test_questions')\n      .select('*')\n      .eq('knowledge_point_id', knowledgePointId)\n      .order('difficulty', { ascending: true })\n    \n    if (error) throw error\n    return data || []\n  },\n\n  // Create new question\n  async create(question: Omit<TestQuestion, 'id' | 'created_at'>): Promise<TestQuestion> {\n    const { data, error } = await supabase\n      .from('test_questions')\n      .insert(question)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  },\n\n  // Update question\n  async update(id: string, updates: Partial<TestQuestion>): Promise<TestQuestion> {\n    const { data, error } = await supabase\n      .from('test_questions')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  },\n\n  // Delete question\n  async delete(id: string): Promise<void> {\n    const { error } = await supabase\n      .from('test_questions')\n      .delete()\n      .eq('id', id)\n    \n    if (error) throw error\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAGA,aAAa;AACb,MAAM,sBAAwC;IAC5C;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,kBAAkB;QAClB,MAAM;YAAC;YAAM;SAAK;QAClB,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY,IAAI,OAAO,WAAW;QAClC,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,kBAAkB;QAClB,MAAM;YAAC;YAAM;SAAa;QAC1B,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY,IAAI,OAAO,WAAW;QAClC,SAAS;IACX;CACD;AAED,MAAM,mBAA0C,EAAE;AAClD,MAAM,eAA+B,EAAE;AAEvC,YAAY;AACZ,MAAM,YAAY,CAAC,yHAAA,CAAA,WAAQ;AAGpB,MAAM,qBAAqB;IAChC,4CAA4C;IAC5C,MAAM;QACJ,IAAI,WAAW;YACb,OAAO,QAAQ,OAAO,CAAC;mBAAI;aAAoB;QACjD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAK;QAEzC,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,4BAA4B;IAC5B,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,6BAA6B;IAC7B,MAAM,QAAO,cAAwE;QACnF,IAAI,WAAW;YACb,MAAM,WAA2B;gBAC/B,GAAG,cAAc;gBACjB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,oBAAoB,IAAI,CAAC;YACzB,OAAO,QAAQ,OAAO,CAAC;QACzB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,gBACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,QAAO,EAAU,EAAE,OAAgC;QACvD,IAAI,WAAW;YACb,MAAM,QAAQ,oBAAoB,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YAClE,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM;YAElC,MAAM,eAAe;gBACnB,GAAG,mBAAmB,CAAC,MAAM;gBAC7B,GAAG,OAAO;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,mBAAmB,CAAC,MAAM,GAAG;YAC7B,OAAO,QAAQ,OAAO,CAAC;QACzB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,QAAO,EAAU;QACrB,IAAI,WAAW;YACb,MAAM,QAAQ,oBAAoB,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YAClE,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM;YAElC,oBAAoB,MAAM,CAAC,OAAO;YAClC,cAAc;YACd,MAAM,oBAAoB,EAAE;YAC5B,IAAK,IAAI,IAAI,iBAAiB,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;gBACrD,IAAI,gBAAgB,CAAC,EAAE,CAAC,eAAe,KAAK,MAAM,gBAAgB,CAAC,EAAE,CAAC,YAAY,KAAK,IAAI;oBACzF,kBAAkB,IAAI,CAAC;gBACzB;YACF;YACA,kBAAkB,OAAO,CAAC,CAAA,QAAS,iBAAiB,MAAM,CAAC,OAAO;YAClE,OAAO,QAAQ,OAAO;QACxB;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,oBACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,0CAA0C;IAC1C,MAAM,QAAO,KAAa;QACxB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,AAAC,eAA4C,OAA9B,OAAM,0BAA8B,OAAN,OAAM,MACtD,KAAK,CAAC,cAAc;YAAE,WAAW;QAAK;QAEzC,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF;AAGO,MAAM,kBAAkB;IAC7B,uBAAuB;IACvB,MAAM;QACJ,IAAI,WAAW;YACb,OAAO,QAAQ,OAAO,CAAC;mBAAI;aAAiB;QAC9C;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,0BACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAK;QAEzC,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,0CAA0C;IAC1C,MAAM,kBAAiB,gBAAwB;QAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,0BACL,MAAM,CAAE,+GAIR,EAAE,CAAC,gBAAgB;QAEtB,IAAI,OAAO,MAAM;QACjB,OAAO,CAAA,iBAAA,2BAAA,KAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,gBAAgB,EAAE,MAAM,CAAC,aAAY,EAAE;IACvE;IAEA,uCAAuC;IACvC,MAAM,eAAc,gBAAwB;QAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,0BACL,MAAM,CAAE,yGAIR,EAAE,CAAC,mBAAmB;QAEzB,IAAI,OAAO,MAAM;QACjB,OAAO,CAAA,iBAAA,2BAAA,KAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,gBAAgB,EAAE,MAAM,CAAC,aAAY,EAAE;IACvE;IAEA,oBAAoB;IACpB,MAAM,QAAO,cAAsB,EAAE,WAAmB;QACtD,IAAI,WAAW;YACb,MAAM,gBAAqC;gBACzC,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,iBAAiB;gBACjB,cAAc;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,iBAAiB,IAAI,CAAC;YACtB,OAAO,QAAQ,OAAO,CAAC;QACzB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,0BACL,MAAM,CAAC;YACN,iBAAiB;YACjB,cAAc;QAChB,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM,QAAO,cAAsB,EAAE,WAAmB;QACtD,IAAI,WAAW;YACb,MAAM,QAAQ,iBAAiB,SAAS,CAAC,CAAA,MACvC,IAAI,eAAe,KAAK,kBAAkB,IAAI,YAAY,KAAK;YAEjE,IAAI,UAAU,CAAC,GAAG;gBAChB,iBAAiB,MAAM,CAAC,OAAO;YACjC;YACA,OAAO,QAAQ,OAAO;QACxB;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,0BACL,MAAM,GACN,EAAE,CAAC,mBAAmB,gBACtB,EAAE,CAAC,gBAAgB;QAEtB,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM,cAAc;IACzB,oCAAoC;IACpC,MAAM;QACJ,IAAI,WAAW;YACb,OAAO,QAAQ,OAAO,CAAC;mBAAI;aAAa;QAC1C;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,4CAA4C;IAC5C,MAAM,qBAAoB,gBAAwB;QAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,sBAAsB,kBACzB,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM,MAAM,0BAA0B;;QAC9E,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,QAAO,QAAgE;QAC3E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,UAAU;YAChB,YAAY;QACd,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,QAAO,gBAAwB;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,GACN,EAAE,CAAC,sBAAsB;QAE5B,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM,cAAc;IACzB,oCAAoC;IACpC,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,4CAA4C;IAC5C,MAAM,qBAAoB,gBAAwB;QAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,sBAAsB,kBACzB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,qBAAqB;IACrB,MAAM,QAAO,OAAmD;QAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC,SACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,iBAAiB;IACjB,MAAM,QAAO,EAAU,EAAE,OAAiC;QACxD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,eAAe;IAC1B,oCAAoC;IACpC,MAAM,qBAAoB,gBAAwB;QAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,EAAE,CAAC,sBAAsB,kBACzB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAK;QAEzC,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,sBAAsB;IACtB,MAAM,QAAO,QAAiD;QAC5D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,QAAO,EAAU,EAAE,OAA8B;QACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,QAAO,EAAU;QACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;AACF", "debugId": null}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/services/knowledgeGraphService.ts"], "sourcesContent": ["import { knowledgePointsAPI, dependenciesAPI, progressAPI } from '@/lib/database'\nimport { KnowledgePoint, KnowledgeDependency, KnowledgeGraph, LearningRecommendation } from '@/types'\n\n/**\n * Knowledge Graph Service\n * 处理知识图谱的业务逻辑，确保数据一致性和业务规则\n */\nexport class KnowledgeGraphService {\n  \n  /**\n   * 验证是否会产生循环依赖\n   * 使用深度优先搜索检测环路\n   */\n  private async detectCycle(\n    newPrerequisiteId: string, \n    newDependentId: string, \n    existingDependencies: KnowledgeDependency[]\n  ): Promise<boolean> {\n    // 构建邻接表\n    const graph = new Map<string, string[]>()\n    \n    // 添加现有依赖\n    existingDependencies.forEach(dep => {\n      if (!graph.has(dep.prerequisite_id)) {\n        graph.set(dep.prerequisite_id, [])\n      }\n      graph.get(dep.prerequisite_id)!.push(dep.dependent_id)\n    })\n    \n    // 添加新的依赖\n    if (!graph.has(newPrerequisiteId)) {\n      graph.set(newPrerequisiteId, [])\n    }\n    graph.get(newPrerequisiteId)!.push(newDependentId)\n    \n    // DFS检测环路\n    const visited = new Set<string>()\n    const recursionStack = new Set<string>()\n    \n    const dfs = (node: string): boolean => {\n      if (recursionStack.has(node)) return true // 发现环路\n      if (visited.has(node)) return false\n      \n      visited.add(node)\n      recursionStack.add(node)\n      \n      const neighbors = graph.get(node) || []\n      for (const neighbor of neighbors) {\n        if (dfs(neighbor)) return true\n      }\n      \n      recursionStack.delete(node)\n      return false\n    }\n    \n    // 检查所有节点\n    for (const node of graph.keys()) {\n      if (!visited.has(node)) {\n        if (dfs(node)) return true\n      }\n    }\n    \n    return false\n  }\n  \n  /**\n   * 安全地添加依赖关系\n   * 确保不会产生循环依赖\n   */\n  async addDependency(prerequisiteId: string, dependentId: string): Promise<{\n    success: boolean\n    dependency?: KnowledgeDependency\n    error?: string\n  }> {\n    try {\n      // 验证知识点存在\n      const [prerequisite, dependent] = await Promise.all([\n        knowledgePointsAPI.getById(prerequisiteId),\n        knowledgePointsAPI.getById(dependentId)\n      ])\n      \n      if (!prerequisite) {\n        return { success: false, error: '前置知识点不存在' }\n      }\n      \n      if (!dependent) {\n        return { success: false, error: '依赖知识点不存在' }\n      }\n      \n      // 检查是否已存在该依赖\n      const existingDependencies = await dependenciesAPI.getAll()\n      const exists = existingDependencies.some(\n        dep => dep.prerequisite_id === prerequisiteId && dep.dependent_id === dependentId\n      )\n      \n      if (exists) {\n        return { success: false, error: '依赖关系已存在' }\n      }\n      \n      // 检测循环依赖\n      const hasCycle = await this.detectCycle(prerequisiteId, dependentId, existingDependencies)\n      if (hasCycle) {\n        return { success: false, error: '添加此依赖会产生循环依赖' }\n      }\n      \n      // 创建依赖关系\n      const dependency = await dependenciesAPI.create(prerequisiteId, dependentId)\n      \n      return { success: true, dependency }\n    } catch (error) {\n      console.error('添加依赖关系失败:', error)\n      return { success: false, error: '添加依赖关系失败' }\n    }\n  }\n  \n  /**\n   * 获取知识图谱数据\n   * 包含节点和边的信息\n   */\n  async getKnowledgeGraph(): Promise<KnowledgeGraph> {\n    try {\n      const [knowledgePoints, dependencies, userProgress] = await Promise.all([\n        knowledgePointsAPI.getAll(),\n        dependenciesAPI.getAll(),\n        progressAPI.getAll()\n      ])\n      \n      // 构建节点\n      const nodes = knowledgePoints.map(point => {\n        const progress = userProgress.find(p => p.knowledge_point_id === point.id)\n        return {\n          id: point.id,\n          name: point.name,\n          group: point.difficulty_level,\n          mastery_level: progress?.mastery_level || 0,\n          difficulty_level: point.difficulty_level\n        }\n      })\n      \n      // 构建边\n      const links = dependencies.map(dep => ({\n        source: dep.prerequisite_id,\n        target: dep.dependent_id\n      }))\n      \n      return { nodes, links }\n    } catch (error) {\n      console.error('获取知识图谱失败:', error)\n      return { nodes: [], links: [] }\n    }\n  }\n  \n  /**\n   * 计算拓扑排序\n   * 用于确定学习顺序\n   */\n  async getTopologicalOrder(): Promise<string[]> {\n    try {\n      const dependencies = await dependenciesAPI.getAll()\n      const knowledgePoints = await knowledgePointsAPI.getAll()\n      \n      // 构建邻接表和入度表\n      const graph = new Map<string, string[]>()\n      const inDegree = new Map<string, number>()\n      \n      // 初始化\n      knowledgePoints.forEach(point => {\n        graph.set(point.id, [])\n        inDegree.set(point.id, 0)\n      })\n      \n      // 构建图\n      dependencies.forEach(dep => {\n        graph.get(dep.prerequisite_id)!.push(dep.dependent_id)\n        inDegree.set(dep.dependent_id, (inDegree.get(dep.dependent_id) || 0) + 1)\n      })\n      \n      // Kahn算法进行拓扑排序\n      const queue: string[] = []\n      const result: string[] = []\n      \n      // 找到所有入度为0的节点\n      for (const [nodeId, degree] of inDegree.entries()) {\n        if (degree === 0) {\n          queue.push(nodeId)\n        }\n      }\n      \n      while (queue.length > 0) {\n        const current = queue.shift()!\n        result.push(current)\n        \n        // 处理当前节点的所有邻居\n        const neighbors = graph.get(current) || []\n        for (const neighbor of neighbors) {\n          const newDegree = inDegree.get(neighbor)! - 1\n          inDegree.set(neighbor, newDegree)\n          \n          if (newDegree === 0) {\n            queue.push(neighbor)\n          }\n        }\n      }\n      \n      return result\n    } catch (error) {\n      console.error('计算拓扑排序失败:', error)\n      return []\n    }\n  }\n  \n  /**\n   * 获取学习推荐\n   * 基于当前掌握情况推荐下一步学习内容\n   */\n  async getLearningRecommendations(limit: number = 5): Promise<LearningRecommendation[]> {\n    try {\n      const [knowledgePoints, dependencies, userProgress] = await Promise.all([\n        knowledgePointsAPI.getAll(),\n        dependenciesAPI.getAll(),\n        progressAPI.getAll()\n      ])\n      \n      const recommendations: LearningRecommendation[] = []\n      \n      for (const point of knowledgePoints) {\n        const progress = userProgress.find(p => p.knowledge_point_id === point.id)\n        const currentMastery = progress?.mastery_level || 0\n        \n        // 跳过已掌握的知识点 (>= 80%)\n        if (currentMastery >= 80) continue\n        \n        // 检查前置条件是否满足\n        const prerequisites = dependencies\n          .filter(dep => dep.dependent_id === point.id)\n          .map(dep => dep.prerequisite_id)\n        \n        const allPrerequisitesMet = prerequisites.every(prereqId => {\n          const prereqProgress = userProgress.find(p => p.knowledge_point_id === prereqId)\n          return (prereqProgress?.mastery_level || 0) >= 80\n        })\n        \n        if (allPrerequisitesMet) {\n          // 计算优先级\n          let priority = 100 - currentMastery // 掌握程度越低，优先级越高\n          priority += (10 - point.difficulty_level) * 5 // 难度越低，优先级越高\n          \n          // 估算学习时间（基于难度）\n          const estimatedTime = point.difficulty_level * 30 // 每个难度级别30分钟\n          \n          recommendations.push({\n            knowledge_point: point,\n            reason: prerequisites.length > 0 \n              ? `前置条件已满足，可以开始学习` \n              : `基础知识点，建议优先学习`,\n            priority,\n            estimated_time: estimatedTime\n          })\n        }\n      }\n      \n      // 按优先级排序并限制数量\n      return recommendations\n        .sort((a, b) => b.priority - a.priority)\n        .slice(0, limit)\n        \n    } catch (error) {\n      console.error('获取学习推荐失败:', error)\n      return []\n    }\n  }\n  \n  /**\n   * 删除知识点及其相关依赖\n   * 确保数据一致性\n   */\n  async deleteKnowledgePoint(id: string): Promise<{\n    success: boolean\n    error?: string\n  }> {\n    try {\n      // 检查是否有其他知识点依赖于此知识点\n      const dependencies = await dependenciesAPI.getAll()\n      const hasDependents = dependencies.some(dep => dep.prerequisite_id === id)\n      \n      if (hasDependents) {\n        return { \n          success: false, \n          error: '无法删除：其他知识点依赖于此知识点，请先移除相关依赖关系' \n        }\n      }\n      \n      // 删除知识点（数据库外键约束会自动删除相关的依赖关系和进度记录）\n      await knowledgePointsAPI.delete(id)\n      \n      return { success: true }\n    } catch (error) {\n      console.error('删除知识点失败:', error)\n      return { success: false, error: '删除知识点失败' }\n    }\n  }\n}\n\n// 导出单例实例\nexport const knowledgeGraphService = new KnowledgeGraphService()\n"], "names": [], "mappings": ";;;;AAAA;;AAOO,MAAM;IAEX;;;GAGC,GACD,MAAc,YACZ,iBAAyB,EACzB,cAAsB,EACtB,oBAA2C,EACzB;QAClB,QAAQ;QACR,MAAM,QAAQ,IAAI;QAElB,SAAS;QACT,qBAAqB,OAAO,CAAC,CAAA;YAC3B,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,GAAG;gBACnC,MAAM,GAAG,CAAC,IAAI,eAAe,EAAE,EAAE;YACnC;YACA,MAAM,GAAG,CAAC,IAAI,eAAe,EAAG,IAAI,CAAC,IAAI,YAAY;QACvD;QAEA,SAAS;QACT,IAAI,CAAC,MAAM,GAAG,CAAC,oBAAoB;YACjC,MAAM,GAAG,CAAC,mBAAmB,EAAE;QACjC;QACA,MAAM,GAAG,CAAC,mBAAoB,IAAI,CAAC;QAEnC,UAAU;QACV,MAAM,UAAU,IAAI;QACpB,MAAM,iBAAiB,IAAI;QAE3B,MAAM,MAAM,CAAC;YACX,IAAI,eAAe,GAAG,CAAC,OAAO,OAAO,KAAK,OAAO;;YACjD,IAAI,QAAQ,GAAG,CAAC,OAAO,OAAO;YAE9B,QAAQ,GAAG,CAAC;YACZ,eAAe,GAAG,CAAC;YAEnB,MAAM,YAAY,MAAM,GAAG,CAAC,SAAS,EAAE;YACvC,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI,IAAI,WAAW,OAAO;YAC5B;YAEA,eAAe,MAAM,CAAC;YACtB,OAAO;QACT;QAEA,SAAS;QACT,KAAK,MAAM,QAAQ,MAAM,IAAI,GAAI;YAC/B,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAO;gBACtB,IAAI,IAAI,OAAO,OAAO;YACxB;QACF;QAEA,OAAO;IACT;IAEA;;;GAGC,GACD,MAAM,cAAc,cAAsB,EAAE,WAAmB,EAI5D;QACD,IAAI;YACF,UAAU;YACV,MAAM,CAAC,cAAc,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClD,yHAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC;gBAC3B,yHAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC;aAC5B;YAED,IAAI,CAAC,cAAc;gBACjB,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAW;YAC7C;YAEA,IAAI,CAAC,WAAW;gBACd,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAW;YAC7C;YAEA,aAAa;YACb,MAAM,uBAAuB,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM;YACzD,MAAM,SAAS,qBAAqB,IAAI,CACtC,CAAA,MAAO,IAAI,eAAe,KAAK,kBAAkB,IAAI,YAAY,KAAK;YAGxE,IAAI,QAAQ;gBACV,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C;YAEA,SAAS;YACT,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,aAAa;YACrE,IAAI,UAAU;gBACZ,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAe;YACjD;YAEA,SAAS;YACT,MAAM,aAAa,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,gBAAgB;YAEhE,OAAO;gBAAE,SAAS;gBAAM;YAAW;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAEA;;;GAGC,GACD,MAAM,oBAA6C;QACjD,IAAI;YACF,MAAM,CAAC,iBAAiB,cAAc,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtE,yHAAA,CAAA,qBAAkB,CAAC,MAAM;gBACzB,yHAAA,CAAA,kBAAe,CAAC,MAAM;gBACtB,yHAAA,CAAA,cAAW,CAAC,MAAM;aACnB;YAED,OAAO;YACP,MAAM,QAAQ,gBAAgB,GAAG,CAAC,CAAA;gBAChC,MAAM,WAAW,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,kBAAkB,KAAK,MAAM,EAAE;gBACzE,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,MAAM,MAAM,IAAI;oBAChB,OAAO,MAAM,gBAAgB;oBAC7B,eAAe,CAAA,qBAAA,+BAAA,SAAU,aAAa,KAAI;oBAC1C,kBAAkB,MAAM,gBAAgB;gBAC1C;YACF;YAEA,MAAM;YACN,MAAM,QAAQ,aAAa,GAAG,CAAC,CAAA,MAAO,CAAC;oBACrC,QAAQ,IAAI,eAAe;oBAC3B,QAAQ,IAAI,YAAY;gBAC1B,CAAC;YAED,OAAO;gBAAE;gBAAO;YAAM;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBAAE,OAAO,EAAE;gBAAE,OAAO,EAAE;YAAC;QAChC;IACF;IAEA;;;GAGC,GACD,MAAM,sBAAyC;QAC7C,IAAI;YACF,MAAM,eAAe,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM;YACjD,MAAM,kBAAkB,MAAM,yHAAA,CAAA,qBAAkB,CAAC,MAAM;YAEvD,YAAY;YACZ,MAAM,QAAQ,IAAI;YAClB,MAAM,WAAW,IAAI;YAErB,MAAM;YACN,gBAAgB,OAAO,CAAC,CAAA;gBACtB,MAAM,GAAG,CAAC,MAAM,EAAE,EAAE,EAAE;gBACtB,SAAS,GAAG,CAAC,MAAM,EAAE,EAAE;YACzB;YAEA,MAAM;YACN,aAAa,OAAO,CAAC,CAAA;gBACnB,MAAM,GAAG,CAAC,IAAI,eAAe,EAAG,IAAI,CAAC,IAAI,YAAY;gBACrD,SAAS,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC,SAAS,GAAG,CAAC,IAAI,YAAY,KAAK,CAAC,IAAI;YACzE;YAEA,eAAe;YACf,MAAM,QAAkB,EAAE;YAC1B,MAAM,SAAmB,EAAE;YAE3B,cAAc;YACd,KAAK,MAAM,CAAC,QAAQ,OAAO,IAAI,SAAS,OAAO,GAAI;gBACjD,IAAI,WAAW,GAAG;oBAChB,MAAM,IAAI,CAAC;gBACb;YACF;YAEA,MAAO,MAAM,MAAM,GAAG,EAAG;gBACvB,MAAM,UAAU,MAAM,KAAK;gBAC3B,OAAO,IAAI,CAAC;gBAEZ,cAAc;gBACd,MAAM,YAAY,MAAM,GAAG,CAAC,YAAY,EAAE;gBAC1C,KAAK,MAAM,YAAY,UAAW;oBAChC,MAAM,YAAY,SAAS,GAAG,CAAC,YAAa;oBAC5C,SAAS,GAAG,CAAC,UAAU;oBAEvB,IAAI,cAAc,GAAG;wBACnB,MAAM,IAAI,CAAC;oBACb;gBACF;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO,EAAE;QACX;IACF;IAEA;;;GAGC,GACD,MAAM,6BAAiF;YAAtD,QAAA,iEAAgB;QAC/C,IAAI;YACF,MAAM,CAAC,iBAAiB,cAAc,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtE,yHAAA,CAAA,qBAAkB,CAAC,MAAM;gBACzB,yHAAA,CAAA,kBAAe,CAAC,MAAM;gBACtB,yHAAA,CAAA,cAAW,CAAC,MAAM;aACnB;YAED,MAAM,kBAA4C,EAAE;YAEpD,KAAK,MAAM,SAAS,gBAAiB;gBACnC,MAAM,WAAW,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,kBAAkB,KAAK,MAAM,EAAE;gBACzE,MAAM,iBAAiB,CAAA,qBAAA,+BAAA,SAAU,aAAa,KAAI;gBAElD,qBAAqB;gBACrB,IAAI,kBAAkB,IAAI;gBAE1B,aAAa;gBACb,MAAM,gBAAgB,aACnB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK,MAAM,EAAE,EAC3C,GAAG,CAAC,CAAA,MAAO,IAAI,eAAe;gBAEjC,MAAM,sBAAsB,cAAc,KAAK,CAAC,CAAA;oBAC9C,MAAM,iBAAiB,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,kBAAkB,KAAK;oBACvE,OAAO,CAAC,CAAA,2BAAA,qCAAA,eAAgB,aAAa,KAAI,CAAC,KAAK;gBACjD;gBAEA,IAAI,qBAAqB;oBACvB,QAAQ;oBACR,IAAI,WAAW,MAAM,eAAe,eAAe;;oBACnD,YAAY,CAAC,KAAK,MAAM,gBAAgB,IAAI,GAAE,aAAa;oBAE3D,eAAe;oBACf,MAAM,gBAAgB,MAAM,gBAAgB,GAAG,GAAG,aAAa;;oBAE/D,gBAAgB,IAAI,CAAC;wBACnB,iBAAiB;wBACjB,QAAQ,cAAc,MAAM,GAAG,IAC1B,mBACA;wBACL;wBACA,gBAAgB;oBAClB;gBACF;YACF;YAEA,cAAc;YACd,OAAO,gBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,EACtC,KAAK,CAAC,GAAG;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO,EAAE;QACX;IACF;IAEA;;;GAGC,GACD,MAAM,qBAAqB,EAAU,EAGlC;QACD,IAAI;YACF,oBAAoB;YACpB,MAAM,eAAe,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM;YACjD,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,MAAO,IAAI,eAAe,KAAK;YAEvE,IAAI,eAAe;gBACjB,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,kCAAkC;YAClC,MAAM,yHAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;YAEhC,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAU;QAC5C;IACF;AACF;AAGO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/store/useKnowledgeStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { KnowledgePoint, KnowledgeDependency, UserProgress, KnowledgeGraph } from '@/types'\n\ninterface KnowledgeStore {\n  // State\n  knowledgePoints: KnowledgePoint[]\n  dependencies: KnowledgeDependency[]\n  userProgress: UserProgress[]\n  selectedKnowledgePoint: KnowledgePoint | null\n  isLoading: boolean\n  error: string | null\n\n  // Actions\n  setKnowledgePoints: (points: KnowledgePoint[]) => void\n  addKnowledgePoint: (point: KnowledgePoint) => void\n  updateKnowledgePoint: (id: string, updates: Partial<KnowledgePoint>) => void\n  deleteKnowledgePoint: (id: string) => void\n  \n  setDependencies: (deps: KnowledgeDependency[]) => void\n  addDependency: (dep: KnowledgeDependency) => void\n  removeDependency: (prerequisiteId: string, dependentId: string) => void\n  \n  setUserProgress: (progress: UserProgress[]) => void\n  updateUserProgress: (knowledgePointId: string, updates: Partial<UserProgress>) => void\n  \n  setSelectedKnowledgePoint: (point: KnowledgePoint | null) => void\n  setLoading: (loading: boolean) => void\n  setError: (error: string | null) => void\n  \n  // Computed\n  getKnowledgeGraph: () => KnowledgeGraph\n  getPrerequisites: (knowledgePointId: string) => KnowledgePoint[]\n  getDependents: (knowledgePointId: string) => KnowledgePoint[]\n  getUserMasteryLevel: (knowledgePointId: string) => number\n  getRecommendations: () => KnowledgePoint[]\n}\n\nexport const useKnowledgeStore = create<KnowledgeStore>((set, get) => ({\n  // Initial state\n  knowledgePoints: [],\n  dependencies: [],\n  userProgress: [],\n  selectedKnowledgePoint: null,\n  isLoading: false,\n  error: null,\n\n  // Actions\n  setKnowledgePoints: (points) => set({ knowledgePoints: points }),\n  \n  addKnowledgePoint: (point) => set((state) => ({\n    knowledgePoints: [...state.knowledgePoints, point]\n  })),\n  \n  updateKnowledgePoint: (id, updates) => set((state) => ({\n    knowledgePoints: state.knowledgePoints.map(point =>\n      point.id === id ? { ...point, ...updates } : point\n    )\n  })),\n  \n  deleteKnowledgePoint: (id) => set((state) => ({\n    knowledgePoints: state.knowledgePoints.filter(point => point.id !== id),\n    dependencies: state.dependencies.filter(dep => \n      dep.prerequisite_id !== id && dep.dependent_id !== id\n    )\n  })),\n  \n  setDependencies: (deps) => set({ dependencies: deps }),\n  \n  addDependency: (dep) => set((state) => ({\n    dependencies: [...state.dependencies, dep]\n  })),\n  \n  removeDependency: (prerequisiteId, dependentId) => set((state) => ({\n    dependencies: state.dependencies.filter(dep =>\n      !(dep.prerequisite_id === prerequisiteId && dep.dependent_id === dependentId)\n    )\n  })),\n  \n  setUserProgress: (progress) => set({ userProgress: progress }),\n  \n  updateUserProgress: (knowledgePointId, updates) => set((state) => ({\n    userProgress: state.userProgress.map(progress =>\n      progress.knowledge_point_id === knowledgePointId \n        ? { ...progress, ...updates }\n        : progress\n    )\n  })),\n  \n  setSelectedKnowledgePoint: (point) => set({ selectedKnowledgePoint: point }),\n  setLoading: (loading) => set({ isLoading: loading }),\n  setError: (error) => set({ error }),\n\n  // Computed functions\n  getKnowledgeGraph: () => {\n    const { knowledgePoints, dependencies, userProgress } = get()\n    \n    const nodes = knowledgePoints.map(point => {\n      const progress = userProgress.find(p => p.knowledge_point_id === point.id)\n      return {\n        id: point.id,\n        name: point.name,\n        group: point.difficulty_level,\n        mastery_level: progress?.mastery_level || 0,\n        difficulty_level: point.difficulty_level\n      }\n    })\n    \n    const links = dependencies.map(dep => ({\n      source: dep.prerequisite_id,\n      target: dep.dependent_id\n    }))\n    \n    return { nodes, links }\n  },\n  \n  getPrerequisites: (knowledgePointId) => {\n    const { knowledgePoints, dependencies } = get()\n    const prerequisiteIds = dependencies\n      .filter(dep => dep.dependent_id === knowledgePointId)\n      .map(dep => dep.prerequisite_id)\n    \n    return knowledgePoints.filter(point => prerequisiteIds.includes(point.id))\n  },\n  \n  getDependents: (knowledgePointId) => {\n    const { knowledgePoints, dependencies } = get()\n    const dependentIds = dependencies\n      .filter(dep => dep.prerequisite_id === knowledgePointId)\n      .map(dep => dep.dependent_id)\n    \n    return knowledgePoints.filter(point => dependentIds.includes(point.id))\n  },\n  \n  getUserMasteryLevel: (knowledgePointId) => {\n    const { userProgress } = get()\n    const progress = userProgress.find(p => p.knowledge_point_id === knowledgePointId)\n    return progress?.mastery_level || 0\n  },\n  \n  getRecommendations: () => {\n    const { knowledgePoints, dependencies, userProgress } = get()\n    \n    // Simple recommendation algorithm: find knowledge points where all prerequisites are mastered\n    return knowledgePoints.filter(point => {\n      const progress = userProgress.find(p => p.knowledge_point_id === point.id)\n      const currentMastery = progress?.mastery_level || 0\n      \n      // Skip if already mastered (>= 80%)\n      if (currentMastery >= 80) return false\n      \n      // Check if all prerequisites are mastered\n      const prerequisites = dependencies\n        .filter(dep => dep.dependent_id === point.id)\n        .map(dep => dep.prerequisite_id)\n      \n      const allPrerequisitesMastered = prerequisites.every(prereqId => {\n        const prereqProgress = userProgress.find(p => p.knowledge_point_id === prereqId)\n        return (prereqProgress?.mastery_level || 0) >= 80\n      })\n      \n      return allPrerequisitesMastered\n    })\n  }\n}))\n"], "names": [], "mappings": ";;;AAAA;;AAqCO,MAAM,oBAAoB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,KAAK,MAAQ,CAAC;QACrE,gBAAgB;QAChB,iBAAiB,EAAE;QACnB,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,wBAAwB;QACxB,WAAW;QACX,OAAO;QAEP,UAAU;QACV,oBAAoB,CAAC,SAAW,IAAI;gBAAE,iBAAiB;YAAO;QAE9D,mBAAmB,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBAC5C,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;qBAAM;gBACpD,CAAC;QAED,sBAAsB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACrD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,QACzC,MAAM,EAAE,KAAK,KAAK;4BAAE,GAAG,KAAK;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEjD,CAAC;QAED,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;oBACpE,cAAc,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA,MACtC,IAAI,eAAe,KAAK,MAAM,IAAI,YAAY,KAAK;gBAEvD,CAAC;QAED,iBAAiB,CAAC,OAAS,IAAI;gBAAE,cAAc;YAAK;QAEpD,eAAe,CAAC,MAAQ,IAAI,CAAC,QAAU,CAAC;oBACtC,cAAc;2BAAI,MAAM,YAAY;wBAAE;qBAAI;gBAC5C,CAAC;QAED,kBAAkB,CAAC,gBAAgB,cAAgB,IAAI,CAAC,QAAU,CAAC;oBACjE,cAAc,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA,MACtC,CAAC,CAAC,IAAI,eAAe,KAAK,kBAAkB,IAAI,YAAY,KAAK,WAAW;gBAEhF,CAAC;QAED,iBAAiB,CAAC,WAAa,IAAI;gBAAE,cAAc;YAAS;QAE5D,oBAAoB,CAAC,kBAAkB,UAAY,IAAI,CAAC,QAAU,CAAC;oBACjE,cAAc,MAAM,YAAY,CAAC,GAAG,CAAC,CAAA,WACnC,SAAS,kBAAkB,KAAK,mBAC5B;4BAAE,GAAG,QAAQ;4BAAE,GAAG,OAAO;wBAAC,IAC1B;gBAER,CAAC;QAED,2BAA2B,CAAC,QAAU,IAAI;gBAAE,wBAAwB;YAAM;QAC1E,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAClD,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,qBAAqB;QACrB,mBAAmB;YACjB,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;YAExD,MAAM,QAAQ,gBAAgB,GAAG,CAAC,CAAA;gBAChC,MAAM,WAAW,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,kBAAkB,KAAK,MAAM,EAAE;gBACzE,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,MAAM,MAAM,IAAI;oBAChB,OAAO,MAAM,gBAAgB;oBAC7B,eAAe,CAAA,qBAAA,+BAAA,SAAU,aAAa,KAAI;oBAC1C,kBAAkB,MAAM,gBAAgB;gBAC1C;YACF;YAEA,MAAM,QAAQ,aAAa,GAAG,CAAC,CAAA,MAAO,CAAC;oBACrC,QAAQ,IAAI,eAAe;oBAC3B,QAAQ,IAAI,YAAY;gBAC1B,CAAC;YAED,OAAO;gBAAE;gBAAO;YAAM;QACxB;QAEA,kBAAkB,CAAC;YACjB,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,GAAG;YAC1C,MAAM,kBAAkB,aACrB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK,kBACnC,GAAG,CAAC,CAAA,MAAO,IAAI,eAAe;YAEjC,OAAO,gBAAgB,MAAM,CAAC,CAAA,QAAS,gBAAgB,QAAQ,CAAC,MAAM,EAAE;QAC1E;QAEA,eAAe,CAAC;YACd,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,GAAG;YAC1C,MAAM,eAAe,aAClB,MAAM,CAAC,CAAA,MAAO,IAAI,eAAe,KAAK,kBACtC,GAAG,CAAC,CAAA,MAAO,IAAI,YAAY;YAE9B,OAAO,gBAAgB,MAAM,CAAC,CAAA,QAAS,aAAa,QAAQ,CAAC,MAAM,EAAE;QACvE;QAEA,qBAAqB,CAAC;YACpB,MAAM,EAAE,YAAY,EAAE,GAAG;YACzB,MAAM,WAAW,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,kBAAkB,KAAK;YACjE,OAAO,CAAA,qBAAA,+BAAA,SAAU,aAAa,KAAI;QACpC;QAEA,oBAAoB;YAClB,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;YAExD,8FAA8F;YAC9F,OAAO,gBAAgB,MAAM,CAAC,CAAA;gBAC5B,MAAM,WAAW,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,kBAAkB,KAAK,MAAM,EAAE;gBACzE,MAAM,iBAAiB,CAAA,qBAAA,+BAAA,SAAU,aAAa,KAAI;gBAElD,oCAAoC;gBACpC,IAAI,kBAAkB,IAAI,OAAO;gBAEjC,0CAA0C;gBAC1C,MAAM,gBAAgB,aACnB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK,MAAM,EAAE,EAC3C,GAAG,CAAC,CAAA,MAAO,IAAI,eAAe;gBAEjC,MAAM,2BAA2B,cAAc,KAAK,CAAC,CAAA;oBACnD,MAAM,iBAAiB,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,kBAAkB,KAAK;oBACvE,OAAO,CAAC,CAAA,2BAAA,qCAAA,eAAgB,aAAa,KAAI,CAAC,KAAK;gBACjD;gBAEA,OAAO;YACT;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1279, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/hooks/useKnowledgeGraph.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react'\nimport { knowledgeGraphService } from '@/services/knowledgeGraphService'\nimport { knowledgePointsAPI } from '@/lib/database'\nimport { useKnowledgeStore } from '@/store/useKnowledgeStore'\nimport { KnowledgePoint, KnowledgeGraph, LearningRecommendation } from '@/types'\n\n/**\n * 知识图谱相关的自定义Hook\n * 提供UI组件需要的所有知识图谱操作\n */\nexport function useKnowledgeGraph() {\n  const {\n    knowledgePoints,\n    dependencies,\n    selectedKnowledgePoint,\n    isLoading,\n    error,\n    setKnowledgePoints,\n    setDependencies,\n    addKnowledgePoint,\n    updateKnowledgePoint,\n    deleteKnowledgePoint: deleteFromStore,\n    addDependency,\n    removeDependency,\n    setSelectedKnowledgePoint,\n    setLoading,\n    setError,\n    getKnowledgeGraph,\n    getRecommendations\n  } = useKnowledgeStore()\n\n  // 本地状态\n  const [recommendations, setRecommendations] = useState<LearningRecommendation[]>([])\n  const [topologicalOrder, setTopologicalOrder] = useState<string[]>([])\n\n  /**\n   * 加载所有知识图谱数据\n   */\n  const loadKnowledgeGraph = useCallback(async () => {\n    setLoading(true)\n    setError(null)\n\n    try {\n      const { dependenciesAPI } = await import('@/lib/database')\n      const [points, dependencies] = await Promise.all([\n        knowledgePointsAPI.getAll(),\n        dependenciesAPI.getAll()\n      ])\n\n      setKnowledgePoints(points)\n      setDependencies(dependencies)\n\n    } catch (err) {\n      console.error('加载知识图谱失败:', err)\n      setError('加载知识图谱失败')\n    } finally {\n      setLoading(false)\n    }\n  }, [setKnowledgePoints, setDependencies, setLoading, setError])\n\n  /**\n   * 创建新知识点\n   */\n  const createKnowledgePoint = useCallback(async (\n    pointData: Omit<KnowledgePoint, 'id' | 'created_at' | 'updated_at'>\n  ) => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      const newPoint = await knowledgePointsAPI.create(pointData)\n      addKnowledgePoint(newPoint)\n      return { success: true, data: newPoint }\n    } catch (err) {\n      console.error('创建知识点失败:', err)\n      setError('创建知识点失败')\n      return { success: false, error: '创建知识点失败' }\n    } finally {\n      setLoading(false)\n    }\n  }, [addKnowledgePoint, setLoading, setError])\n\n  /**\n   * 更新知识点\n   */\n  const updateKnowledgePointData = useCallback(async (\n    id: string, \n    updates: Partial<KnowledgePoint>\n  ) => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      const updatedPoint = await knowledgePointsAPI.update(id, updates)\n      updateKnowledgePoint(id, updatedPoint)\n      return { success: true, data: updatedPoint }\n    } catch (err) {\n      console.error('更新知识点失败:', err)\n      setError('更新知识点失败')\n      return { success: false, error: '更新知识点失败' }\n    } finally {\n      setLoading(false)\n    }\n  }, [updateKnowledgePoint, setLoading, setError])\n\n  /**\n   * 删除知识点\n   */\n  const deleteKnowledgePoint = useCallback(async (id: string) => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      const result = await knowledgeGraphService.deleteKnowledgePoint(id)\n      \n      if (result.success) {\n        deleteFromStore(id)\n        // 如果删除的是当前选中的知识点，清除选择\n        if (selectedKnowledgePoint?.id === id) {\n          setSelectedKnowledgePoint(null)\n        }\n        return { success: true }\n      } else {\n        setError(result.error || '删除知识点失败')\n        return { success: false, error: result.error }\n      }\n    } catch (err) {\n      console.error('删除知识点失败:', err)\n      setError('删除知识点失败')\n      return { success: false, error: '删除知识点失败' }\n    } finally {\n      setLoading(false)\n    }\n  }, [deleteFromStore, selectedKnowledgePoint, setSelectedKnowledgePoint, setLoading, setError])\n\n  /**\n   * 添加依赖关系\n   */\n  const createDependency = useCallback(async (prerequisiteId: string, dependentId: string) => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      const result = await knowledgeGraphService.addDependency(prerequisiteId, dependentId)\n      \n      if (result.success && result.dependency) {\n        addDependency(result.dependency)\n        return { success: true, data: result.dependency }\n      } else {\n        setError(result.error || '添加依赖关系失败')\n        return { success: false, error: result.error }\n      }\n    } catch (err) {\n      console.error('添加依赖关系失败:', err)\n      setError('添加依赖关系失败')\n      return { success: false, error: '添加依赖关系失败' }\n    } finally {\n      setLoading(false)\n    }\n  }, [addDependency, setLoading, setError])\n\n  /**\n   * 删除依赖关系\n   */\n  const deleteDependency = useCallback(async (prerequisiteId: string, dependentId: string) => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      const { dependenciesAPI } = await import('@/lib/database')\n      await dependenciesAPI.delete(prerequisiteId, dependentId)\n      removeDependency(prerequisiteId, dependentId)\n      return { success: true }\n    } catch (err) {\n      console.error('删除依赖关系失败:', err)\n      setError('删除依赖关系失败')\n      return { success: false, error: '删除依赖关系失败' }\n    } finally {\n      setLoading(false)\n    }\n  }, [removeDependency, setLoading, setError])\n\n  /**\n   * 获取学习推荐\n   */\n  const loadRecommendations = useCallback(async (limit: number = 5) => {\n    try {\n      const recs = await knowledgeGraphService.getLearningRecommendations(limit)\n      setRecommendations(recs)\n      return recs\n    } catch (err) {\n      console.error('获取学习推荐失败:', err)\n      return []\n    }\n  }, [])\n\n  /**\n   * 获取拓扑排序\n   */\n  const loadTopologicalOrder = useCallback(async () => {\n    try {\n      const order = await knowledgeGraphService.getTopologicalOrder()\n      setTopologicalOrder(order)\n      return order\n    } catch (err) {\n      console.error('获取拓扑排序失败:', err)\n      return []\n    }\n  }, [])\n\n  /**\n   * 搜索知识点\n   */\n  const searchKnowledgePoints = useCallback(async (query: string) => {\n    if (!query.trim()) return knowledgePoints\n    \n    try {\n      const results = await knowledgePointsAPI.search(query)\n      return results\n    } catch (err) {\n      console.error('搜索知识点失败:', err)\n      return []\n    }\n  }, [knowledgePoints])\n\n  // 初始化时加载数据\n  useEffect(() => {\n    loadKnowledgeGraph()\n  }, [loadKnowledgeGraph])\n\n  // 返回所有需要的状态和方法\n  return {\n    // 状态\n    knowledgePoints,\n    dependencies,\n    selectedKnowledgePoint,\n    recommendations,\n    topologicalOrder,\n    isLoading,\n    error,\n    \n    // 计算属性\n    knowledgeGraph: getKnowledgeGraph(),\n    storeRecommendations: getRecommendations(),\n    \n    // 方法\n    loadKnowledgeGraph,\n    createKnowledgePoint,\n    updateKnowledgePoint: updateKnowledgePointData,\n    deleteKnowledgePoint,\n    createDependency,\n    deleteDependency,\n    loadRecommendations,\n    loadTopologicalOrder,\n    searchKnowledgePoints,\n    setSelectedKnowledgePoint,\n    \n    // 工具方法\n    clearError: () => setError(null),\n    refreshData: loadKnowledgeGraph\n  }\n}\n\n/**\n * 简化版Hook，只提供只读数据\n * 适用于只需要展示数据的组件\n */\nexport function useKnowledgeGraphData() {\n  const { \n    knowledgePoints, \n    dependencies, \n    isLoading, \n    error,\n    getKnowledgeGraph,\n    getRecommendations \n  } = useKnowledgeStore()\n\n  return {\n    knowledgePoints,\n    dependencies,\n    knowledgeGraph: getKnowledgeGraph(),\n    recommendations: getRecommendations(),\n    isLoading,\n    error\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAOO,SAAS;;IACd,MAAM,EACJ,eAAe,EACf,YAAY,EACZ,sBAAsB,EACtB,SAAS,EACT,KAAK,EACL,kBAAkB,EAClB,eAAe,EACf,iBAAiB,EACjB,oBAAoB,EACpB,sBAAsB,eAAe,EACrC,aAAa,EACb,gBAAgB,EAChB,yBAAyB,EACzB,UAAU,EACV,QAAQ,EACR,iBAAiB,EACjB,kBAAkB,EACnB,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD;IAEpB,OAAO;IACP,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErE;;GAEC,GACD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACrC,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,EAAE,eAAe,EAAE,GAAG;gBAC5B,MAAM,CAAC,QAAQ,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAC/C,yHAAA,CAAA,qBAAkB,CAAC,MAAM;oBACzB,gBAAgB,MAAM;iBACvB;gBAED,mBAAmB;gBACnB,gBAAgB;YAElB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;4DAAG;QAAC;QAAoB;QAAiB;QAAY;KAAS;IAE9D;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,OACvC;YAEA,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,WAAW,MAAM,yHAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;gBACjD,kBAAkB;gBAClB,OAAO;oBAAE,SAAS;oBAAM,MAAM;gBAAS;YACzC,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,YAAY;gBAC1B,SAAS;gBACT,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C,SAAU;gBACR,WAAW;YACb;QACF;8DAAG;QAAC;QAAmB;QAAY;KAAS;IAE5C;;GAEC,GACD,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mEAAE,OAC3C,IACA;YAEA,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,eAAe,MAAM,yHAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,IAAI;gBACzD,qBAAqB,IAAI;gBACzB,OAAO;oBAAE,SAAS;oBAAM,MAAM;gBAAa;YAC7C,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,YAAY;gBAC1B,SAAS;gBACT,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C,SAAU;gBACR,WAAW;YACb;QACF;kEAAG;QAAC;QAAsB;QAAY;KAAS;IAE/C;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,OAAO;YAC9C,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,SAAS,MAAM,2IAAA,CAAA,wBAAqB,CAAC,oBAAoB,CAAC;gBAEhE,IAAI,OAAO,OAAO,EAAE;oBAClB,gBAAgB;oBAChB,sBAAsB;oBACtB,IAAI,CAAA,mCAAA,6CAAA,uBAAwB,EAAE,MAAK,IAAI;wBACrC,0BAA0B;oBAC5B;oBACA,OAAO;wBAAE,SAAS;oBAAK;gBACzB,OAAO;oBACL,SAAS,OAAO,KAAK,IAAI;oBACzB,OAAO;wBAAE,SAAS;wBAAO,OAAO,OAAO,KAAK;oBAAC;gBAC/C;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,YAAY;gBAC1B,SAAS;gBACT,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C,SAAU;gBACR,WAAW;YACb;QACF;8DAAG;QAAC;QAAiB;QAAwB;QAA2B;QAAY;KAAS;IAE7F;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO,gBAAwB;YAClE,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,SAAS,MAAM,2IAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,gBAAgB;gBAEzE,IAAI,OAAO,OAAO,IAAI,OAAO,UAAU,EAAE;oBACvC,cAAc,OAAO,UAAU;oBAC/B,OAAO;wBAAE,SAAS;wBAAM,MAAM,OAAO,UAAU;oBAAC;gBAClD,OAAO;oBACL,SAAS,OAAO,KAAK,IAAI;oBACzB,OAAO;wBAAE,SAAS;wBAAO,OAAO,OAAO,KAAK;oBAAC;gBAC/C;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;gBACT,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAW;YAC7C,SAAU;gBACR,WAAW;YACb;QACF;0DAAG;QAAC;QAAe;QAAY;KAAS;IAExC;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO,gBAAwB;YAClE,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,EAAE,eAAe,EAAE,GAAG;gBAC5B,MAAM,gBAAgB,MAAM,CAAC,gBAAgB;gBAC7C,iBAAiB,gBAAgB;gBACjC,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;gBACT,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAW;YAC7C,SAAU;gBACR,WAAW;YACb;QACF;0DAAG;QAAC;QAAkB;QAAY;KAAS;IAE3C;;GAEC,GACD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;gBAAO,yEAAgB;YAC7D,IAAI;gBACF,MAAM,OAAO,MAAM,2IAAA,CAAA,wBAAqB,CAAC,0BAA0B,CAAC;gBACpE,mBAAmB;gBACnB,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,aAAa;gBAC3B,OAAO,EAAE;YACX;QACF;6DAAG,EAAE;IAEL;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YACvC,IAAI;gBACF,MAAM,QAAQ,MAAM,2IAAA,CAAA,wBAAqB,CAAC,mBAAmB;gBAC7D,oBAAoB;gBACpB,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,aAAa;gBAC3B,OAAO,EAAE;YACX;QACF;8DAAG,EAAE;IAEL;;GAEC,GACD,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE,OAAO;YAC/C,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAE1B,IAAI;gBACF,MAAM,UAAU,MAAM,yHAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;gBAChD,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,YAAY;gBAC1B,OAAO,EAAE;YACX;QACF;+DAAG;QAAC;KAAgB;IAEpB,WAAW;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAmB;IAEvB,eAAe;IACf,OAAO;QACL,KAAK;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,OAAO;QACP,gBAAgB;QAChB,sBAAsB;QAEtB,KAAK;QACL;QACA;QACA,sBAAsB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,OAAO;QACP,YAAY,IAAM,SAAS;QAC3B,aAAa;IACf;AACF;GA3PgB;;QAmBV,oIAAA,CAAA,oBAAiB;;;AA8OhB,SAAS;;IACd,MAAM,EACJ,eAAe,EACf,YAAY,EACZ,SAAS,EACT,KAAK,EACL,iBAAiB,EACjB,kBAAkB,EACnB,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD;IAEpB,OAAO;QACL;QACA;QACA,gBAAgB;QAChB,iBAAiB;QACjB;QACA;IACF;AACF;IAlBgB;;QAQV,oIAAA,CAAA,oBAAiB", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/components/knowledge/KnowledgePointList.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useMemo } from 'react'\nimport { KnowledgePointCard } from './KnowledgePointCard'\nimport { Input } from '@/components/ui/Input'\nimport { Button } from '@/components/ui/Button'\nimport { useKnowledgeGraph } from '@/hooks/useKnowledgeGraph'\nimport { KnowledgePoint } from '@/types'\nimport { Search, Plus, Filter, SortAsc, SortDesc } from 'lucide-react'\n\ninterface KnowledgePointListProps {\n  onCreateNew?: () => void\n  onEditPoint?: (point: KnowledgePoint) => void\n  onDeletePoint?: (id: string) => void\n  showActions?: boolean\n}\n\ntype SortField = 'name' | 'difficulty' | 'mastery' | 'created_at'\ntype SortOrder = 'asc' | 'desc'\n\nexport function KnowledgePointList({\n  onCreateNew,\n  onEditPoint,\n  onDeletePoint,\n  showActions = true\n}: KnowledgePointListProps) {\n  const {\n    knowledgePoints,\n    selectedKnowledgePoint,\n    setSelectedKnowledgePoint,\n    isLoading,\n    error\n  } = useKnowledgeGraph()\n\n  // 本地状态\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedTags, setSelectedTags] = useState<string[]>([])\n  const [difficultyFilter, setDifficultyFilter] = useState<number | null>(null)\n  const [sortField, setSortField] = useState<SortField>('created_at')\n  const [sortOrder, setSortOrder] = useState<SortOrder>('desc')\n  const [showFilters, setShowFilters] = useState(false)\n\n  // 获取所有标签\n  const allTags = useMemo(() => {\n    const tags = new Set<string>()\n    knowledgePoints.forEach(point => {\n      point.tags?.forEach(tag => tags.add(tag))\n    })\n    return Array.from(tags).sort()\n  }, [knowledgePoints])\n\n  // 过滤和排序逻辑\n  const filteredAndSortedPoints = useMemo(() => {\n    let filtered = knowledgePoints\n\n    // 搜索过滤\n    if (searchQuery.trim()) {\n      const query = searchQuery.toLowerCase()\n      filtered = filtered.filter(point =>\n        point.name.toLowerCase().includes(query) ||\n        point.description?.toLowerCase().includes(query) ||\n        point.tags?.some(tag => tag.toLowerCase().includes(query))\n      )\n    }\n\n    // 标签过滤\n    if (selectedTags.length > 0) {\n      filtered = filtered.filter(point =>\n        selectedTags.every(tag => point.tags?.includes(tag))\n      )\n    }\n\n    // 难度过滤\n    if (difficultyFilter !== null) {\n      filtered = filtered.filter(point => point.difficulty_level === difficultyFilter)\n    }\n\n    // 排序\n    filtered.sort((a, b) => {\n      let aValue: any\n      let bValue: any\n\n      switch (sortField) {\n        case 'name':\n          aValue = a.name.toLowerCase()\n          bValue = b.name.toLowerCase()\n          break\n        case 'difficulty':\n          aValue = a.difficulty_level\n          bValue = b.difficulty_level\n          break\n        case 'mastery':\n          // 这里需要从store获取掌握程度，暂时使用0\n          aValue = 0\n          bValue = 0\n          break\n        case 'created_at':\n          aValue = new Date(a.created_at).getTime()\n          bValue = new Date(b.created_at).getTime()\n          break\n        default:\n          return 0\n      }\n\n      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1\n      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1\n      return 0\n    })\n\n    return filtered\n  }, [knowledgePoints, searchQuery, selectedTags, difficultyFilter, sortField, sortOrder])\n\n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')\n    } else {\n      setSortField(field)\n      setSortOrder('asc')\n    }\n  }\n\n  const toggleTag = (tag: string) => {\n    setSelectedTags(prev =>\n      prev.includes(tag)\n        ? prev.filter(t => t !== tag)\n        : [...prev, tag]\n    )\n  }\n\n  const clearFilters = () => {\n    setSearchQuery('')\n    setSelectedTags([])\n    setDifficultyFilter(null)\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-8\">\n        <p className=\"text-red-600 dark:text-red-400\">{error}</p>\n        <Button \n          variant=\"outline\" \n          onClick={() => window.location.reload()}\n          className=\"mt-4\"\n        >\n          重试\n        </Button>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 头部操作栏 */}\n      <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n        <div className=\"flex-1 max-w-md\">\n          <Input\n            placeholder=\"搜索知识点...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"pl-10\"\n          />\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n        </div>\n        \n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => setShowFilters(!showFilters)}\n          >\n            <Filter className=\"h-4 w-4 mr-2\" />\n            筛选\n          </Button>\n          \n          {onCreateNew && (\n            <Button onClick={onCreateNew}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              新建知识点\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* 筛选面板 */}\n      {showFilters && (\n        <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-4\">\n          <div className=\"flex flex-wrap items-center gap-4\">\n            {/* 排序选项 */}\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">排序:</span>\n              {(['name', 'difficulty', 'created_at'] as SortField[]).map(field => (\n                <Button\n                  key={field}\n                  variant={sortField === field ? 'primary' : 'ghost'}\n                  size=\"sm\"\n                  onClick={() => handleSort(field)}\n                  className=\"flex items-center gap-1\"\n                >\n                  {field === 'name' && '名称'}\n                  {field === 'difficulty' && '难度'}\n                  {field === 'created_at' && '创建时间'}\n                  {sortField === field && (\n                    sortOrder === 'asc' ? <SortAsc className=\"h-3 w-3\" /> : <SortDesc className=\"h-3 w-3\" />\n                  )}\n                </Button>\n              ))}\n            </div>\n\n            {/* 难度筛选 */}\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">难度:</span>\n              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(level => (\n                <Button\n                  key={level}\n                  variant={difficultyFilter === level ? 'primary' : 'ghost'}\n                  size=\"sm\"\n                  onClick={() => setDifficultyFilter(difficultyFilter === level ? null : level)}\n                >\n                  {level}\n                </Button>\n              ))}\n            </div>\n          </div>\n\n          {/* 标签筛选 */}\n          {allTags.length > 0 && (\n            <div>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block\">标签:</span>\n              <div className=\"flex flex-wrap gap-2\">\n                {allTags.map(tag => (\n                  <Button\n                    key={tag}\n                    variant={selectedTags.includes(tag) ? 'primary' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => toggleTag(tag)}\n                  >\n                    {tag}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* 清除筛选 */}\n          <div className=\"flex justify-end\">\n            <Button variant=\"ghost\" size=\"sm\" onClick={clearFilters}>\n              清除筛选\n            </Button>\n          </div>\n        </div>\n      )}\n\n      {/* 统计信息 */}\n      <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n        显示 {filteredAndSortedPoints.length} / {knowledgePoints.length} 个知识点\n      </div>\n\n      {/* 知识点列表 */}\n      {isLoading ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {[...Array(6)].map((_, i) => (\n            <div key={i} className=\"animate-pulse\">\n              <div className=\"bg-gray-200 dark:bg-gray-700 rounded-lg h-48\"></div>\n            </div>\n          ))}\n        </div>\n      ) : filteredAndSortedPoints.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\n            {knowledgePoints.length === 0 ? '还没有知识点' : '没有找到匹配的知识点'}\n          </p>\n          {onCreateNew && knowledgePoints.length === 0 && (\n            <Button onClick={onCreateNew}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              创建第一个知识点\n            </Button>\n          )}\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {filteredAndSortedPoints.map(point => (\n            <KnowledgePointCard\n              key={point.id}\n              knowledgePoint={point}\n              masteryLevel={0} // TODO: 从store获取实际掌握程度\n              onEdit={onEditPoint}\n              onDelete={onDeletePoint}\n              onSelect={setSelectedKnowledgePoint}\n              isSelected={selectedKnowledgePoint?.id === point.id}\n              showActions={showActions}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AAoBO,SAAS,mBAAmB,KAKT;QALS,EACjC,WAAW,EACX,WAAW,EACX,aAAa,EACb,cAAc,IAAI,EACM,GALS;;IAMjC,MAAM,EACJ,eAAe,EACf,sBAAsB,EACtB,yBAAyB,EACzB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD;IAEpB,OAAO;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,SAAS;IACT,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YACtB,MAAM,OAAO,IAAI;YACjB,gBAAgB,OAAO;uDAAC,CAAA;wBACtB;qBAAA,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,OAAO;+DAAC,CAAA,MAAO,KAAK,GAAG,CAAC;;gBACtC;;YACA,OAAO,MAAM,IAAI,CAAC,MAAM,IAAI;QAC9B;8CAAG;QAAC;KAAgB;IAEpB,UAAU;IACV,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+DAAE;YACtC,IAAI,WAAW;YAEf,OAAO;YACP,IAAI,YAAY,IAAI,IAAI;gBACtB,MAAM,QAAQ,YAAY,WAAW;gBACrC,WAAW,SAAS,MAAM;2EAAC,CAAA;4BAEzB,oBACA;+BAFA,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAClC,qBAAA,MAAM,WAAW,cAAjB,yCAAA,mBAAmB,WAAW,GAAG,QAAQ,CAAC,aAC1C,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,IAAI;mFAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;;;;YAEvD;YAEA,OAAO;YACP,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,WAAW,SAAS,MAAM;2EAAC,CAAA,QACzB,aAAa,KAAK;mFAAC,CAAA;oCAAO;wCAAA,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,QAAQ,CAAC;;;;YAEnD;YAEA,OAAO;YACP,IAAI,qBAAqB,MAAM;gBAC7B,WAAW,SAAS,MAAM;2EAAC,CAAA,QAAS,MAAM,gBAAgB,KAAK;;YACjE;YAEA,KAAK;YACL,SAAS,IAAI;uEAAC,CAAC,GAAG;oBAChB,IAAI;oBACJ,IAAI;oBAEJ,OAAQ;wBACN,KAAK;4BACH,SAAS,EAAE,IAAI,CAAC,WAAW;4BAC3B,SAAS,EAAE,IAAI,CAAC,WAAW;4BAC3B;wBACF,KAAK;4BACH,SAAS,EAAE,gBAAgB;4BAC3B,SAAS,EAAE,gBAAgB;4BAC3B;wBACF,KAAK;4BACH,yBAAyB;4BACzB,SAAS;4BACT,SAAS;4BACT;wBACF,KAAK;4BACH,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;4BACvC,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;4BACvC;wBACF;4BACE,OAAO;oBACX;oBAEA,IAAI,SAAS,QAAQ,OAAO,cAAc,QAAQ,CAAC,IAAI;oBACvD,IAAI,SAAS,QAAQ,OAAO,cAAc,QAAQ,IAAI,CAAC;oBACvD,OAAO;gBACT;;YAEA,OAAO;QACT;8DAAG;QAAC;QAAiB;QAAa;QAAc;QAAkB;QAAW;KAAU;IAEvF,MAAM,aAAa,CAAC;QAClB,IAAI,cAAc,OAAO;YACvB,aAAa,cAAc,QAAQ,SAAS;QAC9C,OAAO;YACL,aAAa;YACb,aAAa;QACf;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB,CAAA,OACd,KAAK,QAAQ,CAAC,OACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,OACvB;mBAAI;gBAAM;aAAI;IAEtB;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,gBAAgB,EAAE;QAClB,oBAAoB;IACtB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAAkC;;;;;;8BAC/C,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oBACrC,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;0CAEZ,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;kCAGpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe,CAAC;;kDAE/B,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAIpC,6BACC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAQxC,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;oCACrE;wCAAC;wCAAQ;wCAAc;qCAAa,CAAiB,GAAG,CAAC,CAAA,sBACzD,6LAAC,qIAAA,CAAA,SAAM;4CAEL,SAAS,cAAc,QAAQ,YAAY;4CAC3C,MAAK;4CACL,SAAS,IAAM,WAAW;4CAC1B,WAAU;;gDAET,UAAU,UAAU;gDACpB,UAAU,gBAAgB;gDAC1B,UAAU,gBAAgB;gDAC1B,cAAc,SAAS,CACtB,cAAc,sBAAQ,6LAAC,iOAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAAe,6LAAC,oOAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;wDAC9E;;2CAXK;;;;;;;;;;;0CAiBX,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;oCACtE;wCAAC;wCAAG;wCAAG;wCAAG;wCAAG;wCAAG;wCAAG;wCAAG;wCAAG;wCAAG;qCAAG,CAAC,GAAG,CAAC,CAAA,sBACnC,6LAAC,qIAAA,CAAA,SAAM;4CAEL,SAAS,qBAAqB,QAAQ,YAAY;4CAClD,MAAK;4CACL,SAAS,IAAM,oBAAoB,qBAAqB,QAAQ,OAAO;sDAEtE;2CALI;;;;;;;;;;;;;;;;;oBAYZ,QAAQ,MAAM,GAAG,mBAChB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;0CAAkE;;;;;;0CAClF,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAA,oBACX,6LAAC,qIAAA,CAAA,SAAM;wCAEL,SAAS,aAAa,QAAQ,CAAC,OAAO,YAAY;wCAClD,MAAK;wCACL,SAAS,IAAM,UAAU;kDAExB;uCALI;;;;;;;;;;;;;;;;kCAaf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;sCAAc;;;;;;;;;;;;;;;;;0BAQ/D,6LAAC;gBAAI,WAAU;;oBAA2C;oBACpD,wBAAwB,MAAM;oBAAC;oBAAI,gBAAgB,MAAM;oBAAC;;;;;;;YAI/D,0BACC,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wBAAY,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;;;;;uBADP;;;;;;;;;uBAKZ,wBAAwB,MAAM,KAAK,kBACrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCACV,gBAAgB,MAAM,KAAK,IAAI,WAAW;;;;;;oBAE5C,eAAe,gBAAgB,MAAM,KAAK,mBACzC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;;0CACf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;qCAMvC,6LAAC;gBAAI,WAAU;0BACZ,wBAAwB,GAAG,CAAC,CAAA,sBAC3B,6LAAC,wJAAA,CAAA,qBAAkB;wBAEjB,gBAAgB;wBAChB,cAAc;wBACd,QAAQ;wBACR,UAAU;wBACV,UAAU;wBACV,YAAY,CAAA,mCAAA,6CAAA,uBAAwB,EAAE,MAAK,MAAM,EAAE;wBACnD,aAAa;uBAPR,MAAM,EAAE;;;;;;;;;;;;;;;;AAc3B;GApRgB;;QAYV,oIAAA,CAAA,oBAAiB;;;KAZP", "debugId": null}}, {"offset": {"line": 2128, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/components/knowledge/KnowledgePointForm.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { <PERSON>, Card<PERSON>eader, CardContent, CardFooter } from '@/components/ui/Card'\nimport { KnowledgePoint } from '@/types'\nimport { X, Plus } from 'lucide-react'\n\ninterface KnowledgePointFormProps {\n  knowledgePoint?: KnowledgePoint | null\n  onSubmit: (data: Omit<KnowledgePoint, 'id' | 'created_at' | 'updated_at'>) => Promise<void>\n  onCancel: () => void\n  isLoading?: boolean\n}\n\nexport function KnowledgePointForm({\n  knowledgePoint,\n  onSubmit,\n  onCancel,\n  isLoading = false\n}: KnowledgePointFormProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    difficulty_level: 1,\n    tags: [] as string[],\n    user_id: '' // 这个会在提交时设置\n  })\n  \n  const [newTag, setNewTag] = useState('')\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (knowledgePoint) {\n      setFormData({\n        name: knowledgePoint.name,\n        description: knowledgePoint.description || '',\n        difficulty_level: knowledgePoint.difficulty_level,\n        tags: knowledgePoint.tags || [],\n        user_id: knowledgePoint.user_id\n      })\n    } else {\n      setFormData({\n        name: '',\n        description: '',\n        difficulty_level: 1,\n        tags: [],\n        user_id: ''\n      })\n    }\n    setErrors({})\n  }, [knowledgePoint])\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.name.trim()) {\n      newErrors.name = '知识点名称不能为空'\n    }\n\n    if (formData.difficulty_level < 1 || formData.difficulty_level > 10) {\n      newErrors.difficulty_level = '难度级别必须在1-10之间'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) return\n\n    try {\n      await onSubmit(formData)\n    } catch (error) {\n      console.error('提交表单失败:', error)\n    }\n  }\n\n  const handleInputChange = (field: keyof typeof formData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    // 清除对应字段的错误\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }))\n    }\n  }\n\n  const addTag = () => {\n    const tag = newTag.trim()\n    if (tag && !formData.tags.includes(tag)) {\n      handleInputChange('tags', [...formData.tags, tag])\n      setNewTag('')\n    }\n  }\n\n  const removeTag = (tagToRemove: string) => {\n    handleInputChange('tags', formData.tags.filter(tag => tag !== tagToRemove))\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault()\n      addTag()\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            {knowledgePoint ? '编辑知识点' : '创建新知识点'}\n          </h2>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onCancel}\n            className=\"h-8 w-8 p-0\"\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </CardHeader>\n\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4\">\n          {/* 知识点名称 */}\n          <Input\n            label=\"知识点名称 *\"\n            value={formData.name}\n            onChange={(e) => handleInputChange('name', e.target.value)}\n            error={errors.name}\n            placeholder=\"输入知识点名称\"\n            disabled={isLoading}\n          />\n\n          {/* 描述 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              描述\n            </label>\n            <textarea\n              value={formData.description}\n              onChange={(e) => handleInputChange('description', e.target.value)}\n              placeholder=\"描述这个知识点的内容和要点\"\n              rows={3}\n              disabled={isLoading}\n              className=\"block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder:text-gray-400 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-500 dark:focus:border-indigo-400 dark:focus:ring-indigo-400\"\n            />\n          </div>\n\n          {/* 难度级别 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              难度级别 *\n            </label>\n            <div className=\"flex items-center space-x-4\">\n              <input\n                type=\"range\"\n                min=\"1\"\n                max=\"10\"\n                value={formData.difficulty_level}\n                onChange={(e) => handleInputChange('difficulty_level', parseInt(e.target.value))}\n                disabled={isLoading}\n                className=\"flex-1\"\n              />\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 min-w-[3rem]\">\n                {formData.difficulty_level}/10\n              </span>\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\">\n              <span>简单</span>\n              <span>中等</span>\n              <span>困难</span>\n            </div>\n            {errors.difficulty_level && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {errors.difficulty_level}\n              </p>\n            )}\n          </div>\n\n          {/* 标签 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              标签\n            </label>\n            \n            {/* 现有标签 */}\n            {formData.tags.length > 0 && (\n              <div className=\"flex flex-wrap gap-2 mb-2\">\n                {formData.tags.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-2 py-1 rounded-md text-xs bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200\"\n                  >\n                    {tag}\n                    <button\n                      type=\"button\"\n                      onClick={() => removeTag(tag)}\n                      disabled={isLoading}\n                      className=\"ml-1 text-indigo-600 hover:text-indigo-800 dark:text-indigo-300 dark:hover:text-indigo-100\"\n                    >\n                      <X className=\"h-3 w-3\" />\n                    </button>\n                  </span>\n                ))}\n              </div>\n            )}\n            \n            {/* 添加新标签 */}\n            <div className=\"flex gap-2\">\n              <Input\n                value={newTag}\n                onChange={(e) => setNewTag(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"输入标签名称\"\n                disabled={isLoading}\n                className=\"flex-1\"\n              />\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={addTag}\n                disabled={!newTag.trim() || isLoading}\n              >\n                <Plus className=\"h-4 w-4\" />\n              </Button>\n            </div>\n            <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\n              按回车键或点击加号添加标签\n            </p>\n          </div>\n        </CardContent>\n\n        <CardFooter className=\"flex justify-end space-x-2\">\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            onClick={onCancel}\n            disabled={isLoading}\n          >\n            取消\n          </Button>\n          <Button\n            type=\"submit\"\n            isLoading={isLoading}\n            disabled={isLoading}\n          >\n            {knowledgePoint ? '更新' : '创建'}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;;;AAPA;;;;;;AAgBO,SAAS,mBAAmB,KAKT;QALS,EACjC,cAAc,EACd,QAAQ,EACR,QAAQ,EACR,YAAY,KAAK,EACO,GALS;;IAMjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,kBAAkB;QAClB,MAAM,EAAE;QACR,SAAS,GAAG,YAAY;IAC1B;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,gBAAgB;gBAClB,YAAY;oBACV,MAAM,eAAe,IAAI;oBACzB,aAAa,eAAe,WAAW,IAAI;oBAC3C,kBAAkB,eAAe,gBAAgB;oBACjD,MAAM,eAAe,IAAI,IAAI,EAAE;oBAC/B,SAAS,eAAe,OAAO;gBACjC;YACF,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,aAAa;oBACb,kBAAkB;oBAClB,MAAM,EAAE;oBACR,SAAS;gBACX;YACF;YACA,UAAU,CAAC;QACb;uCAAG;QAAC;KAAe;IAEnB,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,SAAS,gBAAgB,GAAG,KAAK,SAAS,gBAAgB,GAAG,IAAI;YACnE,UAAU,gBAAgB,GAAG;QAC/B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,SAAS;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,oBAAoB,CAAC,OAA8B;QACvD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,YAAY;QACZ,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,SAAS;QACb,MAAM,MAAM,OAAO,IAAI;QACvB,IAAI,OAAO,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;YACvC,kBAAkB,QAAQ;mBAAI,SAAS,IAAI;gBAAE;aAAI;YACjD,UAAU;QACZ;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,kBAAkB,QAAQ,SAAS,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;IAChE;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,iBAAiB,UAAU;;;;;;sCAE9B,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAK,UAAU;;kCACd,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,OAAM;gCACN,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACzD,OAAO,OAAO,IAAI;gCAClB,aAAY;gCACZ,UAAU;;;;;;0CAIZ,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wCAChE,aAAY;wCACZ,MAAM;wCACN,UAAU;wCACV,WAAU;;;;;;;;;;;;0CAKd,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,SAAS,gBAAgB;gDAChC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC9E,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;;oDACb,SAAS,gBAAgB;oDAAC;;;;;;;;;;;;;kDAG/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;oCAEP,OAAO,gBAAgB,kBACtB,6LAAC;wCAAE,WAAU;kDACV,OAAO,gBAAgB;;;;;;;;;;;;0CAM9B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;oCAKlF,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,6LAAC;wCAAI,WAAU;kDACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACvB,6LAAC;gDAEC,WAAU;;oDAET;kEACD,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,UAAU;wDACzB,UAAU;wDACV,WAAU;kEAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;+CAVV;;;;;;;;;;kDAkBb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,YAAY;gDACZ,aAAY;gDACZ,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,OAAO,IAAI,MAAM;0DAE5B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;kCAMjE,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;0CACX;;;;;;0CAGD,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAW;gCACX,UAAU;0CAET,iBAAiB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAMrC;GAnPgB;KAAA", "debugId": null}}, {"offset": {"line": 2570, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/app/knowledge/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { KnowledgePointList } from '@/components/knowledge/KnowledgePointList'\nimport { KnowledgePointForm } from '@/components/knowledge/KnowledgePointForm'\nimport { useKnowledgeGraph } from '@/hooks/useKnowledgeGraph'\nimport { KnowledgePoint } from '@/types'\nimport { Brain, ArrowLeft } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\n\ntype ViewMode = 'list' | 'create' | 'edit'\n\nexport default function KnowledgePage() {\n  const {\n    createKnowledgePoint,\n    updateKnowledgePoint,\n    deleteKnowledgePoint,\n    isLoading,\n    error\n  } = useKnowledgeGraph()\n\n  const [viewMode, setViewMode] = useState<ViewMode>('list')\n  const [editingPoint, setEditingPoint] = useState<KnowledgePoint | null>(null)\n\n  const handleCreateNew = () => {\n    setEditingPoint(null)\n    setViewMode('create')\n  }\n\n  const handleEdit = (point: KnowledgePoint) => {\n    setEditingPoint(point)\n    setViewMode('edit')\n  }\n\n  const handleDelete = async (id: string) => {\n    if (window.confirm('确定要删除这个知识点吗？这将同时删除相关的依赖关系和学习记录。')) {\n      const result = await deleteKnowledgePoint(id)\n      if (!result.success && result.error) {\n        alert(result.error)\n      }\n    }\n  }\n\n  const handleFormSubmit = async (data: Omit<KnowledgePoint, 'id' | 'created_at' | 'updated_at'>) => {\n    try {\n      // TODO: 获取当前用户ID，这里暂时使用占位符\n      const formDataWithUser = {\n        ...data,\n        user_id: '00000000-0000-0000-0000-000000000000' // 临时用户ID\n      }\n\n      let result\n      if (viewMode === 'edit' && editingPoint) {\n        result = await updateKnowledgePoint(editingPoint.id, formDataWithUser)\n      } else {\n        result = await createKnowledgePoint(formDataWithUser)\n      }\n\n      if (result.success) {\n        setViewMode('list')\n        setEditingPoint(null)\n      } else {\n        alert(result.error || '操作失败')\n      }\n    } catch (error) {\n      console.error('表单提交失败:', error)\n      alert('操作失败，请重试')\n    }\n  }\n\n  const handleCancel = () => {\n    setViewMode('list')\n    setEditingPoint(null)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* 页面头部 */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              {viewMode !== 'list' && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={handleCancel}\n                  className=\"mr-4\"\n                >\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  返回\n                </Button>\n              )}\n              <div className=\"flex items-center\">\n                <Brain className=\"h-8 w-8 text-indigo-600 mr-3\" />\n                <div>\n                  <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                    {viewMode === 'list' && '知识管理'}\n                    {viewMode === 'create' && '创建知识点'}\n                    {viewMode === 'edit' && '编辑知识点'}\n                  </h1>\n                  <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                    {viewMode === 'list' && '管理你的知识点和学习进度'}\n                    {viewMode === 'create' && '添加新的知识点到你的学习体系'}\n                    {viewMode === 'edit' && '修改知识点信息'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 错误提示 */}\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <p className=\"text-red-800\">{error}</p>\n          </div>\n        )}\n\n        {/* 主要内容区域 */}\n        <div className=\"max-w-7xl mx-auto\">\n          {viewMode === 'list' && (\n            <KnowledgePointList\n              onCreateNew={handleCreateNew}\n              onEditPoint={handleEdit}\n              onDeletePoint={handleDelete}\n              showActions={true}\n            />\n          )}\n\n          {(viewMode === 'create' || viewMode === 'edit') && (\n            <div className=\"flex justify-center\">\n              <KnowledgePointForm\n                knowledgePoint={editingPoint}\n                onSubmit={handleFormSubmit}\n                onCancel={handleCancel}\n                isLoading={isLoading}\n              />\n            </div>\n          )}\n        </div>\n\n        {/* 底部统计信息 */}\n        {viewMode === 'list' && (\n          <div className=\"mt-12 text-center\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n              <Brain className=\"h-4 w-4 text-indigo-600 mr-2\" />\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                知识图谱核心功能已就绪 - 可以开始创建和管理知识点\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AACA;;;AARA;;;;;;;AAYe,SAAS;;IACtB,MAAM,EACJ,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD;IAEpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAExE,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,YAAY;IACd;IAEA,MAAM,aAAa,CAAC;QAClB,gBAAgB;QAChB,YAAY;IACd;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,oCAAoC;YACrD,MAAM,SAAS,MAAM,qBAAqB;YAC1C,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBACnC,MAAM,OAAO,KAAK;YACpB;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,2BAA2B;YAC3B,MAAM,mBAAmB;gBACvB,GAAG,IAAI;gBACP,SAAS,uCAAuC,SAAS;YAC3D;YAEA,IAAI;YACJ,IAAI,aAAa,UAAU,cAAc;gBACvC,SAAS,MAAM,qBAAqB,aAAa,EAAE,EAAE;YACvD,OAAO;gBACL,SAAS,MAAM,qBAAqB;YACtC;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY;gBACZ,gBAAgB;YAClB,OAAO;gBACL,MAAM,OAAO,KAAK,IAAI;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR;IACF;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,aAAa,wBACZ,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAI1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;;wDACX,aAAa,UAAU;wDACvB,aAAa,YAAY;wDACzB,aAAa,UAAU;;;;;;;8DAE1B,6LAAC;oDAAE,WAAU;;wDACV,aAAa,UAAU;wDACvB,aAAa,YAAY;wDACzB,aAAa,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASnC,uBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;8BAKjC,6LAAC;oBAAI,WAAU;;wBACZ,aAAa,wBACZ,6LAAC,wJAAA,CAAA,qBAAkB;4BACjB,aAAa;4BACb,aAAa;4BACb,eAAe;4BACf,aAAa;;;;;;wBAIhB,CAAC,aAAa,YAAY,aAAa,MAAM,mBAC5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,wJAAA,CAAA,qBAAkB;gCACjB,gBAAgB;gCAChB,UAAU;gCACV,UAAU;gCACV,WAAW;;;;;;;;;;;;;;;;;gBAOlB,aAAa,wBACZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzE;GAhJwB;;QAOlB,oIAAA,CAAA,oBAAiB;;;KAPC", "debugId": null}}]}