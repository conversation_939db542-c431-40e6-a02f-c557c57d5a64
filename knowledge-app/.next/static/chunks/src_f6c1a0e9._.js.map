{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder_key'\n\n// 检查是否为开发环境且使用占位符配置\nconst isDevMode = process.env.NODE_ENV === 'development' && supabaseUrl.includes('placeholder')\n\n// 创建Supabase客户端，开发模式下使用模拟配置\nexport const supabase = isDevMode\n  ? null // 开发模式下返回null，API调用将被模拟\n  : createClient(supabaseUrl, supabaseAnonKey)\n\nexport type Database = {\n  public: {\n    Tables: {\n      knowledge_points: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          difficulty_level: number\n          tags: string[]\n          created_at: string\n          updated_at: string\n          user_id: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          description?: string | null\n          difficulty_level?: number\n          tags?: string[]\n          created_at?: string\n          updated_at?: string\n          user_id: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          description?: string | null\n          difficulty_level?: number\n          tags?: string[]\n          created_at?: string\n          updated_at?: string\n          user_id?: string\n        }\n      }\n      knowledge_dependencies: {\n        Row: {\n          id: string\n          prerequisite_id: string\n          dependent_id: string\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          prerequisite_id: string\n          dependent_id: string\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          prerequisite_id?: string\n          dependent_id?: string\n          created_at?: string\n        }\n      }\n      user_progress: {\n        Row: {\n          id: string\n          user_id: string\n          knowledge_point_id: string\n          mastery_level: number\n          notes: string | null\n          last_reviewed: string | null\n          test_scores: number[]\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          knowledge_point_id: string\n          mastery_level?: number\n          notes?: string | null\n          last_reviewed?: string | null\n          test_scores?: number[]\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          knowledge_point_id?: string\n          mastery_level?: number\n          notes?: string | null\n          last_reviewed?: string | null\n          test_scores?: number[]\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM,cAAc,uEAAwC;AAC5D,MAAM,kBAAkB,uDAA6C;AAErE,oBAAoB;AACpB,MAAM,YAAY,oDAAyB,iBAAiB,YAAY,QAAQ,CAAC;AAG1E,MAAM,WAAW,YACpB,KAAK,wBAAwB;GAC7B,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/lib/auth.ts"], "sourcesContent": ["import { supabase } from './supabase'\nimport { User, Session } from '@supabase/supabase-js'\n\nexport interface AuthUser {\n  id: string\n  email: string\n  name?: string\n  avatar_url?: string\n  created_at: string\n}\n\nexport interface AuthState {\n  user: AuthUser | null\n  session: Session | null\n  loading: boolean\n}\n\n/**\n * 认证服务类\n * 处理用户注册、登录、登出等认证相关操作\n */\nexport class AuthService {\n  \n  /**\n   * 用户注册\n   */\n  async signUp(email: string, password: string, metadata?: { name?: string }) {\n    if (!supabase) {\n      // 开发模式下的模拟注册 - 直接创建会话\n      const mockUser = {\n        id: 'mock-user-' + Date.now(),\n        email,\n        user_metadata: metadata || {},\n        created_at: new Date().toISOString()\n      }\n\n      return {\n        data: {\n          user: mockUser,\n          session: {\n            access_token: 'mock-token',\n            refresh_token: 'mock-refresh',\n            expires_in: 3600,\n            expires_at: Math.floor(Date.now() / 1000) + 3600,\n            token_type: 'bearer',\n            user: mockUser\n          }\n        },\n        error: null\n      }\n    }\n\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: metadata || {}\n      }\n    })\n\n    return { data, error }\n  }\n\n  /**\n   * 用户登录\n   */\n  async signIn(email: string, password: string) {\n    console.log('AuthService.signIn 被调用:', email)\n\n    if (!supabase) {\n      console.log('开发模式：使用模拟登录')\n      // 开发模式下的模拟登录\n      const mockUser = {\n        id: 'mock-user',\n        email,\n        user_metadata: { name: '测试用户' },\n        created_at: new Date().toISOString()\n      }\n\n      const result = {\n        data: {\n          user: mockUser,\n          session: {\n            access_token: 'mock-token',\n            refresh_token: 'mock-refresh',\n            expires_in: 3600,\n            expires_at: Math.floor(Date.now() / 1000) + 3600,\n            token_type: 'bearer',\n            user: mockUser\n          }\n        },\n        error: null\n      }\n\n      console.log('模拟登录结果:', result)\n      return result\n    }\n\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password\n    })\n\n    return { data, error }\n  }\n\n  /**\n   * 第三方登录 (Google)\n   */\n  async signInWithGoogle() {\n    if (!supabase) {\n      // 开发模式下不支持第三方登录\n      return {\n        data: null,\n        error: { message: '开发模式下不支持第三方登录' }\n      }\n    }\n\n    const { data, error } = await supabase.auth.signInWithOAuth({\n      provider: 'google',\n      options: {\n        redirectTo: `${window.location.origin}/auth/callback`\n      }\n    })\n\n    return { data, error }\n  }\n\n  /**\n   * 用户登出\n   */\n  async signOut() {\n    if (!supabase) {\n      // 开发模式下的模拟登出\n      return { error: null }\n    }\n\n    const { error } = await supabase.auth.signOut()\n    return { error }\n  }\n\n  /**\n   * 获取当前用户\n   */\n  async getCurrentUser(): Promise<AuthUser | null> {\n    if (!supabase) {\n      // 开发模式下检查localStorage中的用户信息\n      try {\n        const authStorage = localStorage.getItem('auth-storage')\n        if (authStorage) {\n          const parsed = JSON.parse(authStorage)\n          if (parsed.state?.user) {\n            return parsed.state.user\n          }\n        }\n      } catch (error) {\n        console.warn('获取本地用户信息失败:', error)\n      }\n      return null\n    }\n\n    const { data: { user }, error } = await supabase.auth.getUser()\n    \n    if (error || !user) {\n      return null\n    }\n\n    return {\n      id: user.id,\n      email: user.email || '',\n      name: user.user_metadata?.name || user.email?.split('@')[0],\n      avatar_url: user.user_metadata?.avatar_url,\n      created_at: user.created_at || new Date().toISOString()\n    }\n  }\n\n  /**\n   * 获取当前会话\n   */\n  async getCurrentSession(): Promise<Session | null> {\n    if (!supabase) {\n      // 开发模式下检查是否有有效的用户会话\n      try {\n        const authStorage = localStorage.getItem('auth-storage')\n        if (authStorage) {\n          const parsed = JSON.parse(authStorage)\n          if (parsed.state?.user) {\n            // 返回模拟会话\n            return {\n              access_token: 'mock-token',\n              refresh_token: 'mock-refresh',\n              expires_in: 3600,\n              expires_at: Math.floor(Date.now() / 1000) + 3600,\n              token_type: 'bearer',\n              user: parsed.state.user\n            } as Session\n          }\n        }\n      } catch (error) {\n        console.warn('获取本地会话信息失败:', error)\n      }\n      return null\n    }\n\n    const { data: { session }, error } = await supabase.auth.getSession()\n    \n    if (error) {\n      console.error('获取会话失败:', error)\n      return null\n    }\n\n    return session\n  }\n\n  /**\n   * 刷新会话\n   */\n  async refreshSession() {\n    if (!supabase) {\n      return { data: null, error: null }\n    }\n\n    const { data, error } = await supabase.auth.refreshSession()\n    return { data, error }\n  }\n\n  /**\n   * 更新用户信息\n   */\n  async updateUser(updates: { \n    email?: string\n    password?: string\n    data?: { name?: string, avatar_url?: string }\n  }) {\n    if (!supabase) {\n      // 开发模式下的模拟更新\n      return {\n        data: {\n          user: {\n            id: 'mock-user',\n            email: updates.email || '<EMAIL>',\n            user_metadata: updates.data || { name: '测试用户' }\n          }\n        },\n        error: null\n      }\n    }\n\n    const { data, error } = await supabase.auth.updateUser(updates)\n    return { data, error }\n  }\n\n  /**\n   * 重置密码\n   */\n  async resetPassword(email: string) {\n    if (!supabase) {\n      // 开发模式下的模拟重置\n      return { data: null, error: null }\n    }\n\n    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {\n      redirectTo: `${window.location.origin}/auth/reset-password`\n    })\n\n    return { data, error }\n  }\n\n  /**\n   * 监听认证状态变化\n   */\n  onAuthStateChange(callback: (event: string, session: Session | null) => void) {\n    if (!supabase) {\n      // 开发模式下不监听状态变化\n      return { data: { subscription: { unsubscribe: () => {} } } }\n    }\n\n    return supabase.auth.onAuthStateChange(callback)\n  }\n}\n\n// 导出单例实例\nexport const authService = new AuthService()\n"], "names": [], "mappings": ";;;;AAAA;;AAqBO,MAAM;IAEX;;GAEC,GACD,MAAM,OAAO,KAAa,EAAE,QAAgB,EAAE,QAA4B,EAAE;QAC1E,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,sBAAsB;YACtB,MAAM,WAAW;gBACf,IAAI,eAAe,KAAK,GAAG;gBAC3B;gBACA,eAAe,YAAY,CAAC;gBAC5B,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,OAAO;gBACL,MAAM;oBACJ,MAAM;oBACN,SAAS;wBACP,cAAc;wBACd,eAAe;wBACf,YAAY;wBACZ,YAAY,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,QAAQ;wBAC5C,YAAY;wBACZ,MAAM;oBACR;gBACF;gBACA,OAAO;YACT;QACF;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM,YAAY,CAAC;YACrB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA;;GAEC,GACD,MAAM,OAAO,KAAa,EAAE,QAAgB,EAAE;QAC5C,QAAQ,GAAG,CAAC,2BAA2B;QAEvC,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,QAAQ,GAAG,CAAC;YACZ,aAAa;YACb,MAAM,WAAW;gBACf,IAAI;gBACJ;gBACA,eAAe;oBAAE,MAAM;gBAAO;gBAC9B,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,SAAS;gBACb,MAAM;oBACJ,MAAM;oBACN,SAAS;wBACP,cAAc;wBACd,eAAe;wBACf,YAAY;wBACZ,YAAY,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,QAAQ;wBAC5C,YAAY;wBACZ,MAAM;oBACR;gBACF;gBACA,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,WAAW;YACvB,OAAO;QACT;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA;;GAEC,GACD,MAAM,mBAAmB;QACvB,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,gBAAgB;YAChB,OAAO;gBACL,MAAM;gBACN,OAAO;oBAAE,SAAS;gBAAgB;YACpC;QACF;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;YAC1D,UAAU;YACV,SAAS;gBACP,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;YACxC;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA;;GAEC,GACD,MAAM,UAAU;QACd,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,aAAa;YACb,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC7C,OAAO;YAAE;QAAM;IACjB;IAEA;;GAEC,GACD,MAAM,iBAA2C;YA0BvC,qBAA4B,aACtB;QA1Bd,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,4BAA4B;YAC5B,IAAI;gBACF,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;wBAEX;oBADJ,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,KAAI,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,IAAI,EAAE;wBACtB,OAAO,OAAO,KAAK,CAAC,IAAI;oBAC1B;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,eAAe;YAC9B;YACA,OAAO;QACT;QAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAE7D,IAAI,SAAS,CAAC,MAAM;YAClB,OAAO;QACT;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK,IAAI;YACrB,MAAM,EAAA,sBAAA,KAAK,aAAa,cAAlB,0CAAA,oBAAoB,IAAI,OAAI,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3D,UAAU,GAAE,uBAAA,KAAK,aAAa,cAAlB,2CAAA,qBAAoB,UAAU;YAC1C,YAAY,KAAK,UAAU,IAAI,IAAI,OAAO,WAAW;QACvD;IACF;IAEA;;GAEC,GACD,MAAM,oBAA6C;QACjD,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,oBAAoB;YACpB,IAAI;gBACF,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;wBAEX;oBADJ,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,KAAI,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,IAAI,EAAE;wBACtB,SAAS;wBACT,OAAO;4BACL,cAAc;4BACd,eAAe;4BACf,YAAY;4BACZ,YAAY,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,QAAQ;4BAC5C,YAAY;4BACZ,MAAM,OAAO,KAAK,CAAC,IAAI;wBACzB;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,eAAe;YAC9B;YACA,OAAO;QACT;QAEA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;QAEnE,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,WAAW;YACzB,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,iBAAiB;QACrB,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAK;QACnC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,cAAc;QAC1D,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA;;GAEC,GACD,MAAM,WAAW,OAIhB,EAAE;QACD,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,aAAa;YACb,OAAO;gBACL,MAAM;oBACJ,MAAM;wBACJ,IAAI;wBACJ,OAAO,QAAQ,KAAK,IAAI;wBACxB,eAAe,QAAQ,IAAI,IAAI;4BAAE,MAAM;wBAAO;oBAChD;gBACF;gBACA,OAAO;YACT;QACF;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;QACvD,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA;;GAEC,GACD,MAAM,cAAc,KAAa,EAAE;QACjC,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,aAAa;YACb,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAK;QACnC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;YACvE,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;QACxC;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA;;GAEC,GACD,kBAAkB,QAA0D,EAAE;QAC5E,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,eAAe;YACf,OAAO;gBAAE,MAAM;oBAAE,cAAc;wBAAE,aAAa,KAAO;oBAAE;gBAAE;YAAE;QAC7D;QAEA,OAAO,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACzC;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/store/useAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { AuthUser, AuthState } from '@/lib/auth'\nimport { Session } from '@supabase/supabase-js'\n\ninterface AuthStore extends AuthState {\n  // Actions\n  setUser: (user: AuthUser | null) => void\n  setSession: (session: Session | null) => void\n  setLoading: (loading: boolean) => void\n  clearAuth: () => void\n}\n\nexport const useAuthStore = create<AuthStore>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      session: null,\n      loading: true,\n\n      // Actions\n      setUser: (user) => set({ user }),\n      setSession: (session) => set({ session }),\n      setLoading: (loading) => set({ loading }),\n      clearAuth: () => set({ user: null, session: null, loading: false })\n    }),\n    {\n      name: 'auth-storage',\n      // 只持久化用户信息，不持久化session（安全考虑）\n      partialize: (state) => ({ \n        user: state.user,\n        loading: false // 重新加载时不保持loading状态\n      })\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAYO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,SAAS;QACT,SAAS;QAET,UAAU;QACV,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAC9B,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QACvC,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QACvC,WAAW,IAAM,IAAI;gBAAE,MAAM;gBAAM,SAAS;gBAAM,SAAS;YAAM;IACnE,CAAC,GACD;IACE,MAAM;IACN,6BAA6B;IAC7B,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,SAAS,MAAM,oBAAoB;QACrC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/hooks/useAuth.ts"], "sourcesContent": ["import { useEffect, useCallback } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { authService } from '@/lib/auth'\nimport { useAuthStore } from '@/store/useAuthStore'\n\n/**\n * 认证相关的自定义Hook\n * 提供登录、登出、注册等功能\n */\nexport function useAuth() {\n  const router = useRouter()\n  const {\n    user,\n    session,\n    loading,\n    setUser,\n    setSession,\n    setLoading,\n    clearAuth\n  } = useAuthStore()\n\n  // 计算认证状态\n  const isAuthenticated = !!user\n\n  // 调试信息 (仅在开发模式下)\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'development') {\n      console.log('useAuth 状态变化:', { user: !!user, session: !!session, isAuthenticated, loading })\n    }\n  }, [user, session, isAuthenticated, loading])\n\n  /**\n   * 初始化认证状态\n   */\n  const initializeAuth = useCallback(async () => {\n    setLoading(true)\n    \n    try {\n      const [currentUser, currentSession] = await Promise.all([\n        authService.getCurrentUser(),\n        authService.getCurrentSession()\n      ])\n      \n      setUser(currentUser)\n      setSession(currentSession)\n    } catch (error) {\n      console.error('初始化认证状态失败:', error)\n      clearAuth()\n    } finally {\n      setLoading(false)\n    }\n  }, [setUser, setSession, setLoading, clearAuth])\n\n  /**\n   * 用户登录\n   */\n  const signIn = useCallback(async (email: string, password: string) => {\n    setLoading(true)\n    \n    try {\n      const { data, error } = await authService.signIn(email, password)\n      \n      if (error) {\n        throw new Error(error.message)\n      }\n      \n      if (data.user && data.session) {\n        const authUser = {\n          id: data.user.id,\n          email: data.user.email || '',\n          name: data.user.user_metadata?.name || data.user.email?.split('@')[0],\n          avatar_url: data.user.user_metadata?.avatar_url,\n          created_at: data.user.created_at || new Date().toISOString()\n        }\n\n        console.log('设置用户状态:', authUser)\n        setUser(authUser)\n        setSession(data.session)\n        console.log('用户状态已设置')\n\n        return { success: true }\n      }\n      \n      throw new Error('登录失败')\n    } catch (error) {\n      console.error('登录失败:', error)\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : '登录失败' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }, [setUser, setSession, setLoading])\n\n  /**\n   * 用户注册\n   */\n  const signUp = useCallback(async (email: string, password: string, name?: string) => {\n    setLoading(true)\n    \n    try {\n      const { data, error } = await authService.signUp(email, password, { name })\n      \n      if (error) {\n        throw new Error(error.message)\n      }\n      \n      // 注册成功，如果有session则直接登录\n      if (data.session && data.user) {\n        const authUser = {\n          id: data.user.id,\n          email: data.user.email || '',\n          name: data.user.user_metadata?.name || data.user.email?.split('@')[0],\n          avatar_url: data.user.user_metadata?.avatar_url,\n          created_at: data.user.created_at || new Date().toISOString()\n        }\n\n        setUser(authUser)\n        setSession(data.session)\n\n        return {\n          success: true,\n          needsVerification: false,\n          message: '注册成功'\n        }\n      }\n\n      // 需要邮箱验证\n      return {\n        success: true,\n        needsVerification: true,\n        message: '请检查邮箱并点击验证链接'\n      }\n    } catch (error) {\n      console.error('注册失败:', error)\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : '注册失败' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }, [setLoading])\n\n  /**\n   * 第三方登录\n   */\n  const signInWithGoogle = useCallback(async () => {\n    setLoading(true)\n    \n    try {\n      const { data, error } = await authService.signInWithGoogle()\n      \n      if (error) {\n        throw new Error(error.message)\n      }\n      \n      // OAuth登录会重定向，这里不需要处理结果\n      return { success: true }\n    } catch (error) {\n      console.error('Google登录失败:', error)\n      setLoading(false)\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Google登录失败' \n      }\n    }\n  }, [setLoading])\n\n  /**\n   * 用户登出\n   */\n  const signOut = useCallback(async () => {\n    setLoading(true)\n    \n    try {\n      const { error } = await authService.signOut()\n      \n      if (error) {\n        throw new Error(error.message)\n      }\n      \n      clearAuth()\n      router.push('/auth/signin')\n      \n      return { success: true }\n    } catch (error) {\n      console.error('登出失败:', error)\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : '登出失败' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }, [clearAuth, router, setLoading])\n\n  /**\n   * 更新用户信息\n   */\n  const updateProfile = useCallback(async (updates: { \n    name?: string\n    email?: string\n    avatar_url?: string \n  }) => {\n    if (!user) return { success: false, error: '用户未登录' }\n    \n    setLoading(true)\n    \n    try {\n      const { data, error } = await authService.updateUser({\n        email: updates.email,\n        data: {\n          name: updates.name,\n          avatar_url: updates.avatar_url\n        }\n      })\n      \n      if (error) {\n        throw new Error(error.message)\n      }\n      \n      if (data.user) {\n        const updatedUser = {\n          ...user,\n          email: data.user.email || user.email,\n          name: data.user.user_metadata?.name || user.name,\n          avatar_url: data.user.user_metadata?.avatar_url || user.avatar_url\n        }\n        \n        setUser(updatedUser)\n      }\n      \n      return { success: true }\n    } catch (error) {\n      console.error('更新用户信息失败:', error)\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : '更新失败' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }, [user, setUser, setLoading])\n\n  /**\n   * 重置密码\n   */\n  const resetPassword = useCallback(async (email: string) => {\n    setLoading(true)\n    \n    try {\n      const { error } = await authService.resetPassword(email)\n      \n      if (error) {\n        throw new Error(error.message)\n      }\n      \n      return { \n        success: true, \n        message: '密码重置邮件已发送，请检查邮箱' \n      }\n    } catch (error) {\n      console.error('重置密码失败:', error)\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : '重置密码失败' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }, [setLoading])\n\n  // 监听认证状态变化\n  useEffect(() => {\n    const { data: { subscription } } = authService.onAuthStateChange(\n      async (event, session) => {\n        console.log('认证状态变化:', event, session)\n        \n        if (event === 'SIGNED_IN' && session) {\n          const authUser = {\n            id: session.user.id,\n            email: session.user.email || '',\n            name: session.user.user_metadata?.name || session.user.email?.split('@')[0],\n            avatar_url: session.user.user_metadata?.avatar_url,\n            created_at: session.user.created_at || new Date().toISOString()\n          }\n          \n          setUser(authUser)\n          setSession(session)\n        } else if (event === 'SIGNED_OUT') {\n          clearAuth()\n        }\n        \n        setLoading(false)\n      }\n    )\n\n    // 初始化认证状态\n    initializeAuth()\n\n    return () => {\n      subscription.unsubscribe()\n    }\n  }, [initializeAuth, setUser, setSession, clearAuth, setLoading])\n\n  return {\n    // State\n    user,\n    session,\n    loading,\n    isAuthenticated,\n    \n    // Actions\n    signIn,\n    signUp,\n    signInWithGoogle,\n    signOut,\n    updateProfile,\n    resetPassword,\n    \n    // Utils\n    initializeAuth\n  }\n}\n"], "names": [], "mappings": ";;;AA0BQ;AA1BR;AACA;AACA;AACA;;;;;;AAMO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,EACV,SAAS,EACV,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEf,SAAS;IACT,MAAM,kBAAkB,CAAC,CAAC;IAE1B,iBAAiB;IACjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,wCAA4C;gBAC1C,QAAQ,GAAG,CAAC,iBAAiB;oBAAE,MAAM,CAAC,CAAC;oBAAM,SAAS,CAAC,CAAC;oBAAS;oBAAiB;gBAAQ;YAC5F;QACF;4BAAG;QAAC;QAAM;QAAS;QAAiB;KAAQ;IAE5C;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YACjC,WAAW;YAEX,IAAI;gBACF,MAAM,CAAC,aAAa,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACtD,qHAAA,CAAA,cAAW,CAAC,cAAc;oBAC1B,qHAAA,CAAA,cAAW,CAAC,iBAAiB;iBAC9B;gBAED,QAAQ;gBACR,WAAW;YACb,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,cAAc;gBAC5B;YACF,SAAU;gBACR,WAAW;YACb;QACF;8CAAG;QAAC;QAAS;QAAY;QAAY;KAAU;IAE/C;;GAEC,GACD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE,OAAO,OAAe;YAC/C,WAAW;YAEX,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,qHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,OAAO;gBAExD,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI,KAAK,IAAI,IAAI,KAAK,OAAO,EAAE;wBAIrB,0BAAiC,kBAC3B;oBAJd,MAAM,WAAW;wBACf,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI;wBAC1B,MAAM,EAAA,2BAAA,KAAK,IAAI,CAAC,aAAa,cAAvB,+CAAA,yBAAyB,IAAI,OAAI,mBAAA,KAAK,IAAI,CAAC,KAAK,cAAf,uCAAA,iBAAiB,KAAK,CAAC,IAAI,CAAC,EAAE;wBACrE,UAAU,GAAE,4BAAA,KAAK,IAAI,CAAC,aAAa,cAAvB,gDAAA,0BAAyB,UAAU;wBAC/C,YAAY,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;oBAC5D;oBAEA,QAAQ,GAAG,CAAC,WAAW;oBACvB,QAAQ;oBACR,WAAW,KAAK,OAAO;oBACvB,QAAQ,GAAG,CAAC;oBAEZ,OAAO;wBAAE,SAAS;oBAAK;gBACzB;gBAEA,MAAM,IAAI,MAAM;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,SAAS;gBACvB,OAAO;oBACL,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF,SAAU;gBACR,WAAW;YACb;QACF;sCAAG;QAAC;QAAS;QAAY;KAAW;IAEpC;;GAEC,GACD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE,OAAO,OAAe,UAAkB;YACjE,WAAW;YAEX,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,qHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,OAAO,UAAU;oBAAE;gBAAK;gBAEzE,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;wBAIrB,0BAAiC,kBAC3B;oBAJd,MAAM,WAAW;wBACf,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI;wBAC1B,MAAM,EAAA,2BAAA,KAAK,IAAI,CAAC,aAAa,cAAvB,+CAAA,yBAAyB,IAAI,OAAI,mBAAA,KAAK,IAAI,CAAC,KAAK,cAAf,uCAAA,iBAAiB,KAAK,CAAC,IAAI,CAAC,EAAE;wBACrE,UAAU,GAAE,4BAAA,KAAK,IAAI,CAAC,aAAa,cAAvB,gDAAA,0BAAyB,UAAU;wBAC/C,YAAY,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;oBAC5D;oBAEA,QAAQ;oBACR,WAAW,KAAK,OAAO;oBAEvB,OAAO;wBACL,SAAS;wBACT,mBAAmB;wBACnB,SAAS;oBACX;gBACF;gBAEA,SAAS;gBACT,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,SAAS;gBACvB,OAAO;oBACL,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF,SAAU;gBACR,WAAW;YACb;QACF;sCAAG;QAAC;KAAW;IAEf;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACnC,WAAW;YAEX,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,qHAAA,CAAA,cAAW,CAAC,gBAAgB;gBAE1D,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,wBAAwB;gBACxB,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,eAAe;gBAC7B,WAAW;gBACX,OAAO;oBACL,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF;QACF;gDAAG;QAAC;KAAW;IAEf;;GAEC,GACD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wCAAE;YAC1B,WAAW;YAEX,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;gBAE3C,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA;gBACA,OAAO,IAAI,CAAC;gBAEZ,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,SAAS;gBACvB,OAAO;oBACL,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF,SAAU;gBACR,WAAW;YACb;QACF;uCAAG;QAAC;QAAW;QAAQ;KAAW;IAElC;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,OAAO;YAKvC,IAAI,CAAC,MAAM,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAQ;YAEnD,WAAW;YAEX,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,qHAAA,CAAA,cAAW,CAAC,UAAU,CAAC;oBACnD,OAAO,QAAQ,KAAK;oBACpB,MAAM;wBACJ,MAAM,QAAQ,IAAI;wBAClB,YAAY,QAAQ,UAAU;oBAChC;gBACF;gBAEA,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI,KAAK,IAAI,EAAE;wBAIL,0BACM;oBAJd,MAAM,cAAc;wBAClB,GAAG,IAAI;wBACP,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK;wBACpC,MAAM,EAAA,2BAAA,KAAK,IAAI,CAAC,aAAa,cAAvB,+CAAA,yBAAyB,IAAI,KAAI,KAAK,IAAI;wBAChD,YAAY,EAAA,4BAAA,KAAK,IAAI,CAAC,aAAa,cAAvB,gDAAA,0BAAyB,UAAU,KAAI,KAAK,UAAU;oBACpE;oBAEA,QAAQ;gBACV;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,OAAO;oBACL,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF,SAAU;gBACR,WAAW;YACb;QACF;6CAAG;QAAC;QAAM;QAAS;KAAW;IAE9B;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,OAAO;YACvC,WAAW;YAEX,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,qHAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAElD,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,WAAW;gBACzB,OAAO;oBACL,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF,SAAU;gBACR,WAAW;YACb;QACF;6CAAG;QAAC;KAAW;IAEf,WAAW;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,qHAAA,CAAA,cAAW,CAAC,iBAAiB;qCAC9D,OAAO,OAAO;oBACZ,QAAQ,GAAG,CAAC,WAAW,OAAO;oBAE9B,IAAI,UAAU,eAAe,SAAS;4BAI5B,6BAAoC,qBAC9B;wBAJd,MAAM,WAAW;4BACf,IAAI,QAAQ,IAAI,CAAC,EAAE;4BACnB,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;4BAC7B,MAAM,EAAA,8BAAA,QAAQ,IAAI,CAAC,aAAa,cAA1B,kDAAA,4BAA4B,IAAI,OAAI,sBAAA,QAAQ,IAAI,CAAC,KAAK,cAAlB,0CAAA,oBAAoB,KAAK,CAAC,IAAI,CAAC,EAAE;4BAC3E,UAAU,GAAE,+BAAA,QAAQ,IAAI,CAAC,aAAa,cAA1B,mDAAA,6BAA4B,UAAU;4BAClD,YAAY,QAAQ,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;wBAC/D;wBAEA,QAAQ;wBACR,WAAW;oBACb,OAAO,IAAI,UAAU,cAAc;wBACjC;oBACF;oBAEA,WAAW;gBACb;;YAGF,UAAU;YACV;YAEA;qCAAO;oBACL,aAAa,WAAW;gBAC1B;;QACF;4BAAG;QAAC;QAAgB;QAAS;QAAY;QAAW;KAAW;IAE/D,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QAEA,QAAQ;QACR;IACF;AACF;GA5TgB;;QACC,qIAAA,CAAA,YAAS;QASpB,+HAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 699, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/components/auth/AuthGuard.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Brain } from 'lucide-react'\n\ninterface AuthGuardProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  redirectTo?: string\n}\n\n/**\n * 认证保护组件\n * 用于保护需要登录才能访问的页面\n */\nexport function AuthGuard({ \n  children, \n  fallback,\n  redirectTo = '/auth/signin' \n}: AuthGuardProps) {\n  const router = useRouter()\n  const { isAuthenticated, loading } = useAuth()\n\n  useEffect(() => {\n    // 如果加载完成且用户未认证，重定向到登录页\n    if (!loading && !isAuthenticated) {\n      router.push(redirectTo)\n    }\n  }, [loading, isAuthenticated, router, redirectTo])\n\n  // 显示加载状态\n  if (loading) {\n    return fallback || (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Brain className=\"w-12 h-12 text-indigo-600 mx-auto mb-4 animate-pulse\" />\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Learn Everything\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            正在验证身份...\n          </p>\n          <div className=\"mt-4\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"></div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  // 如果用户未认证，显示空内容（即将重定向）\n  if (!isAuthenticated) {\n    return null\n  }\n\n  // 用户已认证，显示受保护的内容\n  return <>{children}</>\n}\n\n/**\n * 反向认证保护组件\n * 用于保护已登录用户不应该访问的页面（如登录页）\n */\nexport function GuestGuard({ \n  children, \n  redirectTo = '/knowledge' \n}: {\n  children: React.ReactNode\n  redirectTo?: string\n}) {\n  const router = useRouter()\n  const { isAuthenticated, loading } = useAuth()\n\n  useEffect(() => {\n    console.log('GuestGuard状态:', { loading, isAuthenticated })\n    // 如果加载完成且用户已认证，重定向到指定页面\n    if (!loading && isAuthenticated) {\n      console.log('GuestGuard: 用户已认证，重定向到', redirectTo)\n      router.push(redirectTo)\n    }\n  }, [loading, isAuthenticated, router, redirectTo])\n\n  // 显示加载状态\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Brain className=\"w-12 h-12 text-indigo-600 mx-auto mb-4 animate-pulse\" />\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            正在加载...\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  // 如果用户已认证，显示空内容（即将重定向）\n  if (isAuthenticated) {\n    return null\n  }\n\n  // 用户未认证，显示内容\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAiBO,SAAS,UAAU,KAIT;QAJS,EACxB,QAAQ,EACR,QAAQ,EACR,aAAa,cAAc,EACZ,GAJS;;IAKxB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,uBAAuB;YACvB,IAAI,CAAC,WAAW,CAAC,iBAAiB;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAS;QAAiB;QAAQ;KAAW;IAEjD,SAAS;IACT,IAAI,SAAS;QACX,OAAO,0BACL,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;kCAGhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,uBAAuB;IACvB,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,iBAAiB;IACjB,qBAAO;kBAAG;;AACZ;GA1CgB;;QAKC,qIAAA,CAAA,YAAS;QACa,0HAAA,CAAA,UAAO;;;KAN9B;AAgDT,SAAS,WAAW,KAM1B;QAN0B,EACzB,QAAQ,EACR,aAAa,YAAY,EAI1B,GAN0B;;IAOzB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,QAAQ,GAAG,CAAC,iBAAiB;gBAAE;gBAAS;YAAgB;YACxD,wBAAwB;YACxB,IAAI,CAAC,WAAW,iBAAiB;gBAC/B,QAAQ,GAAG,CAAC,0BAA0B;gBACtC,OAAO,IAAI,CAAC;YACd;QACF;+BAAG;QAAC;QAAS;QAAiB;QAAQ;KAAW;IAEjD,SAAS;IACT,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,uBAAuB;IACvB,IAAI,iBAAiB;QACnB,OAAO;IACT;IAEA,aAAa;IACb,qBAAO;kBAAG;;AACZ;IAxCgB;;QAOC,qIAAA,CAAA,YAAS;QACa,0HAAA,CAAA,UAAO;;;MAR9B", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { clsx } from 'clsx'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    isLoading = false,\n    disabled,\n    children, \n    ...props \n  }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variantClasses = {\n      primary: 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'\n    }\n    \n    const sizeClasses = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base'\n    }\n    \n    return (\n      <button\n        className={clsx(\n          baseClasses,\n          variantClasses[variant],\n          sizeClasses[size],\n          className\n        )}\n        disabled={disabled || isLoading}\n        ref={ref}\n        {...props}\n      >\n        {isLoading && (\n          <svg \n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\" \n            xmlns=\"http://www.w3.org/2000/svg\" \n            fill=\"none\" \n            viewBox=\"0 0 24 24\"\n          >\n            <circle \n              className=\"opacity-25\" \n              cx=\"12\" \n              cy=\"12\" \n              r=\"10\" \n              stroke=\"currentColor\" \n              strokeWidth=\"4\"\n            />\n            <path \n              className=\"opacity-75\" \n              fill=\"currentColor\" \n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAQG;QARF,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,2BACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react'\nimport { clsx } from 'clsx'\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  helperText?: string\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, label, error, helperText, id, ...props }, ref) => {\n    const generatedId = React.useId()\n    const inputId = id || generatedId\n    \n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label \n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <input\n          id={inputId}\n          className={clsx(\n            'block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm',\n            'placeholder:text-gray-400 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500',\n            'disabled:cursor-not-allowed disabled:opacity-50',\n            'dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-500',\n            'dark:focus:border-indigo-400 dark:focus:ring-indigo-400',\n            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n            {error}\n          </p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAQA,MAAM,sBAAQ,GAAA,6JAAA,CAAA,UAAK,CAAC,UAAU,SAC5B,QAAwD;QAAvD,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,OAAO;;IACpD,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,KAAK;IAC/B,MAAM,UAAU,MAAM;IAEtB,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBACC,IAAI;gBACJ,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,oEACA,2GACA,mDACA,wFACA,2DACA,SAAS,0DACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAGJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { clsx } from 'clsx'\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={clsx(\n        'rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={clsx('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={clsx('p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={clsx('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\n\nCard.displayName = 'Card'\nCardHeader.displayName = 'CardHeader'\nCardContent.displayName = 'CardContent'\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardContent, CardFooter }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAkBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,8FACA;QAED,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,iCAAiC;QAChD,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAC3B,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,8BAA8B;QAC7C,GAAG,KAAK;kBAER;;;;;;;;AAKP,KAAK,WAAW,GAAG;AACnB,WAAW,WAAW,GAAG;AACzB,YAAY,WAAW,GAAG;AAC1B,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1142, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/debug/test/learn-everything/knowledge-app/src/app/auth/signin/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/hooks/useAuth'\nimport { GuestGuard } from '@/components/auth/AuthGuard'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Card, CardHeader, CardContent, CardFooter } from '@/components/ui/Card'\nimport { Brain, Mail, Lock, Eye, EyeOff } from 'lucide-react'\n\nexport default function SignInPage() {\n  const router = useRouter()\n  const { signIn, signInWithGoogle, loading } = useAuth()\n  \n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  })\n  const [showPassword, setShowPassword] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleInputChange = (field: keyof typeof formData, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    if (error) setError('') // 清除错误信息\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n\n    if (!formData.email || !formData.password) {\n      setError('请填写所有必填字段')\n      return\n    }\n\n    console.log('开始登录:', formData.email)\n    const result = await signIn(formData.email, formData.password)\n    console.log('登录结果:', result)\n\n    if (result.success) {\n      console.log('登录成功，GuestGuard将自动处理跳转')\n      // 不需要手动跳转，GuestGuard会检测到用户已登录并自动重定向\n    } else {\n      console.log('登录失败:', result.error)\n      setError(result.error || '登录失败')\n    }\n  }\n\n  const handleGoogleSignIn = async () => {\n    const result = await signInWithGoogle()\n    if (!result.success) {\n      setError(result.error || 'Google登录失败')\n    }\n  }\n\n  return (\n    <GuestGuard>\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"flex justify-center mb-4\">\n            <Brain className=\"w-12 h-12 text-indigo-600\" />\n          </div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            欢迎回来\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            登录到你的知识管理系统\n          </p>\n        </CardHeader>\n\n        <form onSubmit={handleSubmit}>\n          <CardContent className=\"space-y-4\">\n            {error && (\n              <div className=\"p-3 bg-red-50 border border-red-200 rounded-lg\">\n                <p className=\"text-red-800 text-sm\">{error}</p>\n              </div>\n            )}\n\n            <div className=\"relative\">\n              <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <Input\n                type=\"email\"\n                placeholder=\"邮箱地址\"\n                value={formData.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                className=\"pl-10\"\n                disabled={loading}\n                required\n              />\n            </div>\n\n            <div className=\"relative\">\n              <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <Input\n                type={showPassword ? 'text' : 'password'}\n                placeholder=\"密码\"\n                value={formData.password}\n                onChange={(e) => handleInputChange('password', e.target.value)}\n                className=\"pl-10 pr-10\"\n                disabled={loading}\n                required\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                disabled={loading}\n              >\n                {showPassword ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n              </button>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  className=\"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500\"\n                />\n                <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">\n                  记住我\n                </span>\n              </label>\n              <Link \n                href=\"/auth/forgot-password\"\n                className=\"text-sm text-indigo-600 hover:text-indigo-500\"\n              >\n                忘记密码？\n              </Link>\n            </div>\n          </CardContent>\n\n          <CardFooter className=\"space-y-4\">\n            <Button\n              type=\"submit\"\n              className=\"w-full\"\n              isLoading={loading}\n              disabled={loading}\n            >\n              登录\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300 dark:border-gray-600\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white dark:bg-gray-800 text-gray-500\">\n                  或者\n                </span>\n              </div>\n            </div>\n\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              className=\"w-full\"\n              onClick={handleGoogleSignIn}\n              disabled={loading}\n            >\n              <svg className=\"w-4 h-4 mr-2\" viewBox=\"0 0 24 24\">\n                <path\n                  fill=\"currentColor\"\n                  d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                />\n                <path\n                  fill=\"currentColor\"\n                  d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                />\n                <path\n                  fill=\"currentColor\"\n                  d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                />\n                <path\n                  fill=\"currentColor\"\n                  d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                />\n              </svg>\n              使用 Google 登录\n            </Button>\n\n            <div className=\"text-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                还没有账户？{' '}\n                <Link \n                  href=\"/auth/signup\"\n                  className=\"text-indigo-600 hover:text-indigo-500 font-medium\"\n                >\n                  立即注册\n                </Link>\n              </span>\n            </div>\n          </CardFooter>\n        </form>\n      </Card>\n      </div>\n    </GuestGuard>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEpD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,oBAAoB,CAAC,OAA8B;QACvD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,IAAI,OAAO,SAAS,KAAI,SAAS;IACnC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,EAAE;YACzC,SAAS;YACT;QACF;QAEA,QAAQ,GAAG,CAAC,SAAS,SAAS,KAAK;QACnC,MAAM,SAAS,MAAM,OAAO,SAAS,KAAK,EAAE,SAAS,QAAQ;QAC7D,QAAQ,GAAG,CAAC,SAAS;QAErB,IAAI,OAAO,OAAO,EAAE;YAClB,QAAQ,GAAG,CAAC;QACZ,oCAAoC;QACtC,OAAO;YACL,QAAQ,GAAG,CAAC,SAAS,OAAO,KAAK;YACjC,SAAS,OAAO,KAAK,IAAI;QAC3B;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,SAAS,MAAM;QACrB,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,SAAS,OAAO,KAAK,IAAI;QAC3B;IACF;IAEA,qBACE,6LAAC,0IAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;sBACf,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAKlD,6LAAC;wBAAK,UAAU;;0CACd,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;oCACpB,uBACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;kDAIzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,WAAU;gDACV,UAAU;gDACV,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAM,eAAe,SAAS;gDAC9B,aAAY;gDACZ,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC7D,WAAU;gDACV,UAAU;gDACV,QAAQ;;;;;;0DAEV,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,CAAC;gDAChC,WAAU;gDACV,UAAU;0DAET,6BAAe,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;yEAAe,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAgD;;;;;;;;;;;;0DAIlE,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAML,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,WAAW;wCACX,UAAU;kDACX;;;;;;kDAID,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;0DAEjB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+C;;;;;;;;;;;;;;;;;kDAMnE,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;wCACT,UAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,SAAQ;;kEACpC,6LAAC;wDACC,MAAK;wDACL,GAAE;;;;;;kEAEJ,6LAAC;wDACC,MAAK;wDACL,GAAE;;;;;;kEAEJ,6LAAC;wDACC,MAAK;wDACL,GAAE;;;;;;kEAEJ,6LAAC;wDACC,MAAK;wDACL,GAAE;;;;;;;;;;;;4CAEA;;;;;;;kDAIR,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDAA2C;gDAClD;8DACP,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWjB;GA5LwB;;QACP,qIAAA,CAAA,YAAS;QACsB,0HAAA,CAAA,UAAO;;;KAF/B", "debugId": null}}]}