-- Sample data for testing (replace with actual user IDs when testing)
-- Note: This is for development/testing purposes only

-- Insert sample knowledge points for mathematics learning path
INSERT INTO knowledge_points (id, name, description, difficulty_level, tags, user_id) VALUES
('550e8400-e29b-41d4-a716-446655440001', '基础算术', '加减乘除的基本运算', 1, ARRAY['数学', '基础'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-446655440002', '分数运算', '分数的加减乘除运算', 2, ARRAY['数学', '分数'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-446655440003', '代数基础', '变量和简单方程', 3, ARRAY['数学', '代数'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-446655440004', '一元一次方程', '解一元一次方程', 4, ARRAY['数学', '方程'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-446655440005', '函数概念', '函数的定义和基本性质', 5, ARRAY['数学', '函数'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-446655440006', '二次函数', '二次函数的图像和性质', 6, ARRAY['数学', '函数', '二次'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-446655440007', '导数概念', '导数的定义和几何意义', 7, ARRAY['数学', '微积分'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-446655440008', '导数运算', '基本函数的导数计算', 8, ARRAY['数学', '微积分'], '00000000-0000-0000-0000-000000000000');

-- Insert sample knowledge points for programming learning path
INSERT INTO knowledge_points (id, name, description, difficulty_level, tags, user_id) VALUES
('550e8400-e29b-41d4-a716-446655440009', '编程基础概念', '变量、数据类型、控制结构', 2, ARRAY['编程', '基础'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-44665544000a', 'JavaScript语法', 'JavaScript基本语法和特性', 3, ARRAY['编程', 'JavaScript'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-44665544000b', 'DOM操作', '文档对象模型操作', 4, ARRAY['编程', 'JavaScript', 'DOM'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-44665544000c', 'React基础', 'React组件和JSX', 5, ARRAY['编程', 'React', '前端'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-44665544000d', 'React Hooks', 'useState, useEffect等Hooks', 6, ARRAY['编程', 'React', 'Hooks'], '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-44665544000e', '状态管理', 'Redux或Zustand状态管理', 7, ARRAY['编程', 'React', '状态管理'], '00000000-0000-0000-0000-000000000000');

-- Insert dependencies (DAG structure)
-- Mathematics dependencies
INSERT INTO knowledge_dependencies (prerequisite_id, dependent_id) VALUES
('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002'), -- 基础算术 -> 分数运算
('550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003'), -- 分数运算 -> 代数基础
('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440004'), -- 代数基础 -> 一元一次方程
('550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440005'), -- 一元一次方程 -> 函数概念
('550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440006'), -- 函数概念 -> 二次函数
('550e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440007'), -- 二次函数 -> 导数概念
('550e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-446655440008'); -- 导数概念 -> 导数运算

-- Programming dependencies
INSERT INTO knowledge_dependencies (prerequisite_id, dependent_id) VALUES
('550e8400-e29b-41d4-a716-446655440009', '550e8400-e29b-41d4-a716-44665544000a'), -- 编程基础 -> JavaScript语法
('550e8400-e29b-41d4-a716-44665544000a', '550e8400-e29b-41d4-a716-44665544000b'), -- JavaScript语法 -> DOM操作
('550e8400-e29b-41d4-a716-44665544000b', '550e8400-e29b-41d4-a716-44665544000c'), -- DOM操作 -> React基础
('550e8400-e29b-41d4-a716-44665544000c', '550e8400-e29b-41d4-a716-44665544000d'), -- React基础 -> React Hooks
('550e8400-e29b-41d4-a716-44665544000d', '550e8400-e29b-41d4-a716-44665544000e'); -- React Hooks -> 状态管理

-- Insert sample user progress
INSERT INTO user_progress (user_id, knowledge_point_id, mastery_level, notes, last_reviewed) VALUES
('00000000-0000-0000-0000-000000000000', '550e8400-e29b-41d4-a716-446655440001', 95, '基础算术已经完全掌握，可以快速进行四则运算', NOW() - INTERVAL '2 days'),
('00000000-0000-0000-0000-000000000000', '550e8400-e29b-41d4-a716-446655440002', 85, '分数运算基本掌握，偶尔在复杂分数上有困难', NOW() - INTERVAL '1 day'),
('00000000-0000-0000-0000-000000000000', '550e8400-e29b-41d4-a716-446655440003', 70, '代数基础理解，但需要更多练习', NOW() - INTERVAL '3 hours'),
('00000000-0000-0000-0000-000000000000', '550e8400-e29b-41d4-a716-446655440009', 90, '编程基础概念清晰，理解变量和控制结构', NOW() - INTERVAL '1 week'),
('00000000-0000-0000-0000-000000000000', '550e8400-e29b-41d4-a716-44665544000a', 75, 'JavaScript语法基本掌握，需要加强异步编程理解', NOW() - INTERVAL '2 days');

-- Insert sample test questions
INSERT INTO test_questions (knowledge_point_id, question, question_type, options, correct_answer, difficulty, created_by) VALUES
('550e8400-e29b-41d4-a716-446655440001', '计算：25 + 37 = ?', 'multiple_choice', ARRAY['52', '62', '72', '82'], '62', 1, '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-446655440001', '计算：144 ÷ 12 = ?', 'multiple_choice', ARRAY['10', '11', '12', '13'], '12', 1, '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-446655440002', '计算：2/3 + 1/4 = ?', 'multiple_choice', ARRAY['3/7', '11/12', '3/12', '5/6'], '11/12', 2, '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-446655440003', '如果 x + 5 = 12，那么 x = ?', 'short_answer', NULL, '7', 3, '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-44665544000a', '在JavaScript中，声明变量使用哪个关键字？', 'multiple_choice', ARRAY['var', 'let', 'const', '以上都可以'], '以上都可以', 2, '00000000-0000-0000-0000-000000000000'),
('550e8400-e29b-41d4-a716-44665544000a', '解释JavaScript中的闭包概念', 'essay', NULL, '闭包是指函数能够访问其外部作用域中的变量，即使在外部函数已经返回之后', 4, '00000000-0000-0000-0000-000000000000');

-- Insert sample learning sessions
INSERT INTO learning_sessions (user_id, knowledge_point_id, start_time, end_time, notes, test_score, questions_answered, time_spent_minutes) VALUES
('00000000-0000-0000-0000-000000000000', '550e8400-e29b-41d4-a716-446655440001', NOW() - INTERVAL '3 days', NOW() - INTERVAL '3 days' + INTERVAL '45 minutes', '复习了基础算术，做了20道练习题', 95, 20, 45),
('00000000-0000-0000-0000-000000000000', '550e8400-e29b-41d4-a716-446655440002', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days' + INTERVAL '60 minutes', '学习分数运算，重点练习了分数加减法', 85, 15, 60),
('00000000-0000-0000-0000-000000000000', '550e8400-e29b-41d4-a716-446655440003', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day' + INTERVAL '90 minutes', '初次学习代数基础，理解了变量概念', 70, 12, 90);
