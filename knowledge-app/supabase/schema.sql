-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create knowledge_points table
CREATE TABLE knowledge_points (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level >= 1 AND difficulty_level <= 10),
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

-- Create knowledge_dependencies table (for DAG structure)
CREATE TABLE knowledge_dependencies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    prerequisite_id UUID REFERENCES knowledge_points(id) ON DELETE CASCADE NOT NULL,
    dependent_id UUID REFERENCES knowledge_points(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(prerequisite_id, dependent_id),
    CHECK (prerequisite_id != dependent_id)
);

-- Create user_progress table
CREATE TABLE user_progress (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    knowledge_point_id UUID REFERENCES knowledge_points(id) ON DELETE CASCADE NOT NULL,
    mastery_level INTEGER DEFAULT 0 CHECK (mastery_level >= 0 AND mastery_level <= 100),
    notes TEXT,
    last_reviewed TIMESTAMP WITH TIME ZONE,
    test_scores INTEGER[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, knowledge_point_id)
);

-- Create learning_sessions table
CREATE TABLE learning_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    knowledge_point_id UUID REFERENCES knowledge_points(id) ON DELETE CASCADE NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    test_score INTEGER CHECK (test_score >= 0 AND test_score <= 100),
    questions_answered INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create test_questions table
CREATE TABLE test_questions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    knowledge_point_id UUID REFERENCES knowledge_points(id) ON DELETE CASCADE NOT NULL,
    question TEXT NOT NULL,
    question_type VARCHAR(50) DEFAULT 'multiple_choice' CHECK (question_type IN ('multiple_choice', 'short_answer', 'essay')),
    options TEXT[], -- For multiple choice questions
    correct_answer TEXT,
    difficulty INTEGER DEFAULT 1 CHECK (difficulty >= 1 AND difficulty <= 10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX idx_knowledge_points_user_id ON knowledge_points(user_id);
CREATE INDEX idx_knowledge_points_tags ON knowledge_points USING GIN(tags);
CREATE INDEX idx_knowledge_dependencies_prerequisite ON knowledge_dependencies(prerequisite_id);
CREATE INDEX idx_knowledge_dependencies_dependent ON knowledge_dependencies(dependent_id);
CREATE INDEX idx_user_progress_user_id ON user_progress(user_id);
CREATE INDEX idx_user_progress_knowledge_point_id ON user_progress(knowledge_point_id);
CREATE INDEX idx_learning_sessions_user_id ON learning_sessions(user_id);
CREATE INDEX idx_learning_sessions_knowledge_point_id ON learning_sessions(knowledge_point_id);
CREATE INDEX idx_test_questions_knowledge_point_id ON test_questions(knowledge_point_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_knowledge_points_updated_at BEFORE UPDATE ON knowledge_points FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_progress_updated_at BEFORE UPDATE ON user_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE knowledge_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_questions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Knowledge points: users can only see and modify their own
CREATE POLICY "Users can view their own knowledge points" ON knowledge_points FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own knowledge points" ON knowledge_points FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own knowledge points" ON knowledge_points FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own knowledge points" ON knowledge_points FOR DELETE USING (auth.uid() = user_id);

-- Knowledge dependencies: users can only see dependencies for their own knowledge points
CREATE POLICY "Users can view dependencies for their knowledge points" ON knowledge_dependencies FOR SELECT 
USING (
    EXISTS (SELECT 1 FROM knowledge_points WHERE id = prerequisite_id AND user_id = auth.uid()) OR
    EXISTS (SELECT 1 FROM knowledge_points WHERE id = dependent_id AND user_id = auth.uid())
);
CREATE POLICY "Users can insert dependencies for their knowledge points" ON knowledge_dependencies FOR INSERT 
WITH CHECK (
    EXISTS (SELECT 1 FROM knowledge_points WHERE id = prerequisite_id AND user_id = auth.uid()) AND
    EXISTS (SELECT 1 FROM knowledge_points WHERE id = dependent_id AND user_id = auth.uid())
);
CREATE POLICY "Users can delete dependencies for their knowledge points" ON knowledge_dependencies FOR DELETE 
USING (
    EXISTS (SELECT 1 FROM knowledge_points WHERE id = prerequisite_id AND user_id = auth.uid()) OR
    EXISTS (SELECT 1 FROM knowledge_points WHERE id = dependent_id AND user_id = auth.uid())
);

-- User progress: users can only see and modify their own progress
CREATE POLICY "Users can view their own progress" ON user_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own progress" ON user_progress FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own progress" ON user_progress FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own progress" ON user_progress FOR DELETE USING (auth.uid() = user_id);

-- Learning sessions: users can only see and modify their own sessions
CREATE POLICY "Users can view their own learning sessions" ON learning_sessions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own learning sessions" ON learning_sessions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own learning sessions" ON learning_sessions FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own learning sessions" ON learning_sessions FOR DELETE USING (auth.uid() = user_id);

-- Test questions: users can see questions for their knowledge points and create questions
CREATE POLICY "Users can view questions for their knowledge points" ON test_questions FOR SELECT 
USING (EXISTS (SELECT 1 FROM knowledge_points WHERE id = knowledge_point_id AND user_id = auth.uid()));
CREATE POLICY "Users can insert questions for their knowledge points" ON test_questions FOR INSERT 
WITH CHECK (EXISTS (SELECT 1 FROM knowledge_points WHERE id = knowledge_point_id AND user_id = auth.uid()));
CREATE POLICY "Users can update their own questions" ON test_questions FOR UPDATE USING (auth.uid() = created_by);
CREATE POLICY "Users can delete their own questions" ON test_questions FOR DELETE USING (auth.uid() = created_by);
