{"name": "d3", "version": "7.9.0", "description": "Data-Driven Documents", "homepage": "https://d3js.org", "repository": {"type": "git", "url": "https://github.com/d3/d3.git"}, "keywords": ["d3", "dom", "visualization", "svg", "animation", "canvas"], "license": "ISC", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "type": "module", "files": ["dist/d3.js", "dist/d3.min.js", "src/**/*.js"], "module": "src/index.js", "main": "src/index.js", "jsdelivr": "dist/d3.min.js", "unpkg": "dist/d3.min.js", "exports": {"umd": "./dist/d3.min.js", "default": "./src/index.js"}, "dependencies": {"d3-array": "3", "d3-axis": "3", "d3-brush": "3", "d3-chord": "3", "d3-color": "3", "d3-contour": "4", "d3-delaunay": "6", "d3-dispatch": "3", "d3-drag": "3", "d3-dsv": "3", "d3-ease": "3", "d3-fetch": "3", "d3-force": "3", "d3-format": "3", "d3-geo": "3", "d3-hierarchy": "3", "d3-interpolate": "3", "d3-path": "3", "d3-polygon": "3", "d3-quadtree": "3", "d3-random": "3", "d3-scale": "4", "d3-scale-chromatic": "3", "d3-selection": "3", "d3-shape": "3", "d3-time": "3", "d3-time-format": "4", "d3-timer": "3", "d3-transition": "3", "d3-zoom": "3"}, "devDependencies": {"@observablehq/plot": "^0.6.7", "@observablehq/runtime": "^5.7.3", "@rollup/plugin-json": "6", "@rollup/plugin-node-resolve": "15", "@rollup/plugin-terser": "^0.4.0", "eslint": "8", "mocha": "10", "rollup": "3", "topojson-client": "^3.1.0", "vitepress": "^1.0.0-beta.2"}, "scripts": {"test": "mocha 'test/**/*-test.js' && eslint src test", "prepublishOnly": "rm -rf dist && rollup -c", "postpublish": "git push && git push --tags", "docs:dev": "node --experimental-network-imports node_modules/vitepress/dist/node/cli.js dev docs", "docs:build": "./prebuild.sh && node --experimental-network-imports node_modules/vitepress/dist/node/cli.js build docs", "docs:preview": "vitepress preview docs"}, "engines": {"node": ">=12"}}