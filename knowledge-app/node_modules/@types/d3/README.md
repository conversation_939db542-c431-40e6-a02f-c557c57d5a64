# Installation
> `npm install --save @types/d3`

# Summary
This package contains type definitions for d3 (https://github.com/d3/d3).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3/index.d.ts)
````ts
// Last module patch version validated against: 7.4.4

export as namespace d3;

/**
 * Version number in format _Major.Minor.BugFix_, like 7.0.0.
 */
export const version: string;

export * from "d3-array";
export * from "d3-axis";
export * from "d3-brush";
export * from "d3-chord";
export * from "d3-color";
export * from "d3-contour";
export * from "d3-delaunay";
export * from "d3-dispatch";
export * from "d3-drag";
export * from "d3-dsv";
export * from "d3-ease";
export * from "d3-fetch";
export * from "d3-force";
export * from "d3-format";
export * from "d3-geo";
export * from "d3-hierarchy";
export * from "d3-interpolate";
export * from "d3-path";
export * from "d3-polygon";
export * from "d3-quadtree";
export * from "d3-random";
export * from "d3-scale";
export * from "d3-scale-chromatic";
export * from "d3-selection";
export * from "d3-shape";
export * from "d3-time";
export * from "d3-time-format";
export * from "d3-timer";
export * from "d3-transition";
export * from "d3-zoom";

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 15:11:36 GMT
 * Dependencies: [@types/d3-array](https://npmjs.com/package/@types/d3-array), [@types/d3-axis](https://npmjs.com/package/@types/d3-axis), [@types/d3-brush](https://npmjs.com/package/@types/d3-brush), [@types/d3-chord](https://npmjs.com/package/@types/d3-chord), [@types/d3-color](https://npmjs.com/package/@types/d3-color), [@types/d3-contour](https://npmjs.com/package/@types/d3-contour), [@types/d3-delaunay](https://npmjs.com/package/@types/d3-delaunay), [@types/d3-dispatch](https://npmjs.com/package/@types/d3-dispatch), [@types/d3-drag](https://npmjs.com/package/@types/d3-drag), [@types/d3-dsv](https://npmjs.com/package/@types/d3-dsv), [@types/d3-ease](https://npmjs.com/package/@types/d3-ease), [@types/d3-fetch](https://npmjs.com/package/@types/d3-fetch), [@types/d3-force](https://npmjs.com/package/@types/d3-force), [@types/d3-format](https://npmjs.com/package/@types/d3-format), [@types/d3-geo](https://npmjs.com/package/@types/d3-geo), [@types/d3-hierarchy](https://npmjs.com/package/@types/d3-hierarchy), [@types/d3-interpolate](https://npmjs.com/package/@types/d3-interpolate), [@types/d3-path](https://npmjs.com/package/@types/d3-path), [@types/d3-polygon](https://npmjs.com/package/@types/d3-polygon), [@types/d3-quadtree](https://npmjs.com/package/@types/d3-quadtree), [@types/d3-random](https://npmjs.com/package/@types/d3-random), [@types/d3-scale](https://npmjs.com/package/@types/d3-scale), [@types/d3-scale-chromatic](https://npmjs.com/package/@types/d3-scale-chromatic), [@types/d3-selection](https://npmjs.com/package/@types/d3-selection), [@types/d3-shape](https://npmjs.com/package/@types/d3-shape), [@types/d3-time](https://npmjs.com/package/@types/d3-time), [@types/d3-time-format](https://npmjs.com/package/@types/d3-time-format), [@types/d3-timer](https://npmjs.com/package/@types/d3-timer), [@types/d3-transition](https://npmjs.com/package/@types/d3-transition), [@types/d3-zoom](https://npmjs.com/package/@types/d3-zoom)

# Credits
These definitions were written by [Tom Wanzek](https://github.com/tomwanzek), [Alex Ford](https://github.com/gustavderdrache), [Boris Yankov](https://github.com/borisyankov), [denisname](https://github.com/denisname), [Nathan Bierema](https://github.com/Methuselah96), and [Fil](https://github.com/Fil).
