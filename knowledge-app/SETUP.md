# Learn Everything - 设置指南

## 项目概述

Learn Everything 是一个个人知识管理系统，帮助用户构建知识图谱、跟踪学习进度、获得智能推荐，并通过严格验证确保知识掌握。

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **状态管理**: Zustand
- **数据库**: Supabase (PostgreSQL)
- **认证**: Supabase Auth
- **图可视化**: D3.js, React Force Graph
- **UI组件**: Lucide React Icons

## 环境设置

### 1. 克隆项目并安装依赖

```bash
cd knowledge-app
npm install
```

### 2. 设置Supabase

1. 访问 [Supabase](https://supabase.com) 并创建新项目
2. 在项目设置中找到API配置信息
3. 复制 `.env.local.example` 为 `.env.local`
4. 填入你的Supabase配置：

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. 设置数据库

1. 在Supabase项目中，进入SQL编辑器
2. 运行 `supabase/schema.sql` 中的SQL脚本来创建表结构
3. （可选）运行 `supabase/seed.sql` 来插入示例数据

### 4. 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:3000 查看应用

## 数据库结构

### 核心表

1. **knowledge_points** - 知识点
   - 存储知识点的基本信息（名称、描述、难度、标签等）
   
2. **knowledge_dependencies** - 知识依赖关系
   - 构建有向无环图(DAG)结构的知识依赖关系
   
3. **user_progress** - 用户学习进度
   - 跟踪用户对每个知识点的掌握程度
   
4. **learning_sessions** - 学习会话
   - 记录用户的学习活动和时间
   
5. **test_questions** - 测试题目
   - 存储用于验证知识掌握的题目

### 安全性

- 启用了行级安全(RLS)
- 用户只能访问自己的数据
- 所有表都有适当的RLS策略

## 功能特性

### 已实现
- ✅ 项目初始化和基础架构
- ✅ 数据模型设计
- ✅ Supabase数据库配置
- ✅ 基础UI界面

### 计划中
- 🔄 知识图谱核心功能
- 🔄 图谱可视化界面
- 🔄 用户认证系统
- 🔄 学习进度跟踪
- 🔄 笔记系统
- 🔄 推荐算法
- 🔄 验证和测试系统

## 开发指南

### 项目结构

```
knowledge-app/
├── src/
│   ├── app/                 # Next.js App Router
│   ├── components/          # React组件
│   ├── lib/                 # 工具库和配置
│   ├── store/               # Zustand状态管理
│   └── types/               # TypeScript类型定义
├── supabase/                # 数据库脚本
│   ├── schema.sql           # 数据库结构
│   └── seed.sql             # 示例数据
└── public/                  # 静态资源
```

### 状态管理

使用Zustand进行状态管理，主要store包括：
- `useKnowledgeStore` - 知识点和依赖关系管理
- 计划添加用户认证、学习进度等store

### API设计

数据库操作通过 `src/lib/database.ts` 中的API函数进行：
- `knowledgePointsAPI` - 知识点CRUD操作
- `dependenciesAPI` - 依赖关系管理
- `progressAPI` - 学习进度跟踪
- `sessionsAPI` - 学习会话管理
- `questionsAPI` - 测试题目管理

## 贡献指南

1. 遵循现有的代码风格和结构
2. 确保所有新功能都有适当的TypeScript类型
3. 数据库更改需要更新schema.sql
4. 重要功能需要添加测试

## 故障排除

### 常见问题

1. **Supabase连接失败**
   - 检查 `.env.local` 文件中的配置
   - 确保Supabase项目处于活跃状态

2. **数据库权限错误**
   - 确保已正确设置RLS策略
   - 检查用户是否已认证

3. **依赖安装问题**
   - 删除 `node_modules` 和 `package-lock.json`
   - 重新运行 `npm install`

## 下一步计划

1. 实现知识图谱的CRUD操作
2. 添加图谱可视化组件
3. 集成用户认证系统
4. 开发学习推荐算法
5. 实现笔记和测试功能
